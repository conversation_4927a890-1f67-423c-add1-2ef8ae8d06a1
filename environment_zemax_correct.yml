# 25AutoAssembly - Zemax集成版环境配置
# 专为Zemax OpticStudio ZOS-API集成设计
# Python 3.6.x版本 - Zemax官方支持版本

name: pyzemax-25autoassembly
channels:
  - conda-forge
  - defaults

dependencies:
  # ==================== Python核心 ====================
  # 必须使用Python 3.6.x版本以支持Zemax ZOS-API
  - python=3.6.15
  
  # ==================== GUI框架 ====================
  # PyQt5 - 图形用户界面
  - pyqt=5.12.3
  - qt=5.12.9
  
  # ==================== 数值计算核心 ====================
  # NumPy - 数值计算基础库
  - numpy=1.19.5
  
  # SciPy - 科学计算库
  #- scipy=1.5.4
  
  # ==================== 数据可视化 ====================
  # Matplotlib - 光学数据绘图
  - matplotlib=3.3.4
  - matplotlib-base=3.3.4
  
  # ==================== 数据处理 ====================
  # Pandas - 数据分析
  - pandas=1.1.5
  
  # ==================== 数学优化库 ====================
  # Intel MKL - 数学核心库优化
  - mkl=2021.4.0
  - mkl-service=2.4.0
  - intel-openmp=2021.4.0
  - blas=1.0=mkl
  - liblapack=3.9.0
  
  # ==================== 系统工具 ====================
  # 包管理工具
  - pip=21.3.1
  - setuptools=59.6.0
  - wheel=0.37.1
  
  # 系统库
  - certifi=2021.5.30
  - wincertstore=0.2
  
  # ==================== 图像处理 ====================
  - pillow=8.4.0
  - jpeg=9e
  - libpng=1.6.43
  - libtiff=4.3.0
  
  # ==================== 网络和通信 ====================
  - openssl=1.1.1w
  - urllib3=1.26.18
  - requests=2.28.2
  
  # ==================== 通过pip安装的包 ====================
  - pip:
    # .NET集成 - Zemax ZOS-API必需
    - pythonnet==2.5.2
    
    # 文件系统监控
    - watchdog==2.3.1
    
    # 网络接口获取
    - netifaces==0.11.0
    
    # 图像处理增强
    - imageio==2.15.0
    
    # Excel文件处理
    - openpyxl==3.1.3
    - xlrd==2.0.2
    - xlsxwriter==3.2.2
    
    # 进度条显示
    - progress==1.6.1
    
    # 日期时间处理
    - python-dateutil==2.9.0.post0
    - pytz==2025.2
    
    # 数据类支持
    - dataclasses==0.8
    
    # 颜色输出
    - colored==1.4.4
    
    # 应用目录管理
    - appdirs==1.4.4
    
    # 表格显示
    - ansitable==0.9.7
    
    # XML处理
    - et-xmlfile==1.1.0
    
    # 元数据处理
    - importlib-metadata==4.8.3
    
    # 包装工具
    - packaging==21.3
    
    # 六兼容库
    - six==1.17.0
