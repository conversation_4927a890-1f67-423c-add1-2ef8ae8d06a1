#!/usr/bin/env python3
"""
VRML测试程序
使用VTK读取和显示VRML文件的独立测试程序

用法：
python vrml_test.py

然后输入VRML文件路径进行测试
"""

import os
import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'src'))

# 检查VTK可用性
try:
    import vtk
    print(f"✅ VTK版本: {vtk.vtkVersion.GetVTKVersion()}")
    VTK_AVAILABLE = True
except ImportError as e:
    print(f"❌ VTK导入失败: {e}")
    VTK_AVAILABLE = False
    sys.exit(1)


class VRMLTester:
    """VRML文件测试器"""
    
    def __init__(self):
        """初始化VRML测试器"""
        self.render_window = None
        self.renderer = None
        self.interactor = None
        
        logger.info("VRML测试器初始化完成")
    
    def test_vrml_file(self, vrml_file_path: str) -> bool:
        """
        测试VRML文件加载和显示
        
        Args:
            vrml_file_path (str): VRML文件路径
            
        Returns:
            bool: 成功返回True，失败返回False
        """
        try:
            # 验证文件
            if not self._validate_file(vrml_file_path):
                return False
            
            print(f"\n🔍 开始测试VRML文件: {vrml_file_path}")
            
            # 方法1：尝试使用vtkVRMLImporter
            success = self._test_with_vrml_importer(vrml_file_path)
            if success:
                print("✅ vtkVRMLImporter方法成功")
                return True
            
            # 方法2：尝试使用通用导入器
            print("⚠️ vtkVRMLImporter失败，尝试其他方法...")
            success = self._test_with_generic_importer(vrml_file_path)
            if success:
                print("✅ 通用导入器方法成功")
                return True
            
            print("❌ 所有方法都失败了")
            return False
            
        except Exception as e:
            logger.error(f"测试VRML文件失败: {e}")
            return False
    
    def _validate_file(self, file_path: str) -> bool:
        """验证文件"""
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                print(f"❌ 文件不存在: {file_path}")
                return False
            
            # 检查文件扩展名
            if not file_path.lower().endswith('.wrl'):
                print(f"⚠️ 警告: 文件扩展名不是.wrl: {file_path}")
            
            # 检查文件大小
            file_size = os.path.getsize(file_path)
            print(f"📁 文件大小: {file_size} 字节")
            
            # 尝试读取文件头部
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    first_line = f.readline().strip()
                    print(f"📄 文件头部: {first_line}")
                    
                    # 检查VRML格式
                    if 'VRML' in first_line.upper():
                        print("✅ 检测到VRML格式标识")
                    else:
                        print("⚠️ 未检测到VRML格式标识")
            except Exception as e:
                print(f"⚠️ 读取文件头部失败: {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ 文件验证失败: {e}")
            return False
    
    def _test_with_vrml_importer(self, vrml_file_path: str) -> bool:
        """使用vtkVRMLImporter测试"""
        try:
            print("\n🔧 方法1: 使用vtkVRMLImporter")
            
            # 创建渲染环境
            self.renderer = vtk.vtkRenderer()
            self.render_window = vtk.vtkRenderWindow()
            self.render_window.AddRenderer(self.renderer)
            self.render_window.SetSize(800, 600)
            self.render_window.SetWindowName("VRML测试 - vtkVRMLImporter")
            
            # 创建VRML导入器
            importer = vtk.vtkVRMLImporter()
            importer.SetFileName(vrml_file_path)
            importer.SetRenderWindow(self.render_window)
            
            print("📥 开始导入VRML文件...")
            importer.Update()
            
            # 检查导入结果
            actors = self.renderer.GetActors()
            actor_count = actors.GetNumberOfItems()
            print(f"🎭 导入的Actor数量: {actor_count}")
            
            if actor_count == 0:
                print("❌ 没有导入任何Actor")
                return False
            
            # 设置渲染器背景
            self.renderer.SetBackground(0.2, 0.3, 0.4)
            
            # 重置相机
            self.renderer.ResetCamera()
            
            # 创建交互器
            self.interactor = vtk.vtkRenderWindowInteractor()
            self.interactor.SetRenderWindow(self.render_window)
            
            # 设置交互样式
            style = vtk.vtkInteractorStyleTrackballCamera()
            self.interactor.SetInteractorStyle(style)
            
            print("🎨 开始渲染...")
            self.render_window.Render()
            
            print("🖱️ 交互控制:")
            print("   - 左键拖拽: 旋转")
            print("   - 右键拖拽: 缩放")
            print("   - 中键拖拽: 平移")
            print("   - 按 'q' 或关闭窗口退出")
            
            # 开始交互
            self.interactor.Start()
            
            return True
            
        except Exception as e:
            print(f"❌ vtkVRMLImporter方法失败: {e}")
            return False
    
    def _test_with_generic_importer(self, vrml_file_path: str) -> bool:
        """使用自定义VRML 1.0解析器测试"""
        try:
            print("\n🔧 方法2: 使用自定义VRML 1.0解析器")

            # 导入自定义VRML解析器
            from assembly_simulation.vrml_processor import VRML10Parser

            # 创建解析器
            parser = VRML10Parser()

            # 解析文件
            print("📖 开始解析VRML文件...")
            actors = parser.parse_file(vrml_file_path)

            print(f"✅ 解析完成，创建了 {len(actors)} 个Actor")

            if actors:
                # 创建渲染器
                renderer = vtk.vtkRenderer()
                renderer.SetBackground(0.1, 0.1, 0.1)  # 深灰色背景

                # 添加Actor到渲染器
                for actor in actors:
                    renderer.AddActor(actor)

                # 添加光源
                light = vtk.vtkLight()
                light.SetPosition(10, 10, 10)
                light.SetIntensity(1.0)
                renderer.AddLight(light)

                # 重置相机
                renderer.ResetCamera()

                # 创建渲染窗口
                render_window = vtk.vtkRenderWindow()
                render_window.AddRenderer(renderer)
                render_window.SetSize(800, 600)
                render_window.SetWindowName("VRML 1.0 解析器测试")

                # 创建交互器
                interactor = vtk.vtkRenderWindowInteractor()
                interactor.SetRenderWindow(render_window)

                # 设置交互样式
                style = vtk.vtkInteractorStyleTrackballCamera()
                interactor.SetInteractorStyle(style)

                # 开始渲染
                render_window.Render()
                print("🎨 渲染窗口已创建，可以旋转、缩放查看模型")
                print("💡 按 'q' 键退出渲染窗口")
                interactor.Start()

                return True
            else:
                print("⚠️ 没有创建任何Actor")
                return False

        except Exception as e:
            print(f"❌ 自定义VRML解析器失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.interactor:
                self.interactor.TerminateApp()
            if self.render_window:
                self.render_window.Finalize()
        except:
            pass


def main():
    """主函数"""
    print("=" * 60)
    print("🎯 VRML文件测试程序")
    print("=" * 60)
    
    if not VTK_AVAILABLE:
        print("❌ VTK不可用，无法进行测试")
        return
    
    tester = VRMLTester()
    
    try:
        while True:
            print("\n" + "-" * 40)
            file_path = input("请输入VRML文件路径 (或输入 'quit' 退出): ").strip()
            
            if file_path.lower() in ['quit', 'exit', 'q']:
                print("👋 退出程序")
                break
            
            if not file_path:
                print("⚠️ 请输入有效的文件路径")
                continue
            
            # 处理相对路径
            if not os.path.isabs(file_path):
                # 相对于当前脚本目录
                script_dir = Path(__file__).parent
                file_path = str(script_dir / file_path)
            
            # 测试文件
            success = tester.test_vrml_file(file_path)
            
            if success:
                print("✅ VRML文件测试成功")
            else:
                print("❌ VRML文件测试失败")
            
            print("\n" + "=" * 40)
    
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出程序")
    
    except Exception as e:
        print(f"❌ 程序异常: {e}")
    
    finally:
        tester.cleanup()


if __name__ == "__main__":
    main()
