"""
Zemax连接工作线程

提供在独立线程中执行Zemax连接的功能，避免阻塞UI线程。
基于OpticalRealSim-main的PythonStandaloneApplication实现异步封装。

主要类：
- ZemaxWorkerThread: 执行Zemax连接的工作线程
- ZemaxApplication: 封装的Zemax应用程序对象

作者: AI Assistant
版本: 1.0.0
"""

import os
import sys
import time
import logging
import winreg  # {{ AURA: Add - 添加 Windows 注册表支持，用于查找 Zemax 安装路径 }}
from PyQt5.QtCore import QThread, pyqtSignal

from .exceptions import (
    ZemaxLicenseException,
    ZemaxConnectionException,
    ZemaxInitializationException,
    ZemaxSystemNotPresentException,
    get_user_friendly_message
)

# 配置日志
logger = logging.getLogger(__name__)


class ZemaxApplication:
    """
    封装的Zemax应用程序对象
    
    基于OpticalRealSim-main的PythonStandaloneApplication实现，
    提供Zemax ZOS-API的完整功能封装。
    """
    
    def __init__(self, zos_api, the_application, the_system, the_connection):
        """
        初始化Zemax应用程序对象
        
        Args:
            zos_api: ZOSAPI对象
            the_application: Zemax应用程序对象
            the_system: Zemax光学系统对象
            the_connection: Zemax连接对象
        """
        self.ZOSAPI = zos_api
        self.TheApplication = the_application
        self.TheSystem = the_system
        self.TheConnection = the_connection
    
    def open_file(self, filepath, save_if_needed=False):
        """
        打开Zemax文件
        
        Args:
            filepath: 文件路径
            save_if_needed: 是否在需要时保存当前文件
        """
        if self.TheSystem is None:
            raise ZemaxSystemNotPresentException("无法获取Zemax光学系统")
        
        self.TheSystem.LoadFile(filepath, save_if_needed)
        logger.info(f"已打开Zemax文件: {filepath}")
    
    def close_file(self, save=False):
        """
        关闭当前文件
        
        Args:
            save: 是否保存文件
        """
        if self.TheSystem is None:
            raise ZemaxSystemNotPresentException("无法获取Zemax光学系统")
        
        self.TheSystem.Close(save)
        logger.info("已关闭Zemax文件")
    
    def cleanup(self):
        """清理资源"""
        try:
            if self.TheApplication is not None:
                self.TheApplication.CloseApplication()
                logger.info("已关闭Zemax应用程序")
        except Exception as e:
            logger.warning(f"清理Zemax应用程序时出错: {e}")
        
        self.TheApplication = None
        self.TheConnection = None
        self.TheSystem = None
        self.ZOSAPI = None


class ZemaxWorkerThread(QThread):
    """
    Zemax连接工作线程
    
    在独立线程中执行Zemax连接逻辑，通过信号与UI线程通信。
    基于OpticalRealSim-main的连接逻辑实现。
    """
    
    # 信号定义
    progress = pyqtSignal(str)              # 进度信息
    success = pyqtSignal(object)            # 连接成功，传递ZemaxApplication对象
    error = pyqtSignal(str)                 # 连接失败，传递错误信息
    
    def __init__(self, zemax_path=None):
        """
        初始化工作线程
        
        Args:
            zemax_path: Zemax安装路径，None表示自动检测
        """
        super().__init__()
        self.zemax_path = zemax_path
        self.zemax_app = None
    
    def run(self):
        """执行连接逻辑"""
        try:
            self._connect_to_zemax()
        except Exception as e:
            error_msg = get_user_friendly_message(str(e))
            logger.error(f"Zemax连接失败: {error_msg}")
            self.error.emit(error_msg)
    
    def _connect_to_zemax(self):
        """执行Zemax连接的核心逻辑"""
        # 步骤1: 加载ZOS-API动态库
        self.progress.emit("正在加载ZOS-API动态库...")
        self._load_zos_dlls()
        
        # 步骤2: 初始化ZOS-API
        self.progress.emit("正在初始化ZOS-API...")
        is_initialized = self._initialize_zos_api()
        
        if not is_initialized:
            raise ZemaxInitializationException("ZOS-API初始化失败")
        
        # 步骤3: 加载ZOSAPI模块
        self.progress.emit("正在加载ZOSAPI模块...")
        ZOSAPI = self._import_zosapi()
        
        # 步骤4: 建立连接
        self.progress.emit("正在建立与Zemax的连接...")
        the_connection = self._create_connection(ZOSAPI)
        
        # 步骤5: 创建应用程序实例
        self.progress.emit("正在创建Zemax应用程序实例...")
        the_application = self._create_application(the_connection)
        
        # 步骤6: 验证许可证
        self.progress.emit("正在验证Zemax许可证...")
        self._validate_license(the_application)
        
        # 步骤7: 获取光学系统
        self.progress.emit("正在获取Zemax光学系统...")
        the_system = self._get_optical_system(the_application)
        
        # 步骤8: 创建应用程序对象
        self.progress.emit("连接成功！正在初始化应用程序对象...")
        self.zemax_app = ZemaxApplication(ZOSAPI, the_application, the_system, the_connection)
        
        self.progress.emit("Zemax连接已建立")
        self.success.emit(self.zemax_app)
        
        logger.info("Zemax连接成功建立")
    
    def _load_zos_dlls(self):
        """
        加载ZOS-API动态库

        严格按照参考代码逻辑：
        1. 从注册表获取ZemaxRoot并加载ZOSAPI_NetHelper.dll
        2. 通过ZOSAPI_NetHelper获取实际的ZOS目录
        3. 在实际目录中加载其他DLL文件
        """
        try:
            import clr

            # {{ AURA: Modify - 阶段1：加载ZOSAPI_NetHelper.dll }}
            logger.debug("阶段1：从注册表获取ZemaxRoot并加载ZOSAPI_NetHelper.dll")

            # 从注册表获取ZemaxRoot
            zemax_root = self._get_zemax_root_from_registry()

            # 构建ZOSAPI_NetHelper.dll路径
            nethelper_path = os.path.join(os.sep, zemax_root, r'ZOS-API\Libraries\ZOSAPI_NetHelper.dll')

            if not os.path.exists(nethelper_path):
                raise ZemaxConnectionException(f"ZOSAPI_NetHelper.dll不存在: {nethelper_path}")

            logger.debug(f"正在加载ZOSAPI_NetHelper.dll: {nethelper_path}")
            clr.AddReference(nethelper_path)
            logger.debug("ZOSAPI_NetHelper.dll加载成功")

            # 导入ZOSAPI_NetHelper模块
            import ZOSAPI_NetHelper

            # {{ AURA: Modify - 阶段2：初始化并获取实际的ZOS目录 }}
            logger.debug("阶段2：初始化ZOSAPI并获取实际ZOS目录")

            # 初始化ZOSAPI
            if self.zemax_path is None:
                is_initialized = ZOSAPI_NetHelper.ZOSAPI_Initializer.Initialize()
            else:
                is_initialized = ZOSAPI_NetHelper.ZOSAPI_Initializer.Initialize(self.zemax_path)

            if not is_initialized:
                raise ZemaxInitializationException("Unable to locate Zemax OpticStudio. Try using a hard-coded path.")

            # 获取实际的ZOS目录
            actual_zos_dir = ZOSAPI_NetHelper.ZOSAPI_Initializer.GetZemaxDirectory()
            logger.info(f"通过ZOSAPI_NetHelper获取的实际ZOS目录: {actual_zos_dir}")

            # {{ AURA: Modify - 阶段3：加载其他DLL文件 }}
            logger.debug("阶段3：在实际ZOS目录中加载其他DLL文件")

            # 加载其他必需的DLL文件
            other_dlls = ["ZOSAPI.dll", "ZOSAPI_Interfaces.dll"]

            for dll_file in other_dlls:
                dll_path = os.path.join(os.sep, actual_zos_dir, dll_file)
                if not os.path.exists(dll_path):
                    raise ZemaxConnectionException(f"缺少必需的DLL文件: {dll_file} (路径: {dll_path})")

                logger.debug(f"正在加载DLL: {dll_path}")
                clr.AddReference(dll_path)
                logger.debug(f"已成功加载DLL: {dll_file}")

            logger.info("所有ZOS-API动态库加载完成")

        except ImportError:
            raise ZemaxConnectionException("无法导入pythonnet模块，请确保已正确安装")
        except Exception as e:
            raise ZemaxConnectionException(f"加载ZOS-API动态库失败: {str(e)}")

    def _get_zemax_root_from_registry(self):
        """
        从注册表获取Zemax根目录路径

        Returns:
            str: Zemax根目录路径
        """
        try:
            logger.debug("从注册表获取ZemaxRoot...")

            # 打开注册表键
            registry_key = winreg.OpenKey(
                winreg.ConnectRegistry(None, winreg.HKEY_CURRENT_USER),
                r"Software\Zemax",
                0,
                winreg.KEY_READ
            )

            # 读取ZemaxRoot值
            zemax_root, _ = winreg.QueryValueEx(registry_key, 'ZemaxRoot')
            winreg.CloseKey(registry_key)

            logger.info(f"从注册表获取的ZemaxRoot: {zemax_root}")
            return zemax_root

        except FileNotFoundError:
            raise ZemaxConnectionException("注册表中未找到Zemax安装信息 (HKEY_CURRENT_USER\\Software\\Zemax)")
        except Exception as e:
            raise ZemaxConnectionException(f"从注册表读取Zemax路径失败: {e}")
    
    def _initialize_zos_api(self):
        """
        初始化ZOS-API

        注意：由于初始化逻辑已经在_load_zos_dlls()中完成，
        这个方法现在只是返回True表示已初始化
        """
        # {{ AURA: Modify - 初始化逻辑已在_load_zos_dlls()中完成 }}
        logger.debug("ZOS-API已在DLL加载阶段完成初始化")
        return True
    
    def _import_zosapi(self):
        """导入ZOSAPI模块"""
        try:
            import ZOSAPI
            return ZOSAPI
        except ImportError:
            raise ZemaxConnectionException("无法导入ZOSAPI模块")
    
    def _create_connection(self, ZOSAPI):
        """创建Zemax连接"""
        try:
            the_connection = ZOSAPI.ZOSAPI_Connection()
            if the_connection is None:
                raise ZemaxConnectionException("Unable to initialize .NET connection to ZOSAPI")
            return the_connection
        except Exception as e:
            raise ZemaxConnectionException(f"创建Zemax连接失败: {str(e)}")
    
    def _create_application(self, the_connection):
        """创建Zemax应用程序实例"""
        try:
            the_application = the_connection.CreateNewApplication()
            if the_application is None:
                raise ZemaxInitializationException("Unable to acquire ZOSAPI application")
            return the_application
        except Exception as e:
            raise ZemaxInitializationException(f"创建Zemax应用程序失败: {str(e)}")
    
    def _validate_license(self, the_application):
        """验证Zemax许可证"""
        try:
            if not the_application.IsValidLicenseForAPI:
                raise ZemaxLicenseException("License is not valid for ZOSAPI use")
        except Exception as e:
            raise ZemaxLicenseException(f"许可证验证失败: {str(e)}")
    
    def _get_optical_system(self, the_application):
        """获取Zemax光学系统"""
        try:
            the_system = the_application.PrimarySystem
            if the_system is None:
                raise ZemaxSystemNotPresentException("Unable to acquire Primary system")
            return the_system
        except Exception as e:
            raise ZemaxSystemNotPresentException(f"获取光学系统失败: {str(e)}")
