"""
Spot Diagram分析器

基于OpticalRealSim-main的实现，提供点列图分析功能。
包括RMS/GEO数据计算、光线追踪和散点图绘制。

主要功能：
- 点列图分析和数据提取
- 批量光线追踪
- 散点图绘制和显示
- 分析结果数据管理

作者: AI Assistant
版本: 1.0.0
"""

import logging
import numpy as np
from typing import Optional, Dict, List, Tuple, Any
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QTableWidget, QTableWidgetItem, QMessageBox

# {{ AURA: Fix - 条件导入System模块，避免启动时错误 }}
# System模块只有在Zemax连接后才可用
from PyQt5.QtCore import QObject, pyqtSignal

# {{ AURA: Add - 导入System类型用于光线追踪 }}
try:
    from System import Int32, Double, Enum
except ImportError:
    # 如果System模块不可用，定义占位符
    Int32 = int
    Double = float
    Enum = None

# {{ AURA: Fix - 条件导入matplotlib，避免启动时错误 }}
# 导入绘图组件（条件导入）
try:
    import matplotlib
    matplotlib.use("Qt5Agg")
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import matplotlib.pyplot as plt

    # {{ AURA: Fix - 设置中文字体，参考ref/zpy_wavefront.py }}
    # 设置中文字体，解决中文乱码问题
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为 SimHei
    plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示问题

    MATPLOTLIB_AVAILABLE = True
except ImportError:
    # 如果matplotlib不可用，定义占位符
    FigureCanvas = None
    Figure = None
    MATPLOTLIB_AVAILABLE = False

from .worker_threads import ZemaxApplication
from .exceptions import ZemaxBaseException

# 配置日志
logger = logging.getLogger(__name__)


class SpotPlotWidget(QWidget):
    """
    点列图绘制组件
    
    基于OpticalRealSim的PlotDataWidget实现，专门用于点列图的散点绘制。
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)

        # {{ AURA: Fix - 条件创建matplotlib组件 }}
        if MATPLOTLIB_AVAILABLE and Figure is not None:
            # 创建Figure对象
            self.figure = Figure(figsize=(8, 6), dpi=100)
            self.canvas = FigureCanvas(self.figure)

            # 创建布局
            layout = QVBoxLayout()
            layout.addWidget(self.canvas)
            self.setLayout(layout)

            # 初始化子图
            self.ax = None
        else:
            # matplotlib不可用时的占位符
            from PyQt5.QtWidgets import QLabel
            layout = QVBoxLayout()
            placeholder = QLabel("点列图显示需要matplotlib库支持")
            placeholder.setStyleSheet("color: gray; text-align: center;")
            layout.addWidget(placeholder)
            self.setLayout(layout)

            self.figure = None
            self.canvas = None
            self.ax = None
        
    def init_scatter(self):
        """初始化散点图（严格参考OpticalRealSim的PlotDataWidget实现）"""
        if not MATPLOTLIB_AVAILABLE or self.figure is None:
            return

        self.figure.clear()
        self.ax = self.figure.add_subplot(111, aspect='equal')
        # {{ AURA: Fix - 移除固定坐标轴范围，让matplotlib自动处理初始范围 }}
        # 不设置固定的xlim和ylim，让数据驱动坐标范围
        self.ax.set_xlabel('X (mm)')
        self.ax.set_ylabel('Y (mm)')
        self.ax.set_title('Spot Diagram')
        self.ax.grid(True, alpha=0.3)
        
    def plot_scatter_data(self, x_data: np.ndarray, y_data: np.ndarray, color='b', label=None):
        """
        绘制散点数据（严格参考OpticalRealSim的PlotDataWidget实现）

        Args:
            x_data: X坐标数据
            y_data: Y坐标数据
            color: 散点颜色
            label: 图例标签
        """
        if not MATPLOTLIB_AVAILABLE or self.ax is None:
            if MATPLOTLIB_AVAILABLE:
                self.init_scatter()
            else:
                return

        # {{ AURA: Fix - 参考OpticalRealSim的散点图绘制方式 }}
        # 绘制散点图，使用小点标记
        self.ax.plot(x_data, y_data, '.', color=color, markersize=1, label=label)
        self.canvas.draw()

    def plot_multi_wavelength_data(self, scatter_data: Dict[str, np.ndarray], field_index: int, wavelength_numbers: List[int]):
        """
        绘制多波长点列图数据（参考ref/zpy_raytrace.py的实现）

        Args:
            scatter_data: 散点数据字典，包含x_data和y_data
            field_index: 视场索引（从0开始）
            wavelength_numbers: 波长编号列表
        """
        if not MATPLOTLIB_AVAILABLE or self.ax is None:
            if MATPLOTLIB_AVAILABLE:
                self.init_scatter()
            else:
                return

        # 清空当前图形
        self.clear_plot()

        x_array = scatter_data.get('x_data')
        y_array = scatter_data.get('y_data')

        if x_array is None or y_array is None:
            return

        # {{ AURA: Fix - 参考ref/zpy_raytrace.py的颜色映射方式 }}
        # 创建颜色映射（参考ref/zpy_raytrace.py）
        try:
            import matplotlib.pyplot as plt
            num_wavelengths = len(wavelength_numbers)
            colors = plt.cm.jet(np.linspace(0, 1, num_wavelengths))
        except ImportError:
            # 如果matplotlib不可用，使用默认颜色
            colors = ['b', 'g', 'r', 'c', 'm', 'y', 'k'] * (len(wavelength_numbers) // 7 + 1)
            num_wavelengths = len(wavelength_numbers)

        # {{ AURA: Fix - 添加调试输出，严格参考ref/zpy_raytrace.py的实现 }}
        print(f"[DEBUG] 绘制视场 {field_index + 1} 的点列图数据")
        print(f"[DEBUG] 数据维度: x_array.shape = {x_array.shape}, y_array.shape = {y_array.shape}")

        # 遍历每个波长
        for wave_idx, wave_num in enumerate(wavelength_numbers):
            # 获取光线坐标（严格参考ref/zpy_raytrace.py）
            x = x_array[field_index, wave_idx, :]
            y = y_array[field_index, wave_idx, :]

            # {{ AURA: Fix - 添加数据范围调试输出 }}
            print(f"[DEBUG] 波长 {wave_num}: 原始数据范围 X=[{np.min(x):.6f}, {np.max(x):.6f}], Y=[{np.min(y):.6f}, {np.max(y):.6f}]")
            print(f"[DEBUG] 波长 {wave_num}: 原始数据点数 = {len(x)}")

            # 过滤掉无效点（默认值为0的点）- 严格参考ref/zpy_raytrace.py
            valid_indices = np.where((x != 0) | (y != 0))[0]
            x_valid = x[valid_indices]
            y_valid = y[valid_indices]

            # {{ AURA: Fix - 添加过滤后数据的调试输出 }}
            print(f"[DEBUG] 波长 {wave_num}: 过滤后有效点数 = {len(x_valid)}")
            if len(x_valid) > 0:
                print(f"[DEBUG] 波长 {wave_num}: 有效数据范围 X=[{np.min(x_valid):.6f}, {np.max(x_valid):.6f}], Y=[{np.min(y_valid):.6f}, {np.max(y_valid):.6f}]")

            if len(x_valid) > 0:
                # 绘制散点图
                color = colors[wave_idx]
                label = f"波长 {wave_num}" if num_wavelengths > 1 else None
                self.ax.plot(x_valid, y_valid, '.', color=color, markersize=1, label=label)
                print(f"[DEBUG] 波长 {wave_num}: 已绘制 {len(x_valid)} 个数据点")
            else:
                print(f"[WARNING] 波长 {wave_num}: 没有有效数据点可绘制")

        # 添加图例（如果有多个波长）
        if num_wavelengths > 1:
            self.ax.legend(loc='best', fontsize='small')

        # {{ AURA: Fix - 添加自适应坐标范围调整，参考ref/zpy_raytrace.py }}
        # 设置坐标轴范围（根据数据自动调整）
        self.ax.set_xlim(auto=True)
        self.ax.set_ylim(auto=True)

        # 设置标题
        self.ax.set_title(f'Spot Diagram - Field {field_index + 1}')

        self.canvas.draw()
        
    def clear_plot(self):
        """清空绘图"""
        if self.ax is not None:
            self.ax.clear()
            self.init_scatter()
            self.canvas.draw()


class SpotDiagramAnalyzer(QObject):
    """
    点列图分析器
    
    基于OpticalRealSim-main的实现，提供完整的点列图分析功能。
    包括数据计算、光线追踪和结果显示。
    """
    
    # 信号定义
    analysis_started = pyqtSignal()
    analysis_progress = pyqtSignal(str)
    analysis_completed = pyqtSignal(dict)
    analysis_failed = pyqtSignal(str)
    
    def __init__(self, zemax_app: ZemaxApplication):
        """
        初始化点列图分析器
        
        Args:
            zemax_app: Zemax应用程序对象
        """
        super().__init__()
        
        self.zemax_app = zemax_app
        self.ZOSAPI = zemax_app.ZOSAPI
        self.TheSystem = zemax_app.TheSystem
        
        # 分析结果存储
        self.spot_analysis = None
        self.spot_results = None
        self.analysis_data = {}
        
        # 分析参数（默认值）
        self.ray_density = 6  # 光线密度
        self.wavelength_index = 0  # 波长索引（0表示所有波长）
        self.field_index = 0  # 视场索引（0表示所有视场）
        self.refer_to = 0  # 参考类型
        
        logger.info("SpotDiagramAnalyzer已初始化")
    
    def set_analysis_parameters(self, ray_density: int = 6, wavelength_index: int = 0,
                              field_index: int = 0, refer_to: int = 0):
        """
        设置分析参数

        Args:
            ray_density: 光线密度
            wavelength_index: 波长索引（0表示所有波长，正数表示具体波长）
            field_index: 视场索引（0表示所有视场，正数表示具体视场）
            refer_to: 参考类型
        """
        # {{ AURA: Fix - 添加参数验证，严格参考ref/zpy_raytrace.py }}
        # 验证波长索引参数
        if wavelength_index < 0:
            logger.warning(f"无效的波长索引: {wavelength_index}，使用默认值0（所有波长）")
            wavelength_index = 0

        # 验证视场索引参数
        if field_index < 0:
            logger.warning(f"无效的视场索引: {field_index}，使用默认值0（所有视场）")
            field_index = 0

        # 验证光线密度参数
        if ray_density <= 0:
            logger.warning(f"无效的光线密度: {ray_density}，使用默认值6")
            ray_density = 6

        self.ray_density = ray_density
        self.wavelength_index = wavelength_index
        self.field_index = field_index
        self.refer_to = refer_to

        logger.info(f"分析参数已设置: 光线密度={ray_density}, 波长={wavelength_index}, 视场={field_index}")
    
    def run_analysis(self) -> Dict[str, Any]:
        """
        执行点列图分析

        Returns:
            Dict: 分析结果数据
        """
        try:
            self.analysis_started.emit()
            self.analysis_progress.emit("开始点列图分析...")

            # {{ AURA: Add - 严格参考OpticalRealSim实现光线追踪和点列图分析 }}
            # 第一步：执行光线追踪获取散点数据
            self.analysis_progress.emit("执行光线追踪...")
            scatter_data = self._perform_ray_tracing()

            # 第二步：创建点列图分析获取RMS/GEO数据
            self.analysis_progress.emit("创建点列图分析对象...")
            self.spot_analysis = self.TheSystem.Analyses.New_Analysis(
                self.ZOSAPI.Analysis.AnalysisIDM.StandardSpot
            )

            # 配置分析设置（严格参考OpticalRealSim的设置方式）
            self.analysis_progress.emit("配置分析参数...")
            spot_settings = self.spot_analysis.GetSettings()
            spot_settings.Wavelength.SetWavelengthNumber(self.wavelength_index)
            # {{ AURA: Fix - 使用用户选择的视场索引而不是硬编码0 }}
            # 设置视场：如果field_index为0则计算所有视场，否则计算指定视场
            spot_settings.Field.SetFieldNumber(self.field_index)
            spot_settings.RayDensity = self.ray_density
            spot_settings.Pattern = self.ZOSAPI.Analysis.Settings.Spot.Patterns.Hexapolar
            spot_settings.ReferTo = self.refer_to

            # 执行分析
            self.analysis_progress.emit("执行点列图计算...")
            self.spot_analysis.ApplyAndWaitForCompletion()

            # 获取分析结果
            self.analysis_progress.emit("获取分析结果...")
            self.spot_results = self.spot_analysis.GetResults()
            
            # 提取数据
            self.analysis_progress.emit("提取分析数据...")
            analysis_data = self._extract_analysis_data()
            
            # 执行光线追踪获取散点数据
            self.analysis_progress.emit("执行光线追踪...")
            scatter_data = self._perform_ray_tracing()
            analysis_data['scatter_data'] = scatter_data
            
            self.analysis_data = analysis_data
            self.analysis_completed.emit(analysis_data)
            
            logger.info("点列图分析完成")
            return analysis_data
            
        except Exception as e:
            error_msg = f"点列图分析失败: {str(e)}"
            logger.error(error_msg)
            self.analysis_failed.emit(error_msg)
            raise ZemaxBaseException(error_msg)
    
    def _extract_analysis_data(self) -> Dict[str, Any]:
        """
        提取分析数据
        
        Returns:
            Dict: 包含RMS和GEO数据的字典
        """
        if not self.spot_results:
            raise ZemaxBaseException("无分析结果可提取")
        
        # 获取系统信息
        num_fields = self.TheSystem.SystemData.Fields.NumberOfFields
        num_wavelengths = self.TheSystem.SystemData.Wavelengths.NumberOfWavelengths
        
        # 提取RMS和GEO数据
        rms_data = []
        geo_data = []
        
        for field_idx in range(1, num_fields + 1):
            field_rms = []
            field_geo = []
            
            for wave_idx in range(num_wavelengths):
                rms_value = self.spot_results.SpotData.GetRMSSpotSizeFor(field_idx, wave_idx)
                geo_value = self.spot_results.SpotData.GetGeoSpotSizeFor(field_idx, wave_idx)
                
                field_rms.append(rms_value)
                field_geo.append(geo_value)
            
            rms_data.append(field_rms)
            geo_data.append(field_geo)
        
        return {
            'rms_data': rms_data,
            'geo_data': geo_data,
            'num_fields': num_fields,
            'num_wavelengths': num_wavelengths,
            'analysis_parameters': {
                'ray_density': self.ray_density,
                'wavelength_index': self.wavelength_index,
                'field_index': self.field_index,
                'refer_to': self.refer_to
            }
        }

    def _perform_ray_tracing(self) -> Dict[str, np.ndarray]:
        """
        执行光线追踪获取散点数据

        严格基于ref/zpy_raytrace.py的实现和Zemax官方示例

        Returns:
            Dict: 包含X和Y坐标数据的字典
        """
        try:
            # {{ AURA: Fix - 动态导入System模块，避免启动时错误 }}
            from System import Enum, Int32, Double

            # {{ AURA: Fix - 严格参考ref/zpy_raytrace.py和Zemax官方示例的实现 }}
            # 打开批量光线追踪工具
            raytrace = self.TheSystem.Tools.OpenBatchRayTrace()
            if raytrace is None:
                raise ZemaxBaseException("无法打开批量光线追踪工具，请确保Zemax文件已正确加载")

            nsur = self.TheSystem.LDE.NumberOfSurfaces
            max_rays = self.ray_density
            total_rays = (max_rays + 1) * (max_rays + 1)

            # 创建归一化非偏振光线数据（严格参考官方示例）
            normUnPolData = raytrace.CreateNormUnpol(
                total_rays,
                self.ZOSAPI.Tools.RayTrace.RaysType.Real,
                nsur
            )

            if normUnPolData is None:
                raise ZemaxBaseException("无法创建光线追踪数据对象")

            # {{ AURA: Fix - 严格参考ref/zpy_raytrace.py的系统参数获取方式 }}
            # 获取系统参数
            num_wavelengths = self.TheSystem.SystemData.Wavelengths.NumberOfWavelengths
            num_fields = self.TheSystem.SystemData.Fields.NumberOfFields

            # 计算归一化视场（参考ref/zpy_raytrace.py）
            max_field_x = 0.0
            max_field_y = 0.0
            for i in range(1, num_fields + 1):
                field = self.TheSystem.SystemData.Fields.GetField(i)
                if abs(field.X) > max_field_x:
                    max_field_x = abs(field.X)
                if abs(field.Y) > max_field_y:
                    max_field_y = abs(field.Y)

            # {{ AURA: Fix - 根据用户选择确定要分析的视场和波长 }}
            # 确定要分析的视场（参考ref/zpy_raytrace.py的逻辑）
            if self.field_index == 0:
                field_numbers = list(range(1, num_fields + 1))  # 所有视场
            else:
                field_numbers = [self.field_index]  # 指定视场

            # 确定要分析的波长（参考ref/zpy_raytrace.py的逻辑）
            if self.wavelength_index == 0:
                wavelength_numbers = list(range(1, num_wavelengths + 1))  # 所有波长
            else:
                wavelength_numbers = [self.wavelength_index]  # 指定波长

            # 初始化坐标数组
            x_array = np.zeros((num_fields, num_wavelengths, total_rays))
            y_array = np.zeros((num_fields, num_wavelengths, total_rays))

            # {{ AURA: Fix - 严格参考ref/zpy_raytrace.py的双重循环逻辑 }}
            # 遍历每个视场和波长（参考ref/zpy_raytrace.py）
            for field_idx, field_num in enumerate(field_numbers):
                # 获取视场信息（参考ref/zpy_raytrace.py）
                field = self.TheSystem.SystemData.Fields.GetField(field_num)
                hx = field.X / max_field_x if max_field_x > 0 else 0
                hy = field.Y / max_field_y if max_field_y > 0 else 0

                # {{ AURA: Fix - 添加索引映射调试输出 }}
                actual_field_idx = field_num - 1  # 视场编号转数组索引
                print(f"[DEBUG] 处理视场 {field_num}，循环索引: {field_idx}，数组索引: {actual_field_idx}，归一化坐标: X={hx:.3f}, Y={hy:.3f}")

                for wave_idx, wave_num in enumerate(wavelength_numbers):
                    # {{ AURA: Fix - 添加波长索引映射调试输出 }}
                    actual_wave_idx = wave_num - 1  # 波长编号转数组索引
                    print(f"  [DEBUG] 处理波长 {wave_num}，循环索引: {wave_idx}，数组索引: {actual_wave_idx}")
                    # 清除之前的数据
                    normUnPolData.ClearData()

                    # 添加光线（参考ref/zpy_raytrace.py的随机采样方法）
                    for i in range(1, total_rays + 1):
                        # 生成随机瞳孔坐标
                        px = np.random.random() * 2 - 1
                        py = np.random.random() * 2 - 1

                        # 确保在单位圆内
                        while (px * px + py * py > 1):
                            px = np.random.random() * 2 - 1
                            py = np.random.random() * 2 - 1

                        # 添加光线到批处理（参考ref/zpy_raytrace.py）
                        normUnPolData.AddRay(
                            wave_num,  # 波长编号
                            Double(hx),  # 归一化X视场
                            Double(hy),  # 归一化Y视场
                            Double(px),  # 归一化X瞳孔坐标
                            Double(py),  # 归一化Y瞳孔坐标
                            Enum.Parse(self.ZOSAPI.Tools.RayTrace.OPDMode, "None")  # OPD模式
                        )

                    # 执行光线追踪
                    if not raytrace.RunAndWaitForCompletion():
                        raise ZemaxBaseException("光线追踪运行失败")

                    # 读取结果
                    normUnPolData.StartReadingResults()

                    # {{ AURA: Fix - 严格参考ref/zpy_raytrace.py的结果读取方式 }}
                    # 准备参数（Python.NET需要传递引用）
                    ray_idx = Int32(0)
                    error_code = Int32(0)
                    vignette_code = Int32(0)
                    x = Double(0.0)
                    y = Double(0.0)
                    z = Double(0.0)
                    l = Double(0.0)
                    m = Double(0.0)
                    n = Double(0.0)
                    l2 = Double(0.0)
                    m2 = Double(0.0)
                    n2 = Double(0.0)
                    opd = Double(0.0)
                    intensity = Double(0.0)

                    # 读取结果（参考ref/zpy_raytrace.py）
                    output = normUnPolData.ReadNextResult(
                        ray_idx, error_code, vignette_code,
                        x, y, z, l, m, n, l2, m2, n2, opd, intensity
                    )

                    ray_count = 0
                    while output[0]:  # 成功读取
                        if output[2] == 0 and output[3] == 0:  # 无错误且未渐晕
                            # {{ AURA: Fix - 修正数据存储索引映射，使用视场和波长编号而不是循环索引 }}
                            # 存储光线坐标到正确的数组位置
                            ray_idx_val = output[1] - 1  # 索引从0开始
                            actual_field_idx = field_num - 1  # 视场编号转数组索引
                            actual_wave_idx = wave_num - 1   # 波长编号转数组索引
                            x_array[actual_field_idx, actual_wave_idx, ray_idx_val] = output[4]  # X坐标
                            y_array[actual_field_idx, actual_wave_idx, ray_idx_val] = output[5]  # Y坐标
                            ray_count += 1

                        # 读取下一个结果
                        output = normUnPolData.ReadNextResult(
                            ray_idx, error_code, vignette_code,
                            x, y, z, l, m, n, l2, m2, n2, opd, intensity
                        )

                    print(f"    [DEBUG] 成功追踪 {ray_count} 条光线，存储到数组索引 [{actual_field_idx}, {actual_wave_idx}, :]")

            # 关闭光线追踪工具
            raytrace.Close()

            return {
                'x_data': x_array,
                'y_data': y_array,
                'field_numbers': field_numbers,
                'wavelength_numbers': wavelength_numbers,
                'num_fields': num_fields,
                'num_wavelengths': num_wavelengths
            }

        except Exception as e:
            logger.error(f"光线追踪失败: {e}")
            raise ZemaxBaseException(f"光线追踪失败: {str(e)}")

    def get_analysis_results(self) -> Optional[Dict[str, Any]]:
        """
        获取分析结果

        Returns:
            Dict: 分析结果数据，如果未执行分析则返回None
        """
        return self.analysis_data if hasattr(self, 'analysis_data') else None

    def cleanup(self):
        """清理资源"""
        try:
            if self.spot_analysis:
                self.spot_analysis.Close()
                self.spot_analysis = None

            self.spot_results = None
            self.analysis_data = {}

            logger.info("SpotDiagramAnalyzer资源已清理")

        except Exception as e:
            logger.warning(f"清理SpotDiagramAnalyzer资源时出错: {e}")

    def __del__(self):
        """析构函数"""
        self.cleanup()
