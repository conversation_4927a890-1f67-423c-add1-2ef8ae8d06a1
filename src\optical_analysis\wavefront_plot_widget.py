#!/usr/bin/env python3
"""
25AutoAssembly 波前图绘制组件

基于OpticalRealSim的PlotDataWidget实现，专门用于波前图的显示和绘制。
严格参考OpticalRealSim-main/src/PlotDataWidget.py的实现方式。

主要功能：
- 二维波前图绘制
- 颜色映射和颜色条显示
- 坐标轴设置和标签
- 数据翻转和范围设置

作者: AI Assistant
版本: 1.0.0
"""

import logging
import numpy as np
from typing import Optional

from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt

# {{ AURA: Add - 条件导入matplotlib，避免启动时错误 }}
try:
    import matplotlib
    matplotlib.use("Qt5Agg")
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import matplotlib.pyplot as plt

    # {{ AURA: Fix - 设置中文字体，参考ref/zpy_wavefront.py }}
    # 设置中文字体，解决中文乱码问题
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为 SimHei
    plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示问题

    MATPLOTLIB_AVAILABLE = True
except ImportError:
    # 如果matplotlib不可用，定义占位符
    FigureCanvas = None
    Figure = None
    MATPLOTLIB_AVAILABLE = False

# 配置日志
logger = logging.getLogger(__name__)


class WavefrontPlotWidget(QWidget):
    """
    波前图绘制组件
    
    基于OpticalRealSim的PlotDataWidget实现，专门用于波前图的二维图像显示。
    严格参考OpticalRealSim-main/src/PlotDataWidget.py的plot_grid_image方法。
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # {{ AURA: Add - 条件创建matplotlib组件 }}
        if MATPLOTLIB_AVAILABLE and Figure is not None:
            # {{ AURA: Add - 严格参考OpticalRealSim的Figure设置 }}
            # 创建Figure对象，设置图形大小等属性
            self.figure = Figure(figsize=(8, 6), dpi=100)
            # 创建FigureCanvas对象，将Figure关联到画布上
            self.canvas = FigureCanvas(self.figure)
            
            # 创建布局管理器
            layout = QVBoxLayout()
            # 将画布添加到布局中
            layout.addWidget(self.canvas)
            # 设置窗口的布局
            self.setLayout(layout)
            
            logger.info("WavefrontPlotWidget初始化完成（matplotlib可用）")
        else:
            # matplotlib不可用时的占位符
            layout = QVBoxLayout()
            placeholder = QLabel("波前图显示需要matplotlib库支持\n请安装matplotlib: pip install matplotlib")
            placeholder.setAlignment(Qt.AlignCenter)
            placeholder.setStyleSheet("""
                QLabel {
                    color: #666;
                    font-size: 14px;
                    background-color: #f8f9fa;
                    border: 2px dashed #dee2e6;
                    border-radius: 8px;
                    padding: 20px;
                }
            """)
            layout.addWidget(placeholder)
            self.setLayout(layout)
            
            self.figure = None
            self.canvas = None
            
            logger.warning("WavefrontPlotWidget初始化完成（matplotlib不可用）")
    
    def plot_grid_image(self, np_data: np.ndarray, title: str = "波前图", colormap: str = "jet"):
        """
        绘制二维数据图像（严格参考OpticalRealSim的PlotDataWidget.plot_grid_image实现）
        
        Args:
            np_data: 要显示的图像数据（numpy数组形式）
            title: 图表标题
            colormap: 颜色映射，默认为'jet'
        """
        if not MATPLOTLIB_AVAILABLE or self.figure is None:
            logger.warning("matplotlib不可用，无法绘制波前图")
            return
        
        try:
            # {{ AURA: Add - 严格参考OpticalRealSim的绘制逻辑 }}
            # 清空当前图形和颜色条
            self.figure.clear()
            ax = self.figure.add_subplot(111)  # Create a subplot
            
            # {{ AURA: Add - 使用np.flipud翻转数据（参考OpticalRealSim的处理方式） }}
            # 使用np.flipud翻转数据（如果原始数据需要这样的处理，根据实际情况调整）
            flipped_data = np.flipud(np_data)
            
            # {{ AURA: Add - 显示图像，设置颜色映射和显示范围 }}
            # 显示图像，设置颜色映射为指定colormap，并设置图像显示的范围extent
            im = ax.imshow(flipped_data, cmap=colormap, extent=[-1, 1, -1, 1])
            
            # {{ AURA: Add - 添加颜色条 }}
            # 添加颜色条
            self.figure.colorbar(im, ax=ax, label="波前误差 (波长)")
            
            # {{ AURA: Add - 设置标题和坐标轴标签 }}
            ax.set_title(title, fontsize=14, fontweight='bold')
            ax.set_xlabel("X坐标 (归一化)", fontsize=12)
            ax.set_ylabel("Y坐标 (归一化)", fontsize=12)
            
            # {{ AURA: Add - 设置坐标轴刻度 }}
            ax.set_xticks([-1, -0.5, 0, 0.5, 1])
            ax.set_yticks([-1, -0.5, 0, 0.5, 1])
            
            # {{ AURA: Add - 绘制到画布 }}
            self.canvas.draw()
            
            logger.info(f"波前图绘制完成，数据形状: {np_data.shape}")
            
        except Exception as e:
            logger.error(f"绘制波前图失败: {e}")
            # 显示错误信息
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, f"绘制失败:\n{str(e)}", 
                   horizontalalignment='center', verticalalignment='center',
                   transform=ax.transAxes, fontsize=12, color='red')
            ax.set_title("波前图绘制错误")
            self.canvas.draw()
    
    def clear_plot(self):
        """清空绘图区域"""
        if not MATPLOTLIB_AVAILABLE or self.figure is None:
            return
        
        try:
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, "等待波前数据...", 
                   horizontalalignment='center', verticalalignment='center',
                   transform=ax.transAxes, fontsize=14, color='gray')
            ax.set_title("波前图")
            self.canvas.draw()
            
            logger.info("波前图区域已清空")
            
        except Exception as e:
            logger.error(f"清空波前图失败: {e}")
    
    def show_message(self, message: str, title: str = "波前图"):
        """显示消息文本"""
        if not MATPLOTLIB_AVAILABLE or self.figure is None:
            return
        
        try:
            self.figure.clear()
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, message, 
                   horizontalalignment='center', verticalalignment='center',
                   transform=ax.transAxes, fontsize=12, color='blue')
            ax.set_title(title)
            self.canvas.draw()
            
            logger.info(f"显示消息: {message}")
            
        except Exception as e:
            logger.error(f"显示消息失败: {e}")
