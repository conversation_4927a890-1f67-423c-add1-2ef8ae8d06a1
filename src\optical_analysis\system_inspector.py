#!/usr/bin/env python3
"""
光学系统检查器 - 基于OpticalRealSim项目的系统数据显示功能

主要功能:
1. 从Zemax系统获取光学系统基本参数
2. 从Zemax系统获取镜头/镜面详细数据
3. 提供数据的结构化访问接口
4. 支持数据导出功能

作者: AI Assistant
基于: OpticalRealSim项目的display_system_data和display_lens_data功能
"""

import logging
from typing import List, Dict, Optional, Any
import numpy as np

logger = logging.getLogger(__name__)

class SystemInspector:
    """
    光学系统检查器
    
    基于OpticalRealSim项目的系统数据获取功能，提供从Zemax获取系统和镜头数据的接口
    """
    
    def __init__(self, zemax_app=None):
        """
        初始化系统检查器
        
        Args:
            zemax_app: Zemax应用程序实例
        """
        self.zemax_app = zemax_app
        self.system_data = []
        self.lens_data = []
        self.logger = logging.getLogger(__name__)
        
    def set_zemax_application(self, zemax_app):
        """设置Zemax应用程序实例"""
        self.zemax_app = zemax_app
        self.logger.info("Zemax应用程序实例已设置")
    
    def display_system_data(self) -> List[Dict[str, Any]]:
        """
        获取光学系统数据 - 基于OpticalRealSim的display_system_data方法
        
        Returns:
            List[Dict]: 系统数据列表，每个字典包含参数名和数值
            
        Raises:
            ValueError: Zemax应用程序未连接
            Exception: 数据获取失败
        """
        try:
            if not self.zemax_app:
                raise ValueError("Zemax应用程序未连接")
            
            # 获取TheSystem对象
            the_system = self.zemax_app.TheSystem
            
            # 清空之前的数据
            self.system_data = []
            
            # 获取光圈信息
            self.system_data.append({
                "parameter": "ApertureType", 
                "value": str(the_system.SystemData.Aperture.ApertureType)
            })
            self.system_data.append({
                "parameter": "ApertureValue", 
                "value": str(the_system.SystemData.Aperture.ApertureValue)
            })
            
            # 获取视场和波长数量
            fields_num = the_system.SystemData.Fields.NumberOfFields
            wavelengths_num = the_system.SystemData.Wavelengths.NumberOfWavelengths
            
            self.system_data.append({"parameter": "FieldsNum", "value": str(fields_num)})
            self.system_data.append({"parameter": "WavelengthsNum", "value": str(wavelengths_num)})
            
            self.logger.info(f"视场数量: {fields_num}")
            
            # 遍历视场
            for n in range(fields_num):
                field = the_system.SystemData.Fields.GetField(n + 1)
                field_str = f"[X:{field.X}, Y:{field.Y}, W:{field.Weight}, active:{field.IsActive}]"
                self.system_data.append({
                    "parameter": f"Field{n + 1}", 
                    "value": field_str
                })
            
            self.logger.info(f"波长数量: {wavelengths_num}")
            
            # 遍历波长
            for n in range(wavelengths_num):
                wavelength = the_system.SystemData.Wavelengths.GetWavelength(n + 1)
                wave_str = f"[wavelength:{wavelength.Wavelength}, W:{wavelength.Weight}, active:{wavelength.IsActive}, primary:{wavelength.IsPrimary}]"
                self.system_data.append({
                    "parameter": f"Wavelength{n + 1}", 
                    "value": wave_str
                })
            
            # 获取环境参数
            self.system_data.append({
                "parameter": "AdjustIndexDataToEnvironment", 
                "value": str(the_system.SystemData.Environment.AdjustIndexToEnvironment)
            })
            self.system_data.append({
                "parameter": "Temperature", 
                "value": str(the_system.SystemData.Environment.Temperature)
            })
            self.system_data.append({
                "parameter": "Pressure", 
                "value": str(the_system.SystemData.Environment.Pressure)
            })
            
            # 获取单位设置
            self.system_data.append({
                "parameter": "LensUnits(0:mm,1:cm,2:inc,3:m)", 
                "value": str(the_system.SystemData.Units.LensUnits)
            })
            self.system_data.append({
                "parameter": "SourceUnits(0:watts,1:lumens,2:joules)", 
                "value": str(the_system.SystemData.Units.SourceUnits)
            })
            
            self.logger.info(f"成功获取系统数据，共{len(self.system_data)}项")
            return self.system_data
            
        except Exception as e:
            self.logger.error(f"获取系统数据失败: {e}")
            raise
    
    def display_lens_data(self) -> List[Dict[str, Any]]:
        """
        获取镜头数据 - 基于OpticalRealSim的display_lens_data方法
        
        Returns:
            List[Dict]: 镜头数据列表，每个字典包含镜面的详细信息
            
        Raises:
            ValueError: Zemax应用程序未连接
            Exception: 数据获取失败
        """
        try:
            if not self.zemax_app:
                raise ValueError("Zemax应用程序未连接")
            
            # 获取TheSystem对象
            the_system = self.zemax_app.TheSystem
            
            # 清空之前的数据
            self.lens_data = []
            
            # 获取镜面数量
            nsur = the_system.LDE.NumberOfSurfaces
            self.logger.info(f"镜面数量: {nsur}")
            
            # 遍历所有镜面
            for n in range(nsur):
                surface = the_system.LDE.GetSurfaceAt(n)
                
                lens = {
                    'ID': str(n),
                    'Surface Type': str(surface.TypeName),
                    'Radius': str(surface.Radius),
                    'Thickness': str(surface.Thickness),
                    'Material': str(surface.Material),
                    'Clear Semi-Diameter': str(surface.SemiDiameter)
                }
                
                self.lens_data.append(lens)
            
            self.logger.info(f"成功获取镜头数据，共{len(self.lens_data)}个镜面")
            return self.lens_data
            
        except Exception as e:
            self.logger.error(f"获取镜头数据失败: {e}")
            raise
    
    def get_system_data(self) -> List[Dict[str, Any]]:
        """获取当前缓存的系统数据"""
        return self.system_data.copy()
    
    def get_lens_data(self) -> List[Dict[str, Any]]:
        """获取当前缓存的镜头数据"""
        return self.lens_data.copy()
    
    def refresh_all_data(self) -> bool:
        """
        刷新所有数据
        
        Returns:
            bool: 刷新成功返回True
        """
        try:
            self.display_system_data()
            self.display_lens_data()
            self.logger.info("所有数据刷新完成")
            return True
        except Exception as e:
            self.logger.error(f"刷新数据失败: {e}")
            return False
    
    def export_data_to_dict(self) -> Dict[str, Any]:
        """
        导出所有数据为字典格式
        
        Returns:
            Dict: 包含系统数据和镜头数据的字典
        """
        return {
            'system_data': self.system_data.copy(),
            'lens_data': self.lens_data.copy(),
            'export_time': str(np.datetime64('now')),
            'total_surfaces': len(self.lens_data)
        }
    
    def find_primary_mirror_index(self) -> int:
        """
        查找主镜索引 - 用于光学系统示意图
        
        Returns:
            int: 主镜索引，如果未找到返回-1
        """
        try:
            # 简单的启发式方法：查找最大直径的镜面作为主镜
            max_diameter = 0
            primary_index = -1
            
            for i, lens in enumerate(self.lens_data):
                try:
                    diameter = float(lens.get('Clear Semi-Diameter', 0)) * 2
                    if diameter > max_diameter:
                        max_diameter = diameter
                        primary_index = i
                except (ValueError, TypeError):
                    continue
            
            self.logger.info(f"找到主镜索引: {primary_index}, 直径: {max_diameter}")
            return primary_index
            
        except Exception as e:
            self.logger.error(f"查找主镜索引失败: {e}")
            return -1
    
    def get_data_summary(self) -> Dict[str, Any]:
        """
        获取数据摘要信息
        
        Returns:
            Dict: 包含数据摘要的字典
        """
        summary = {
            'zemax_connected': self.zemax_app is not None,
            'system_parameters_count': len(self.system_data),
            'total_surfaces': len(self.lens_data),
            'primary_mirror_index': self.find_primary_mirror_index()
        }
        
        # 统计镜面类型
        surface_types = {}
        for lens in self.lens_data:
            surface_type = lens.get('Surface Type', 'Unknown')
            surface_types[surface_type] = surface_types.get(surface_type, 0) + 1
        
        summary['surface_types'] = surface_types
        
        return summary
