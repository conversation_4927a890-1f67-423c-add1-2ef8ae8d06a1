"""
Zemax连接器

提供与Zemax OpticStudio的异步连接功能，基于OpticalRealSim-main的连接逻辑实现。
通过Qt信号槽机制与UI线程通信，确保UI响应性。

主要功能：
- 异步连接到Zemax OpticStudio
- 连接状态管理和监控
- 错误处理和用户友好提示
- 资源清理和连接管理

作者: AI Assistant
版本: 1.0.0
"""

import logging
from typing import Optional
from PyQt5.QtCore import QObject, pyqtSignal

from .worker_threads import ZemaxWorkerThread, ZemaxApplication
from .exceptions import ZemaxBaseException

# 配置日志
logger = logging.getLogger(__name__)


class ZemaxConnector(QObject):
    """
    Zemax连接器
    
    提供与Zemax OpticStudio的异步连接功能。通过工作线程执行连接逻辑，
    避免阻塞UI线程，并通过信号槽机制提供实时的连接状态反馈。
    """
    
    # 信号定义
    connection_status_changed = pyqtSignal(bool, str)  # (is_connected, message)
    progress_updated = pyqtSignal(str)                 # 进度信息
    connection_started = pyqtSignal()                  # 连接开始
    connection_finished = pyqtSignal()                 # 连接结束（成功或失败）
    
    def __init__(self, zemax_path=None):
        """
        初始化Zemax连接器
        
        Args:
            zemax_path: Zemax安装路径，None表示自动检测
        """
        super().__init__()
        
        # 连接状态
        self._is_connected = False
        self._connection_in_progress = False
        
        # Zemax相关对象
        self._zemax_app = None  # type: Optional[ZemaxApplication]
        self._worker_thread = None  # type: Optional[ZemaxWorkerThread]
        self._zemax_path = zemax_path
        
        logger.info("ZemaxConnector已初始化")
    
    def is_connected(self) -> bool:
        """
        检查是否已连接到Zemax
        
        Returns:
            bool: 连接状态
        """
        return self._is_connected
    
    def is_connecting(self) -> bool:
        """
        检查是否正在连接中
        
        Returns:
            bool: 连接进行状态
        """
        return self._connection_in_progress
    
    def get_zemax_app(self):
        """
        获取Zemax应用程序对象
        
        Returns:
            ZemaxApplication: Zemax应用程序对象，未连接时返回None
        """
        return self._zemax_app
    
    def connect_async(self):
        """
        异步连接到Zemax
        
        启动工作线程执行连接逻辑，不会阻塞UI线程。
        连接过程中会发送进度信号，连接完成后发送状态变化信号。
        """
        # 检查当前状态
        if self._connection_in_progress:
            self.progress_updated.emit("连接正在进行中，请稍候...")
            logger.warning("连接已在进行中，忽略重复请求")
            return
        
        if self._is_connected:
            self.progress_updated.emit("已经连接到Zemax")
            logger.info("已经连接到Zemax，无需重复连接")
            return
        
        # 开始连接过程
        self._start_connection()
    
    def disconnect(self):
        """
        断开与Zemax的连接
        
        停止工作线程，清理资源，更新连接状态。
        """
        logger.info("开始断开Zemax连接")
        
        # 停止工作线程
        if self._worker_thread and self._worker_thread.isRunning():
            logger.info("正在停止连接线程...")
            self._worker_thread.terminate()
            self._worker_thread.wait(3000)  # 等待最多3秒
            
            if self._worker_thread.isRunning():
                logger.warning("工作线程未能正常停止，强制终止")
                self._worker_thread.terminate()
        
        # 清理Zemax连接
        if self._zemax_app:
            try:
                self._zemax_app.cleanup()
                logger.info("Zemax应用程序已清理")
            except Exception as e:
                logger.error(f"清理Zemax应用程序时出错: {e}")
        
        # 重置状态
        self._zemax_app = None
        self._worker_thread = None
        self._is_connected = False
        self._connection_in_progress = False
        
        # 发送状态变化信号
        self.connection_status_changed.emit(False, "已断开与Zemax的连接")
        self.connection_finished.emit()
        
        logger.info("Zemax连接已断开")
    
    def _start_connection(self):
        """启动连接过程"""
        logger.info("开始连接到Zemax")
        
        # 更新状态
        self._connection_in_progress = True
        self.connection_started.emit()
        
        # 创建并启动工作线程
        self._worker_thread = ZemaxWorkerThread(self._zemax_path)
        
        # 连接信号
        self._worker_thread.progress.connect(self._on_connection_progress)
        self._worker_thread.success.connect(self._on_connection_success)
        self._worker_thread.error.connect(self._on_connection_error)
        self._worker_thread.finished.connect(self._on_thread_finished)
        
        # 启动线程
        self._worker_thread.start()
        
        self.progress_updated.emit("正在启动Zemax连接...")
    
    def _on_connection_progress(self, message: str):
        """
        处理连接进度更新
        
        Args:
            message: 进度信息
        """
        logger.debug(f"连接进度: {message}")
        self.progress_updated.emit(message)
    
    def _on_connection_success(self, zemax_app: ZemaxApplication):
        """
        处理连接成功
        
        Args:
            zemax_app: Zemax应用程序对象
        """
        logger.info("Zemax连接成功")
        
        # 更新状态
        self._zemax_app = zemax_app
        self._is_connected = True
        self._connection_in_progress = False
        
        # 发送成功信号
        success_message = "✅ 已成功连接到Zemax OpticStudio"
        self.connection_status_changed.emit(True, success_message)
        self.progress_updated.emit(success_message)
    
    def _on_connection_error(self, error_message: str):
        """
        处理连接错误
        
        Args:
            error_message: 错误信息
        """
        logger.error(f"Zemax连接失败: {error_message}")
        
        # 更新状态
        self._is_connected = False
        self._connection_in_progress = False
        self._zemax_app = None
        
        # 发送错误信号
        error_msg = f"❌ Zemax连接失败: {error_message}"
        self.connection_status_changed.emit(False, error_msg)
        self.progress_updated.emit(error_msg)
    
    def _on_thread_finished(self):
        """处理线程结束"""
        logger.debug("连接线程已结束")
        
        # 确保状态正确
        if self._connection_in_progress:
            self._connection_in_progress = False
        
        # 发送连接结束信号
        self.connection_finished.emit()
        
        # 清理线程引用
        if self._worker_thread:
            self._worker_thread.deleteLater()
            self._worker_thread = None
    
    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            if self._is_connected or self._connection_in_progress:
                self.disconnect()
        except Exception as e:
            logger.error(f"析构时清理资源出错: {e}")


# 全局连接器实例（可选）
_global_connector: Optional[ZemaxConnector] = None


def get_global_connector() -> ZemaxConnector:
    """
    获取全局Zemax连接器实例
    
    Returns:
        ZemaxConnector: 全局连接器实例
    """
    global _global_connector
    if _global_connector is None:
        _global_connector = ZemaxConnector()
    return _global_connector


def cleanup_global_connector():
    """清理全局连接器实例"""
    global _global_connector
    if _global_connector is not None:
        _global_connector.disconnect()
        _global_connector = None
