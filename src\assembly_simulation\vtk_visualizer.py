#!/usr/bin/env python3
"""
25AutoAssembly VTK可视化器模块

基于VTK实现URDF模型的3D可视化，严格参考testsimple/urdf_viewer_standalone.py中的逻辑
包括：
- VTK渲染器设置和管理
- 几何体创建和变换
- 坐标系显示和相机控制
- Qt集成和交互处理
"""

import os
import logging
from typing import Dict, List, Optional, Tuple, Any
import numpy as np

# VTK相关导入
VTK_AVAILABLE = False
QVTK_AVAILABLE = False
try:
    import vtk
    print(f"VTK version: {vtk.vtkVersion.GetVTKVersion()}")
    VTK_AVAILABLE = True
    
    # 尝试导入Qt集成组件 - 支持多种导入方式
    try:
        # 方式1: 标准VTK Qt集成 (VTK 9.0+)
        from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
        QVTK_AVAILABLE = True
        print("VTK-Qt integration available (standard)")
    except ImportError as e1:
        print(f"Standard VTK-Qt integration not available: {e1}")
        try:
            # 方式2: 尝试直接从vtkmodules导入 (VTK 9.0+新结构)
            from vtkmodules.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
            QVTK_AVAILABLE = True
            print("VTK-Qt integration available (vtkmodules)")
        except ImportError as e2:
            print(f"vtkmodules VTK-Qt integration not available: {e2}")
            try:
                # 方式3: 尝试从vtk.qt5导入 (某些VTK版本)
                from vtk.qt5.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
                QVTK_AVAILABLE = True
                print("VTK-Qt integration available (qt5)")
            except ImportError as e3:
                print(f"qt5 VTK-Qt integration not available: {e3}")
                QVTK_AVAILABLE = False
                print("❌ 所有VTK-Qt集成方式都不可用")
                print("💡 解决方案:")
                print("   1. 重新安装VTK: pip uninstall vtk && pip install vtk")
                print("   2. 或使用conda: conda install vtk")
                print("   3. 确保安装了完整的VTK包 (包含Qt支持)")

except ImportError as e:
    print(f"VTK not available: {e}")
    print("解决方案:")
    print("1. 安装VTK: pip install vtk")
    print("2. 或者使用conda: conda install vtk")
    print("3. 确保VTK版本>=9.0.0且<10.0.0")
    VTK_AVAILABLE = False

# Qt相关导入
try:
    from PyQt5.QtWidgets import QWidget, QVBoxLayout, QApplication
    from PyQt5.QtCore import Qt
    QT_AVAILABLE = True
except ImportError as e:
    logging.warning(f"PyQt5导入失败: {e}")
    QT_AVAILABLE = False

# 获取日志记录器
logger = logging.getLogger(__name__)


class VTKVisualizer:
    """
    VTK可视化器 - 基于standalone版本的VTK逻辑
    
    负责URDF模型的3D可视化，包括几何体创建、变换应用和渲染管理
    """
    
    def __init__(self, parent_widget=None):
        """
        初始化VTK可视化器
        
        Args:
            parent_widget: 父Qt组件
        """
        self.parent_widget = parent_widget
        self.vtk_widget = None
        self.renderer = None
        self.vtk_actors = []  # 存储所有VTK actors
        self.link_actors = {}  # 存储每个link对应的actors，用于姿态更新
        self.urdf_parser = None
        self.orientation_widget = None  # 右下角坐标轴控件
        
        logger.info("VTKVisualizer初始化完成")
    
    def initialize_vtk_widget(self) -> bool:
        """
        初始化VTK组件
        
        Returns:
            bool: 初始化成功返回True，失败返回False
        """
        if not (VTK_AVAILABLE and QVTK_AVAILABLE and QT_AVAILABLE):
            logger.error("VTK或Qt组件不可用")
            return False
        
        try:
            # 创建VTK渲染组件
            self.vtk_widget = QVTKRenderWindowInteractor(self.parent_widget)
            
            # 设置VTK渲染器
            success = self._setup_vtk_renderer()
            if not success:
                logger.error("VTK渲染器设置失败")
                return False
            
            logger.info("VTK组件初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"VTK组件初始化失败: {e}")
            return False
    
    def _setup_vtk_renderer(self) -> bool:
        """
        设置VTK渲染器 - 基于standalone版本

        Returns:
            bool: 设置成功返回True，失败返回False
        """
        try:
            # 创建渲染器
            self.renderer = vtk.vtkRenderer()
            self.renderer.SetBackground(0.2, 0.3, 0.4)  # 深蓝灰色背景

            # 添加到渲染窗口
            self.vtk_widget.GetRenderWindow().AddRenderer(self.renderer)

            # 设置交互样式为MultiTouchCamera - CAD风格的多点触控交互
            interactor = self.vtk_widget.GetRenderWindow().GetInteractor()
            if interactor:
                multitouch_style = vtk.vtkInteractorStyleMultiTouchCamera()
                interactor.SetInteractorStyle(multitouch_style)
                logger.info("设置交互样式为MultiTouchCamera - CAD风格的多点触控交互")

            # 添加右下角坐标轴控件
            self._add_orientation_marker()

            # 设置光照系统
            self._setup_lighting()

            # 添加网格
            self._add_grid()

            logger.info("VTK渲染器设置完成")
            return True

        except Exception as e:
            logger.error(f"VTK渲染器设置失败: {e}")
            return False
    
    def _add_grid(self):
        """添加网格 - 基于standalone版本"""
        try:
            # 创建网格平面
            plane = vtk.vtkPlaneSource()
            plane.SetXResolution(10)
            plane.SetYResolution(10)
            plane.SetOrigin(-1, -1, 0)
            plane.SetPoint1(1, -1, 0)
            plane.SetPoint2(-1, 1, 0)
            
            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(plane.GetOutputPort())
            
            actor = vtk.vtkActor()
            actor.SetMapper(mapper)
            actor.GetProperty().SetRepresentationToWireframe()
            actor.GetProperty().SetColor(0.5, 0.5, 0.5)
            actor.GetProperty().SetOpacity(0.3)

            # 为网格设置适当的材质属性（线框模式，不需要复杂光照）
            actor.GetProperty().SetAmbient(0.8)  # 高环境光，确保网格可见
            actor.GetProperty().SetDiffuse(0.2)  # 低漫反射
            actor.GetProperty().SetSpecular(0.0) # 无镜面反射

            self.renderer.AddActor(actor)
            
        except Exception as e:
            logger.error(f"添加网格失败: {e}")

    def _setup_lighting(self):
        """设置光照系统 - 基于物理渲染的日光系统 {{ AURA: Modify - 重新设计为真实日光系统 }}"""
        try:
            # 禁用自动光照创建，使用自定义光照
            self.renderer.SetAutomaticLightCreation(False)

            # 移除默认光源
            lights = self.renderer.GetLights()
            lights.InitTraversal()
            light = lights.GetNextItem()
            while light:
                self.renderer.RemoveLight(light)
                light = lights.GetNextItem()

            # === 日光系统参数 ===
            sun_intensity = 2.0          # 主太阳光强度 (类似Unity的Sun Brightness)
            sky_intensity = 0.8          # 天空散射光强度 (类似Sky Light)
            ambient_intensity = 0.55     # 环境光强度 (全局基础照明)
            ground_reflection = 0.3      # 地面反射光贡献

            # 太阳光色温参数 (5500K日光)
            sun_color = (1.0, 0.98, 0.92)      # 略带暖色的日光
            sky_color = (0.4, 0.6, 1.0)        # 蓝色天空光
            ground_color = (0.8, 0.75, 0.7)    # 暖色地面反射

            # 1. 主太阳光 (Direct Sun Light) - 模拟太阳直射光 {{ AURA: Modify - 修改太阳光角度为Z正方向 }}
            sun_light = vtk.vtkLight()
            sun_light.SetLightTypeToSceneLight()
            sun_light.SetPosition(0.0, 0.0, 5.0)  # 太阳位置 (Z正方向，高度角约30度)
            sun_light.SetFocalPoint(0.0, 0.0, 0.0)
            sun_light.SetColor(*sun_color)  # 日光色温
            sun_light.SetIntensity(sun_intensity)  # 主太阳光强度
            sun_light.SetConeAngle(30)  # 较小角度，模拟平行光
            self.renderer.AddLight(sun_light)

            # 2. 天空散射光 (Sky Light) - 模拟天空的漫射光
            sky_light = vtk.vtkLight()
            sky_light.SetLightTypeToSceneLight()
            sky_light.SetPosition(0.0, 5.0, 0.0)  # 天顶位置
            sky_light.SetFocalPoint(0.0, 0.0, 0.0)
            sky_light.SetColor(*sky_color)  # 蓝色天空光
            sky_light.SetIntensity(sky_intensity)  # 天空光强度
            sky_light.SetConeAngle(180)  # 大角度覆盖，模拟天空散射
            self.renderer.AddLight(sky_light)

            # 3. 地面反射光 (Ground Bounce Light) - 模拟地面反射
            ground_light = vtk.vtkLight()
            ground_light.SetLightTypeToSceneLight()
            ground_light.SetPosition(0.0, -2.0, 0.0)  # 地面下方
            ground_light.SetFocalPoint(0.0, 0.0, 0.0)
            ground_light.SetColor(*ground_color)  # 暖色地面反射
            ground_light.SetIntensity(ground_reflection)  # 地面反射强度
            ground_light.SetConeAngle(120)  # 中等角度覆盖
            self.renderer.AddLight(ground_light)

            # 4. 补充侧光 (Fill Light) - 减少阴影，增强细节
            fill_light = vtk.vtkLight()
            fill_light.SetLightTypeToSceneLight()
            fill_light.SetPosition(-2.0, -2.0, 1.0)  # 侧面位置
            fill_light.SetFocalPoint(0.0, 0.0, 0.0)
            fill_light.SetColor(0.95, 0.95, 1.0)  # 略带冷色调
            fill_light.SetIntensity(0.4)  # 较低强度补光
            fill_light.SetConeAngle(60)  # 中等角度
            self.renderer.AddLight(fill_light)

            # 5. 全局环境光 (Global Ambient) - 模拟全局照明
            self.renderer.SetAmbient(ambient_intensity, ambient_intensity, ambient_intensity)

            logger.info(f"日光系统设置完成 - Sun:{sun_intensity}, Sky:{sky_intensity}, Ambient:{ambient_intensity}, Ground:{ground_reflection}")

        except Exception as e:
            logger.error(f"设置光照系统失败: {e}")

    def enable_lighting(self) -> bool:
        """
        启用光照系统 {{ AURA: Add - 新增光照开关功能 }}

        Returns:
            bool: 启用成功返回True，失败返回False
        """
        try:
            if not self.renderer:
                logger.warning("渲染器未初始化，无法启用光照")
                return False

            # 重新设置光照系统
            self._setup_lighting()

            # 刷新渲染
            if self.vtk_widget:
                self.vtk_widget.GetRenderWindow().Render()

            logger.info("光照系统已启用")
            return True

        except Exception as e:
            logger.error(f"启用光照系统失败: {e}")
            return False

    def disable_lighting(self) -> bool:
        """
        禁用光照系统 {{ AURA: Add - 新增光照开关功能 }}

        Returns:
            bool: 禁用成功返回True，失败返回False
        """
        try:
            if not self.renderer:
                logger.warning("渲染器未初始化，无法禁用光照")
                return False

            # 移除所有光源
            lights = self.renderer.GetLights()
            lights.InitTraversal()
            light = lights.GetNextItem()
            while light:
                self.renderer.RemoveLight(light)
                light = lights.GetNextItem()

            # 设置最小环境光，保持基本可见性
            self.renderer.SetAmbient(0.8, 0.8, 0.8)  # 高环境光补偿无光源状态

            # 刷新渲染
            if self.vtk_widget:
                self.vtk_widget.GetRenderWindow().Render()

            logger.info("光照系统已禁用")
            return True

        except Exception as e:
            logger.error(f"禁用光照系统失败: {e}")
            return False

    def _enhance_material_properties(self, actor: 'vtk.vtkActor', material_type: str = 'default'):
        """
        增强Actor的材质属性以配合光照效果

        Args:
            actor: VTK Actor对象
            material_type: 材质类型 ('default', 'metal', 'plastic', 'ceramic')
        """
        try:
            property_obj = actor.GetProperty()

            # 基础材质设置
            if material_type == 'metal':
                # 金属材质 - 高镜面反射，低漫反射
                property_obj.SetAmbient(0.1)      # 环境光反射
                property_obj.SetDiffuse(0.6)      # 漫反射
                property_obj.SetSpecular(0.8)     # 镜面反射
                property_obj.SetSpecularPower(50) # 镜面光泽度
            elif material_type == 'plastic':
                # 塑料材质 - 中等镜面反射，高漫反射
                property_obj.SetAmbient(0.2)
                property_obj.SetDiffuse(0.8)
                property_obj.SetSpecular(0.4)
                property_obj.SetSpecularPower(20)
            elif material_type == 'ceramic':
                # 陶瓷材质 - 低镜面反射，高漫反射
                property_obj.SetAmbient(0.15)
                property_obj.SetDiffuse(0.9)
                property_obj.SetSpecular(0.2)
                property_obj.SetSpecularPower(10)
            else:
                # 默认材质 - 平衡的光照属性
                property_obj.SetAmbient(0.15)     # 适中的环境光反射
                property_obj.SetDiffuse(0.7)      # 主要的漫反射
                property_obj.SetSpecular(0.3)     # 适中的镜面反射
                property_obj.SetSpecularPower(15) # 适中的光泽度

            # 启用光照计算
            property_obj.SetLighting(True)

            # 设置插值方式为Phong，获得更平滑的光照效果
            property_obj.SetInterpolationToPhong()

        except Exception as e:
            logger.error(f"增强材质属性失败: {e}")

    def _add_orientation_marker(self):
        """添加右下角坐标轴控件 - 使用vtkOrientationMarkerWidget和vtkAxesActor"""
        try:
            # 创建vtkAxesActor作为坐标轴标记
            axes_actor = vtk.vtkAxesActor()

            # 设置坐标轴属性
            axes_actor.SetTotalLength(1.0, 1.0, 1.0)  # 设置轴的总长度
            axes_actor.SetShaftTypeToCylinder()        # 设置轴为圆柱形
            axes_actor.SetCylinderRadius(0.02)         # 设置圆柱半径
            axes_actor.SetConeRadius(0.05)             # 设置锥形半径
            axes_actor.SetSphereRadius(0.03)           # 设置球形半径

            # 设置轴标签
            axes_actor.SetXAxisLabelText("X")
            axes_actor.SetYAxisLabelText("Y")
            axes_actor.SetZAxisLabelText("Z")

            # 设置标签文本属性
            x_caption = axes_actor.GetXAxisCaptionActor2D()
            y_caption = axes_actor.GetYAxisCaptionActor2D()
            z_caption = axes_actor.GetZAxisCaptionActor2D()

            # 设置字体属性
            for caption in [x_caption, y_caption, z_caption]:
                text_prop = caption.GetCaptionTextProperty()
                text_prop.SetFontSize(12)
                text_prop.BoldOn()
                text_prop.ShadowOn()

            # 设置轴颜色 - X红色，Y绿色，Z蓝色
            x_caption.GetCaptionTextProperty().SetColor(1.0, 0.0, 0.0)  # 红色
            y_caption.GetCaptionTextProperty().SetColor(0.0, 1.0, 0.0)  # 绿色
            z_caption.GetCaptionTextProperty().SetColor(0.0, 0.0, 1.0)  # 蓝色

            # 创建OrientationMarkerWidget
            self.orientation_widget = vtk.vtkOrientationMarkerWidget()
            self.orientation_widget.SetOrientationMarker(axes_actor)

            # 获取交互器
            interactor = self.vtk_widget.GetRenderWindow().GetInteractor()
            if interactor:
                self.orientation_widget.SetInteractor(interactor)

                # 设置位置在右下角 (viewport: x_min, y_min, x_max, y_max)
                # 右下角位置：x从0.8到1.0，y从0.0到0.2
                self.orientation_widget.SetViewport(0.8, 0.0, 1.0, 0.2)

                # 设置外框颜色
                self.orientation_widget.SetOutlineColor(0.9, 0.9, 0.9)

                # 启用控件
                self.orientation_widget.SetEnabled(True)
                self.orientation_widget.InteractiveOn()  # 允许交互

                logger.info("右下角坐标轴控件添加成功")
            else:
                logger.warning("无法获取交互器，坐标轴控件可能无法正常工作")

        except Exception as e:
            logger.error(f"添加坐标轴控件失败: {e}")

    def get_vtk_widget(self):
        """
        获取VTK组件
        
        Returns:
            QVTKRenderWindowInteractor: VTK组件
        """
        return self.vtk_widget
    
    def load_urdf_model(self, urdf_parser) -> bool:
        """
        加载URDF模型进行可视化 - 基于standalone版本的visualize_urdf逻辑
        
        Args:
            urdf_parser: URDF解析器实例
            
        Returns:
            bool: 加载成功返回True，失败返回False
        """
        if not (VTK_AVAILABLE and QVTK_AVAILABLE):
            logger.error("VTK组件不可用")
            return False
        
        if not urdf_parser.is_loaded():
            logger.error("URDF解析器未加载模型")
            return False
        
        try:
            self.urdf_parser = urdf_parser
            
            # 清除之前的actors
            self._clear_previous_actors()
            
            # 可视化URDF
            success = self._visualize_urdf()
            
            if success:
                # 重置相机视角
                self.reset_camera()
                logger.info("URDF模型加载成功")
                return True
            else:
                logger.error("URDF模型可视化失败")
                return False
                
        except Exception as e:
            logger.error(f"加载URDF模型失败: {e}")
            return False
    
    def _clear_previous_actors(self):
        """清除之前的actors"""
        for actor in self.vtk_actors:
            self.renderer.RemoveActor(actor)
        self.vtk_actors.clear()
        self.link_actors.clear()
    
    def _visualize_urdf(self) -> bool:
        """
        可视化URDF - 基于standalone版本的visualize_urdf逻辑
        
        Returns:
            bool: 可视化成功返回True，失败返回False
        """
        try:
            colors = [(0.8, 0.2, 0.2), (0.2, 0.8, 0.2), (0.2, 0.2, 0.8),
                     (0.8, 0.8, 0.2), (0.8, 0.2, 0.8), (0.2, 0.8, 0.8)]

            links = self.urdf_parser.get_links()
            logger.info(f"开始可视化 {len(links)} 个连杆...")

            for i, link in enumerate(links):
                logger.info(f"处理连杆 {i}: {link['name']} 包含 {len(link['visual'])} 个visual元素")

                # 获取从根链接到当前链接的累积变换
                transform_chain = self.urdf_parser.get_transform_chain(link['name'])
                cumulative_transform = self.urdf_parser.compute_cumulative_transform(transform_chain)

                logger.info(f"  连杆 {link['name']} 的变换链: {len(transform_chain)} 个变换")

                # 初始化该连杆的actor列表
                link_name = link['name']
                if link_name not in self.link_actors:
                    self.link_actors[link_name] = []

                for j, visual in enumerate(link['visual']):
                    logger.info(f"  Visual元素 {j}: {visual}")

                    if visual['geometry']:
                        logger.info(f"  为几何体创建actor: {visual['geometry']}")
                        actors = self._create_geometry_actor(visual['geometry'])
                        if actors:
                            # 设置颜色
                            color = colors[i % len(colors)]

                            # 计算visual变换矩阵
                            # visual['origin']是一个列表: [x, y, z, roll, pitch, yaw]
                            origin = visual.get('origin', [0, 0, 0, 0, 0, 0])
                            xyz = origin[:3]  # 前3个元素是位置
                            rpy = origin[3:]  # 后3个元素是旋转
                            visual_transform = self.urdf_parser.create_transform_matrix(xyz, rpy)

                            # 处理每个Actor（VRML可能有多个）
                            for actor_idx, actor in enumerate(actors):
                                # 设置颜色（VRML Actor保持原有材质，其他类型设置颜色）
                                if visual['geometry']['type'] != 'vrml':
                                    actor.GetProperty().SetColor(*color)

                                # 增强材质属性以配合光照效果
                                # 根据几何体类型选择合适的材质
                                if visual['geometry']['type'] in ['mesh', 'vrml']:
                                    # 网格模型通常是机械零件，使用金属材质
                                    self._enhance_material_properties(actor, 'metal')
                                elif visual['geometry']['type'] in ['box', 'cylinder', 'sphere']:
                                    # 基本几何体使用塑料材质
                                    self._enhance_material_properties(actor, 'plastic')
                                else:
                                    # 其他类型使用默认材质
                                    self._enhance_material_properties(actor, 'default')

                                # 应用累积变换
                                success = self._apply_transform_to_actor(actor, visual, cumulative_transform)
                                if success:
                                    self.renderer.AddActor(actor)
                                    self.vtk_actors.append(actor)

                                    # 存储到link_actors中，包含actor和visual变换信息
                                    actor_info = {
                                        'actor': actor,
                                        'visual_transform': visual_transform
                                }
                                self.link_actors[link_name].append(actor_info)

                                logger.info(f"  成功添加 {link['name']} 的actor")
                            else:
                                logger.warning(f"  应用变换失败: {link['name']}")
                        else:
                            logger.warning(f"  创建actor失败: {link['name']}")
                    else:
                        logger.info(f"  Visual元素 {j} 没有几何体")

            logger.info(f"可视化完成，共创建 {len(self.vtk_actors)} 个组件")
            return True

        except Exception as e:
            logger.error(f"可视化过程中出错: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _create_geometry_actor(self, geometry):
        """
        根据几何体类型创建VTK actor - 支持多Actor返回

        Args:
            geometry: 几何体信息字典

        Returns:
            list: VTK actor对象列表，VRML可能返回多个Actor，其他类型返回单个Actor的列表
        """
        try:
            if geometry['type'] == 'box':
                actor = self._create_box_actor(geometry['size'])
                return [actor] if actor else []
            elif geometry['type'] == 'cylinder':
                actor = self._create_cylinder_actor(geometry['radius'], geometry['length'])
                return [actor] if actor else []
            elif geometry['type'] == 'sphere':
                actor = self._create_sphere_actor(geometry['radius'])
                return [actor] if actor else []
            elif geometry['type'] == 'mesh':
                actor = self._create_mesh_actor(geometry['filename'])
                return [actor] if actor else []
            elif geometry['type'] == 'vrml':
                return self._create_vrml_actors(geometry['filename'])  # 返回多个Actor
            elif geometry['type'] == 'glb':
                actor = self._create_glb_actor(geometry['filename'])
                return [actor] if actor else []
            else:
                logger.warning(f"未知的几何体类型: {geometry['type']}")
                return []
        except Exception as e:
            logger.error(f"创建几何体actor失败: {e}")
            return []

    def _create_box_actor(self, size):
        """创建立方体actor - 基于standalone版本"""
        box = vtk.vtkCubeSource()
        box.SetXLength(size[0])
        box.SetYLength(size[1])
        box.SetZLength(size[2])

        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(box.GetOutputPort())

        actor = vtk.vtkActor()
        actor.SetMapper(mapper)

        return actor

    def _create_cylinder_actor(self, radius, length):
        """创建圆柱体actor - 基于standalone版本"""
        cylinder = vtk.vtkCylinderSource()
        cylinder.SetRadius(radius)
        cylinder.SetHeight(length)
        cylinder.SetResolution(20)

        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(cylinder.GetOutputPort())

        actor = vtk.vtkActor()
        actor.SetMapper(mapper)

        return actor

    def _create_sphere_actor(self, radius):
        """创建球体actor - 基于standalone版本"""
        sphere = vtk.vtkSphereSource()
        sphere.SetRadius(radius)
        sphere.SetThetaResolution(20)
        sphere.SetPhiResolution(20)

        mapper = vtk.vtkPolyDataMapper()
        mapper.SetInputConnection(sphere.GetOutputPort())

        actor = vtk.vtkActor()
        actor.SetMapper(mapper)

        return actor

    def _create_mesh_actor(self, filename):
        """创建mesh actor - 基于standalone版本"""
        try:
            logger.info(f"尝试加载mesh: {filename}")

            # 检查文件是否存在
            if not os.path.exists(filename):
                logger.warning(f"Mesh文件不存在: {filename}")
                return None

            if filename.lower().endswith('.stl'):
                reader = vtk.vtkSTLReader()
                reader.SetFileName(filename)
                reader.Update()
                logger.info(f"成功加载STL: {filename}")
            elif filename.lower().endswith('.obj'):
                reader = vtk.vtkOBJReader()
                reader.SetFileName(filename)
                reader.Update()
                logger.info(f"成功加载OBJ: {filename}")
            else:
                logger.warning(f"不支持的mesh格式: {filename}")
                return None

            mapper = vtk.vtkPolyDataMapper()
            mapper.SetInputConnection(reader.GetOutputPort())

            actor = vtk.vtkActor()
            actor.SetMapper(mapper)

            return actor
        except Exception as e:
            logger.error(f"加载mesh失败 {filename}: {e}")
            return None

    def _create_vrml_actors(self, filename):
        """创建VRML actors - 支持多Separator"""
        try:
            logger.info(f"尝试加载VRML: {filename}")

            # 检查文件是否存在
            if not os.path.exists(filename):
                logger.warning(f"VRML文件不存在: {filename}")
                return []

            # 导入VRML解析器
            from .vrml_processor import VRML10Parser

            # 创建解析器并解析文件
            parser = VRML10Parser()
            actors = parser.parse_file(filename)

            if actors:
                logger.info(f"成功加载VRML: {filename}, 创建了{len(actors)}个Actor")
                return actors  # 返回所有Actor
            else:
                logger.warning(f"VRML文件解析失败: {filename}")
                return []

        except Exception as e:
            logger.error(f"加载VRML失败 {filename}: {e}")
            return []

    def _create_glb_actor(self, filename):
        """创建GLB actor（保留接口，暂未实现）"""
        logger.warning(f"GLB格式暂未实现: {filename}")
        return None

    def _apply_transform_to_actor(self, actor, visual, cumulative_transform) -> bool:
        """
        应用变换到actor - 基于standalone版本的变换逻辑

        Args:
            actor: VTK actor对象
            visual: visual信息字典
            cumulative_transform: 累积变换矩阵

        Returns:
            bool: 应用成功返回True，失败返回False
        """
        try:
            # 创建变换对象
            transform = vtk.vtkTransform()

            # 首先应用visual元素自身的origin变换
            visual_origin = visual['origin']
            if isinstance(visual_origin, dict):
                xyz = visual_origin['xyz']
                rpy = visual_origin['rpy']
            else:
                # 兼容旧格式
                xyz = visual_origin[:3]
                rpy = visual_origin[3:6]

            # 创建visual的变换矩阵
            visual_transform = self.urdf_parser.create_transform_matrix(xyz, rpy)

            # 组合累积变换和visual变换
            final_transform = np.dot(cumulative_transform, visual_transform)

            # 从变换矩阵中提取位置
            translation = final_transform[:3, 3]

            # 设置位置
            transform.Translate(translation[0], translation[1], translation[2])

            # 从旋转矩阵计算欧拉角并应用
            # 使用VTK的矩阵设置方法
            vtk_matrix = vtk.vtkMatrix4x4()
            for row in range(4):
                for col in range(4):
                    vtk_matrix.SetElement(row, col, final_transform[row, col])

            transform.SetMatrix(vtk_matrix)
            actor.SetUserTransform(transform)

            logger.debug(f"成功应用变换，位置: {translation}")
            return True

        except Exception as e:
            logger.error(f"应用变换失败: {e}")
            return False

    def reset_camera(self):
        """重置相机 - 基于standalone版本"""
        if VTK_AVAILABLE and QVTK_AVAILABLE and hasattr(self, 'renderer') and self.renderer:
            self.renderer.ResetCamera()
            if self.vtk_widget:
                self.vtk_widget.GetRenderWindow().Render()
            logger.info("相机视角已重置")

    def render(self):
        """渲染场景"""
        if self.vtk_widget:
            self.vtk_widget.GetRenderWindow().Render()

    def update_robot_pose(self, joint_values: Dict[str, float]) -> bool:
        """
        根据关节角度更新机器人姿态 - 基于standalone版本的update_robot_pose逻辑

        Args:
            joint_values: 关节名称到角度值的映射

        Returns:
            bool: 更新成功返回True，失败返回False
        """
        if not (VTK_AVAILABLE and QVTK_AVAILABLE and hasattr(self, 'renderer')):
            logger.warning("VTK组件不可用，无法更新机器人姿态")
            return False

        if not self.urdf_parser:
            logger.warning("URDF解析器不可用，无法更新机器人姿态")
            return False

        try:
            # 重新计算所有link的变换并更新actor位置
            # 严格基于testsimple/urdf_viewer_standalone.py中的update_robot_pose逻辑
            print(f"🔧 [VTK] 开始更新机器人姿态，关节值: {joint_values}")
            logger.info(f"更新机器人姿态，关节值: {joint_values}")

            if not self.link_actors:
                print("⚠️ [VTK] 警告：link_actors为空，无法更新姿态")
                logger.warning("link_actors为空，无法更新姿态")
                return False

            updated_count = 0
            for link_name, actors in self.link_actors.items():
                print(f"🔧 [VTK] 处理连杆: {link_name}, actors数量: {len(actors)}")

                # 获取变换链
                transform_chain = self.urdf_parser.get_transform_chain(link_name, joint_values)
                print(f"🔧 [VTK] 连杆 {link_name} 变换链长度: {len(transform_chain)}")

                cumulative_transform = self.urdf_parser.compute_cumulative_transform(transform_chain)
                print(f"🔧 [VTK] 连杆 {link_name} 累积变换位置: {cumulative_transform[:3, 3]}")

                # 更新每个actor的变换
                for i, actor_info in enumerate(actors):
                    actor = actor_info['actor']
                    visual_transform = actor_info['visual_transform']

                    # 组合累积变换和visual变换
                    final_transform = np.dot(cumulative_transform, visual_transform)

                    # 创建VTK变换
                    transform = vtk.vtkTransform()
                    vtk_matrix = vtk.vtkMatrix4x4()
                    for row in range(4):
                        for col in range(4):
                            vtk_matrix.SetElement(row, col, final_transform[row, col])

                    transform.SetMatrix(vtk_matrix)
                    actor.SetUserTransform(transform)

                    print(f"🔧 [VTK] 更新actor {i} 位置: {final_transform[:3, 3]}")
                    updated_count += 1

            print(f"🔧 [VTK] 共更新了 {updated_count} 个actor")

            # 刷新渲染
            print("🔧 [VTK] 刷新渲染...")
            self.render()

            print("✅ [VTK] 机器人姿态更新完成")
            logger.info("机器人姿态更新完成")
            return True

        except Exception as e:
            logger.error(f"更新机器人姿态失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False

    def cleanup(self):
        """
        清理VTK资源 - 解决Windows平台退出时的OpenGL上下文错误
        """
        try:
            print("🧹 [VTK] 开始清理VTK资源...")

            # 清理actors
            if hasattr(self, 'vtk_actors') and self.vtk_actors:
                print(f"🧹 [VTK] 清理 {len(self.vtk_actors)} 个actors...")
                for actor in self.vtk_actors:
                    try:
                        if self.renderer:
                            self.renderer.RemoveActor(actor)
                    except Exception as e:
                        # 忽略清理过程中的错误
                        pass
                self.vtk_actors.clear()

            # 清理link_actors
            if hasattr(self, 'link_actors'):
                self.link_actors.clear()

            # 清理坐标轴控件
            if hasattr(self, 'orientation_widget') and self.orientation_widget:
                print("🧹 [VTK] 清理坐标轴控件...")
                try:
                    self.orientation_widget.SetEnabled(False)
                    self.orientation_widget = None
                except Exception as e:
                    # 忽略清理过程中的错误
                    pass

            # 清理渲染器
            if hasattr(self, 'renderer') and self.renderer:
                print("🧹 [VTK] 清理渲染器...")
                try:
                    self.renderer.RemoveAllViewProps()
                    self.renderer = None
                except Exception as e:
                    # 忽略清理过程中的错误
                    pass

            # 清理VTK组件 {{ AURA: Modify - 改进OpenGL上下文清理，解决wglMakeCurrent错误 }}
            if hasattr(self, 'vtk_widget') and self.vtk_widget:
                print("🧹 [VTK] 清理VTK组件...")
                try:
                    render_window = self.vtk_widget.GetRenderWindow()
                    if render_window:
                        # 检查OpenGL上下文是否仍然有效
                        try:
                            # 先停止渲染
                            render_window.SetAbortRender(True)

                            # 清理所有渲染器
                            renderers = render_window.GetRenderers()
                            if renderers:
                                renderers.InitTraversal()
                                renderer = renderers.GetNextItem()
                                while renderer:
                                    render_window.RemoveRenderer(renderer)
                                    renderer = renderers.GetNextItem()

                            # 安全地终结渲染窗口
                            render_window.Finalize()

                        except Exception as gl_error:
                            # OpenGL上下文错误是常见的，不影响程序正常退出
                            print(f"⚠️ [VTK] OpenGL上下文清理警告（无害）: {gl_error}")

                    # 清理交互器
                    interactor = self.vtk_widget.GetRenderWindow().GetInteractor()
                    if interactor:
                        try:
                            interactor.TerminateApp()
                        except Exception:
                            pass

                    # 设置为None
                    self.vtk_widget = None

                except Exception as e:
                    # 忽略清理过程中的错误，这些通常是无害的
                    print(f"⚠️ [VTK] VTK组件清理警告（无害）: {e}")

            print("✅ [VTK] VTK资源清理完成")

        except Exception as e:
            # 清理过程中的错误通常是无害的，只记录但不抛出
            print(f"⚠️ [VTK] VTK清理过程中出现错误（通常无害）: {e}")

    def is_available(self) -> bool:
        """
        检查VTK组件是否可用

        Returns:
            bool: 可用返回True，否则返回False
        """
        return VTK_AVAILABLE and QVTK_AVAILABLE and QT_AVAILABLE

    def get_vtk_widget(self):
        """
        获取VTK组件

        Returns:
            QVTKRenderWindowInteractor: VTK组件，如果未初始化则返回None
        """
        return self.vtk_widget

    def set_interaction_style(self, style_type="MultiTouchCamera"):
        """
        设置VTK交互样式

        Args:
            style_type (str): 交互样式类型
                - "MultiTouchCamera": 多点触控相机控制 (CAD风格，默认)
                - "TrackballActor": 轨迹球Actor控制 (直接操作3D对象)
                - "TrackballCamera": 轨迹球相机控制
                - "JoystickActor": 操纵杆Actor控制
                - "JoystickCamera": 操纵杆相机控制
                - "Flight": 飞行模式
                - "Terrain": 地形模式
        """
        if not self.vtk_widget:
            logger.warning("VTK组件未初始化，无法设置交互样式")
            return

        interactor = self.vtk_widget.GetRenderWindow().GetInteractor()
        if not interactor:
            logger.warning("VTK交互器未初始化，无法设置交互样式")
            return

        try:
            if style_type == "MultiTouchCamera":
                style = vtk.vtkInteractorStyleMultiTouchCamera()
                logger.info("设置交互样式: MultiTouchCamera - CAD风格多点触控相机控制")
            elif style_type == "TrackballActor":
                style = vtk.vtkInteractorStyleTrackballActor()
                logger.info("设置交互样式: TrackballActor - 直接操作3D对象")
            elif style_type == "TrackballCamera":
                style = vtk.vtkInteractorStyleTrackballCamera()
                logger.info("设置交互样式: TrackballCamera - 相机控制")
            elif style_type == "JoystickActor":
                style = vtk.vtkInteractorStyleJoystickActor()
                logger.info("设置交互样式: JoystickActor - 操纵杆Actor控制")
            elif style_type == "JoystickCamera":
                style = vtk.vtkInteractorStyleJoystickCamera()
                logger.info("设置交互样式: JoystickCamera - 操纵杆相机控制")
            elif style_type == "Flight":
                style = vtk.vtkInteractorStyleFlight()
                logger.info("设置交互样式: Flight - 飞行模式")
            elif style_type == "Terrain":
                style = vtk.vtkInteractorStyleTerrain()
                logger.info("设置交互样式: Terrain - 地形模式")
            else:
                # 默认使用MultiTouchCamera
                style = vtk.vtkInteractorStyleMultiTouchCamera()
                logger.info(f"未知交互样式 '{style_type}'，使用默认: MultiTouchCamera")

            interactor.SetInteractorStyle(style)

        except Exception as e:
            logger.error(f"设置交互样式失败: {e}")
            # 回退到默认样式
            try:
                default_style = vtk.vtkInteractorStyleTrackballCamera()
                interactor.SetInteractorStyle(default_style)
                logger.info("回退到默认交互样式: TrackballCamera")
            except Exception as e2:
                logger.error(f"设置默认交互样式也失败: {e2}")
