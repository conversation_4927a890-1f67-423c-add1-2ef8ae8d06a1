#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
点列图绘制组件

提供点列图的绘制和显示功能，支持多波长和多视场的散点图显示。
基于matplotlib实现，如果matplotlib不可用则提供优雅降级。

作者: AI Assistant
版本: 1.0.0
"""

import logging
from typing import Optional, List, Dict, Any
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
from PyQt5.QtCore import Qt

# 配置日志
logger = logging.getLogger(__name__)

# 条件导入matplotlib
try:
    import matplotlib
    matplotlib.use("Qt5Agg")
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import matplotlib.pyplot as plt
    import numpy as np
    
    # 设置中文字体，解决中文乱码问题
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为 SimHei
    plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示问题
    
    MATPLOTLIB_AVAILABLE = True
    logger.info("matplotlib可用，启用完整绘图功能")
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    logger.warning("matplotlib不可用，使用占位符模式")


class SpotPlotWidget(QWidget):
    """
    点列图绘制组件
    
    支持点列图的绘制和显示，包括：
    - 单波长散点图
    - 多波长散点图
    - 散点图初始化和清理
    - 优雅降级支持
    """
    
    def __init__(self, parent=None):
        """
        初始化点列图绘制组件
        
        Args:
            parent: 父组件
        """
        super().__init__(parent)
        
        # 创建布局
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(0, 0, 0, 0)
        
        if MATPLOTLIB_AVAILABLE:
            # 创建matplotlib图形
            self.figure = Figure(figsize=(8, 6), dpi=100)
            self.canvas = FigureCanvas(self.figure)
            self.ax = self.figure.add_subplot(111)
            
            # 设置图形属性
            self.figure.patch.set_facecolor('white')
            self.ax.set_aspect('equal')
            self.ax.grid(True, alpha=0.3)
            
            self.layout.addWidget(self.canvas)
            logger.info("SpotPlotWidget初始化完成（matplotlib模式）")
        else:
            # 创建占位符标签
            self.placeholder_label = QLabel("点列图显示区域\n需要安装matplotlib才能显示图形")
            self.placeholder_label.setAlignment(Qt.AlignCenter)
            self.placeholder_label.setStyleSheet("""
                QLabel {
                    border: 1px solid #ccc;
                    background-color: #f9f9f9;
                    color: #666;
                    font-size: 14px;
                    padding: 20px;
                }
            """)
            self.layout.addWidget(self.placeholder_label)
            logger.info("SpotPlotWidget初始化完成（占位符模式）")
    
    def init_scatter(self):
        """初始化散点图"""
        if not MATPLOTLIB_AVAILABLE:
            return
        
        try:
            # 清空当前图形
            self.ax.clear()
            
            # 重新设置图形属性
            self.ax.set_aspect('equal')
            self.ax.grid(True, alpha=0.3)
            self.ax.set_xlabel('X (mm)')
            self.ax.set_ylabel('Y (mm)')
            
            # 刷新画布
            self.canvas.draw()
            
        except Exception as e:
            logger.error(f"初始化散点图失败: {e}")
    
    def plot_scatter_data(self, x_data, y_data, color='b', label=None, alpha=0.6, s=20):
        """
        绘制散点数据
        
        Args:
            x_data: X坐标数据
            y_data: Y坐标数据
            color: 散点颜色
            label: 图例标签
            alpha: 透明度
            s: 散点大小
        """
        if not MATPLOTLIB_AVAILABLE:
            return
        
        try:
            # 绘制散点
            self.ax.scatter(x_data, y_data, c=color, label=label, alpha=alpha, s=s)
            
            # 如果有标签，显示图例
            if label:
                self.ax.legend()
            
            # 刷新画布
            self.canvas.draw()
            
        except Exception as e:
            logger.error(f"绘制散点数据失败: {e}")
    
    def plot_multi_wavelength_data(self, scatter_data: Dict[str, Any], 
                                   field_index: int, wavelength_numbers: List[int]):
        """
        绘制多波长散点数据
        
        Args:
            scatter_data: 散点数据字典
            field_index: 视场索引
            wavelength_numbers: 波长编号列表
        """
        if not MATPLOTLIB_AVAILABLE:
            if hasattr(self, 'placeholder_label'):
                self.placeholder_label.setText("多波长点列图数据已计算完成\n需要安装matplotlib才能显示图形")
            return
        
        try:
            # 初始化散点图
            self.init_scatter()
            
            x_data = scatter_data.get('x_data')
            y_data = scatter_data.get('y_data')
            
            if x_data is None or y_data is None:
                logger.warning("散点数据为空")
                return
            
            # 定义颜色列表
            colors = ['b', 'r', 'g', 'c', 'm', 'y', 'k']
            
            # 绘制每个波长的数据
            for i, wave_num in enumerate(wavelength_numbers):
                if i >= len(colors):
                    color = colors[i % len(colors)]
                else:
                    color = colors[i]
                
                try:
                    # 获取当前波长的数据
                    wave_index = i  # 波长索引
                    if wave_index < x_data.shape[1]:
                        x_plot = np.squeeze(x_data[field_index, wave_index, :])
                        y_plot = np.squeeze(y_data[field_index, wave_index, :])
                        
                        # 过滤有效数据点
                        valid_mask = (x_plot != 0) | (y_plot != 0)
                        x_plot = x_plot[valid_mask]
                        y_plot = y_plot[valid_mask]
                        
                        if len(x_plot) > 0:
                            self.plot_scatter_data(
                                x_plot, y_plot,
                                color=color,
                                label=f"波长 {wave_num}",
                                alpha=0.6,
                                s=20
                            )
                
                except Exception as e:
                    logger.error(f"绘制波长{wave_num}数据失败: {e}")
            
            # 设置标题
            self.ax.set_title(f'Spot Diagram - Field {field_index + 1} (多波长)')
            
        except Exception as e:
            logger.error(f"绘制多波长散点数据失败: {e}")
    
    def setText(self, text: str):
        """
        设置显示文本（用于占位符模式）
        
        Args:
            text: 要显示的文本
        """
        if not MATPLOTLIB_AVAILABLE and hasattr(self, 'placeholder_label'):
            self.placeholder_label.setText(text)
