{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-09-11T12:16:27.316Z", "updatedAt": "2025-09-11T12:16:27.319Z", "resourceCount": 3}, "resources": [{"id": "motion-control-workflow", "source": "project", "protocol": "execution", "name": "Motion Control Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/motion-control-expert/execution/motion-control-workflow.execution.md", "metadata": {"createdAt": "2025-09-11T12:16:27.318Z", "updatedAt": "2025-09-11T12:16:27.318Z", "scannedAt": "2025-09-11T12:16:27.318Z", "path": "role/motion-control-expert/execution/motion-control-workflow.execution.md"}}, {"id": "motion-control-expert", "source": "project", "protocol": "role", "name": "Motion Control Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/motion-control-expert/motion-control-expert.role.md", "metadata": {"createdAt": "2025-09-11T12:16:27.318Z", "updatedAt": "2025-09-11T12:16:27.318Z", "scannedAt": "2025-09-11T12:16:27.318Z", "path": "role/motion-control-expert/motion-control-expert.role.md"}}, {"id": "motion-control-thinking", "source": "project", "protocol": "thought", "name": "Motion Control Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/motion-control-expert/thought/motion-control-thinking.thought.md", "metadata": {"createdAt": "2025-09-11T12:16:27.319Z", "updatedAt": "2025-09-11T12:16:27.319Z", "scannedAt": "2025-09-11T12:16:27.319Z", "path": "role/motion-control-expert/thought/motion-control-thinking.thought.md"}}], "stats": {"totalResources": 3, "byProtocol": {"execution": 1, "role": 1, "thought": 1}, "bySource": {"project": 3}}}