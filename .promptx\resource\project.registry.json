{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-28T13:37:20.913Z", "updatedAt": "2025-08-28T13:37:20.917Z", "resourceCount": 3}, "resources": [{"id": "motion-control-workflow", "source": "project", "protocol": "execution", "name": "Motion Control Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/motion-control-expert/execution/motion-control-workflow.execution.md", "metadata": {"createdAt": "2025-08-28T13:37:20.915Z", "updatedAt": "2025-08-28T13:37:20.915Z", "scannedAt": "2025-08-28T13:37:20.915Z", "path": "role/motion-control-expert/execution/motion-control-workflow.execution.md"}}, {"id": "motion-control-expert", "source": "project", "protocol": "role", "name": "Motion Control Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/motion-control-expert/motion-control-expert.role.md", "metadata": {"createdAt": "2025-08-28T13:37:20.916Z", "updatedAt": "2025-08-28T13:37:20.916Z", "scannedAt": "2025-08-28T13:37:20.916Z", "path": "role/motion-control-expert/motion-control-expert.role.md"}}, {"id": "motion-control-thinking", "source": "project", "protocol": "thought", "name": "Motion Control Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/motion-control-expert/thought/motion-control-thinking.thought.md", "metadata": {"createdAt": "2025-08-28T13:37:20.916Z", "updatedAt": "2025-08-28T13:37:20.916Z", "scannedAt": "2025-08-28T13:37:20.916Z", "path": "role/motion-control-expert/thought/motion-control-thinking.thought.md"}}], "stats": {"totalResources": 3, "byProtocol": {"execution": 1, "role": 1, "thought": 1}, "bySource": {"project": 3}}}