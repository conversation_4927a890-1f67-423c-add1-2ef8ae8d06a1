#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MTF分析器模块

基于ref/zpy_fftmtf.py的MTF分析功能，适配现有项目架构。
提供完整的Zemax OpticStudio FFT MTF分析功能，包括：
- 创建和运行FFT MTF分析
- 提取和处理MTF数据
- 支持切向和弧矢向MTF曲线分析
- 支持多视场MTF比较分析

主要类：
    - MtfAnalyzer: 主要的MTF分析类
    - MtfAnalysisError: MTF分析异常基类

作者: AI Assistant
版本: 1.0.0
兼容性: Python 3.6+, Zemax OpticStudio 2022+
"""

import os
import sys
import numpy as np
import logging
from typing import Dict, List, Tuple, Union, Any, Optional

from .exceptions import ZemaxBaseException
from .worker_threads import ZemaxApplication

# 配置日志
logger = logging.getLogger(__name__)


class MtfAnalysisError(ZemaxBaseException):
    """MTF分析时的一般错误。"""
    pass


class MtfCreationError(MtfAnalysisError):
    """创建MTF分析时的错误。"""
    pass


class MtfResultError(MtfAnalysisError):
    """获取MTF分析结果时的错误。"""
    pass


class MtfDataError(MtfAnalysisError):
    """提取MTF数据时的错误。"""
    pass


class MtfVisualizationError(MtfAnalysisError):
    """MTF可视化时的错误。"""
    pass


class MtfAnalyzer:
    """
    用于进行Zemax光学系统FFT MTF分析的类。
    提供创建MTF分析、读取结果、提取信息功能。
    """

    def __init__(self, zemax_app: Optional[ZemaxApplication] = None):
        """
        初始化MTF分析器。

        Args:
            zemax_app (Optional[ZemaxApplication]): Zemax应用程序实例，可以为None。

        Raises:
            MtfAnalysisError: 如果初始化失败。
        """
        try:
            # {{ AURA: Fix - 支持None参数，延迟ZOSAPI访问 }}
            # 保存Zemax应用程序实例
            self.zemax_app = zemax_app

            # 只有当zemax_app不为None时才访问其属性
            if zemax_app is not None:
                self.ZOSAPI = zemax_app.ZOSAPI
                self.TheApplication = zemax_app.TheApplication
                self.TheSystem = zemax_app.TheSystem
                logger.info("MTF分析器初始化成功（已连接Zemax）")
            else:
                self.ZOSAPI = None
                self.TheApplication = None
                self.TheSystem = None
                logger.info("MTF分析器初始化成功（未连接Zemax）")

            # 初始化MTF分析相关变量
            self.mtf_analysis = None
            self.mtf_results = None
            self.mtf_data = None

        except Exception as e:
            logger.error(f"MTF分析器初始化失败: {str(e)}")
            raise MtfAnalysisError(f"MTF分析器初始化失败: {str(e)}")

    def set_zemax_application(self, zemax_app: ZemaxApplication) -> None:
        """
        设置Zemax应用程序实例（用于延迟初始化）

        Args:
            zemax_app (ZemaxApplication): Zemax应用程序实例
        """
        try:
            self.zemax_app = zemax_app
            self.ZOSAPI = zemax_app.ZOSAPI
            self.TheApplication = zemax_app.TheApplication
            self.TheSystem = zemax_app.TheSystem
            logger.info("MTF分析器Zemax应用程序设置成功")
        except Exception as e:
            logger.error(f"设置MTF分析器Zemax应用程序失败: {str(e)}")
            raise MtfAnalysisError(f"设置MTF分析器Zemax应用程序失败: {str(e)}")

    def load_file(self, file_path: str, save_if_needed: bool = False) -> None:
        """
        加载Zemax文件。

        Args:
            file_path (str): Zemax文件路径。
            save_if_needed (bool, optional): 如果需要，是否保存当前文件。默认为False。

        Raises:
            FileNotFoundError: 如果文件不存在。
            MtfAnalysisError: 如果加载文件时出错。
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
                
            self.TheSystem.LoadFile(file_path, save_if_needed)
            logger.info(f"文件加载成功: {file_path}")
        except FileNotFoundError as e:
            logger.error(str(e))
            raise
        except Exception as e:
            logger.error(f"加载文件时出错: {str(e)}")
            raise MtfAnalysisError(f"加载文件时出错: {str(e)}")

    def create_fftmtf_analysis(self, 
                              field_number: int = 1, 
                              wavelength_number: int = 1,
                              surface_number: int = -1,  # -1表示像面
                              mtf_type: str = "Modulation",  # Modulation, Phase, SquareWave
                              sample_size: int = 64,  # 64, 128, 256, 512, 1024
                              show_diffraction_limit: bool = True,
                              use_dashes: bool = False,
                              use_polarization: bool = False,
                              maximum_frequency: float = 0.0) -> None:
        """
        创建FFT MTF分析。

        Args:
            field_number (int, optional): 视场编号，0表示所有视场。默认为1。
            wavelength_number (int, optional): 波长编号，0表示所有波长。默认为1。
            surface_number (int, optional): 表面编号，-1表示像面。默认为-1。
            mtf_type (str, optional): MTF类型，可选"Modulation"、"Phase"、"SquareWave"。默认为"Modulation"。
            sample_size (int, optional): 采样大小，支持64、128、256、512、1024。默认为64。
            show_diffraction_limit (bool, optional): 是否显示衍射极限。默认为True。
            use_dashes (bool, optional): 是否使用虚线。默认为False。
            use_polarization (bool, optional): 是否使用偏振。默认为False。
            maximum_frequency (float, optional): 最大频率，0表示自动。默认为0.0。

        Raises:
            MtfCreationError: 如果创建MTF分析时出错。
        """
        try:
            # 创建FFT MTF分析
            self.mtf_analysis = self.TheSystem.Analyses.New_Analysis(
                self.ZOSAPI.Analysis.AnalysisIDM.FftMtf)
            
            if self.mtf_analysis is None:
                raise MtfCreationError("无法创建FFT MTF分析")
            
            # 获取设置
            settings = self.mtf_analysis.GetSettings()
            
            # 设置视场
            if field_number == 0:
                settings.Field.UseAllFields()
            else:
                settings.Field.SetFieldNumber(field_number)
            
            # 设置波长
            if wavelength_number == 0:
                settings.Wavelength.UseAllWavelengths()
            else:
                settings.Wavelength.SetWavelengthNumber(wavelength_number)
            
            # 设置表面
            if surface_number == -1:
                settings.Surface.UseImageSurface()
            else:
                settings.Surface.SetSurfaceNumber(surface_number)
            
            # 设置MTF类型
            if mtf_type == "Modulation":
                settings.Type = self.ZOSAPI.Analysis.Settings.Mtf.MtfTypes.Modulation
            elif mtf_type == "Phase":
                settings.Type = self.ZOSAPI.Analysis.Settings.Mtf.MtfTypes.Phase
            elif mtf_type == "Real":
                settings.Type = self.ZOSAPI.Analysis.Settings.Mtf.MtfTypes.Real
            elif mtf_type == "Imaginary":
                settings.Type = self.ZOSAPI.Analysis.Settings.Mtf.MtfTypes.Imaginary
            elif mtf_type == "SquareWave":
                settings.Type = self.ZOSAPI.Analysis.Settings.Mtf.MtfTypes.SquareWave
            else:
                settings.Type = self.ZOSAPI.Analysis.Settings.Mtf.MtfTypes.Modulation
                logger.warning(f"不支持的MTF类型 {mtf_type}，已使用默认值Modulation")
            
            # 设置采样大小
            if sample_size == 64:
                settings.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_64x64
            elif sample_size == 128:
                settings.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_128x128
            elif sample_size == 256:
                settings.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_256x256
            elif sample_size == 512:
                settings.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_512x512
            elif sample_size == 1024:
                settings.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_1024x1024
            else:
                settings.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_64x64
                logger.warning(f"不支持的采样大小 {sample_size}，已使用默认值64x64")
            
            # 设置其他选项
            settings.ShowDiffractionLimit = show_diffraction_limit
            settings.UseDashes = use_dashes
            settings.UsePolarization = use_polarization
            
            # 设置最大频率（如果不为0）
            if maximum_frequency > 0:
                settings.MaximumFrequency = maximum_frequency
            
            logger.info("FFT MTF分析创建成功")
        except Exception as e:
            logger.error(f"创建FFT MTF分析时出错: {str(e)}")
            raise MtfCreationError(f"创建FFT MTF分析时出错: {str(e)}")

    def run_analysis(self) -> None:
        """
        运行FFT MTF分析。

        Raises:
            MtfCreationError: 如果运行MTF分析时出错。
        """
        try:
            if self.mtf_analysis is None:
                raise MtfCreationError("MTF分析未创建，请先调用create_fftmtf_analysis方法")
            
            # 运行分析并等待完成
            self.mtf_analysis.ApplyAndWaitForCompletion()
            logger.info("FFT MTF分析运行完成")
        except Exception as e:
            logger.error(f"运行FFT MTF分析时出错: {str(e)}")
            raise MtfCreationError(f"运行FFT MTF分析时出错: {str(e)}")

    def get_results(self) -> None:
        """
        获取FFT MTF分析结果。

        Raises:
            MtfResultError: 如果获取MTF分析结果时出错。
        """
        try:
            if self.mtf_analysis is None:
                raise MtfResultError("MTF分析未创建，请先调用create_fftmtf_analysis方法")
            
            # 获取分析结果
            self.mtf_results = self.mtf_analysis.GetResults()
            
            if self.mtf_results is None:
                raise MtfResultError("无法获取FFT MTF分析结果")
                
            logger.info("获取FFT MTF分析结果成功")
        except Exception as e:
            logger.error(f"获取FFT MTF分析结果时出错: {str(e)}")
            raise MtfResultError(f"获取FFT MTF分析结果时出错: {str(e)}")

    def extract_mtf_data(self) -> Dict[str, Any]:
        """
        提取MTF数据并结构化输出。

        Returns:
            Dict[str, Any]: 包含MTF数据的字典。

        Raises:
            MtfDataError: 如果提取MTF数据时出错。
        """
        try:
            if self.mtf_results is None:
                raise MtfDataError("MTF分析结果未获取，请先调用get_results方法")
            
            # 存储MTF数据的字典
            mtf_data = {}
            
            # 获取数据系列数量
            series_count = self.mtf_results.NumberOfDataSeries
            if series_count <= 0:
                raise MtfDataError("没有可用的MTF数据系列")
            
            mtf_data["数据系列数量"] = series_count
            mtf_data["数据系列"] = []
            
            # 提取每个数据系列
            for i in range(series_count):
                series = self.mtf_results.GetDataSeries(i)
                if series is None:
                    continue
                
                # 创建数据系列字典
                series_data = {}
                series_data["索引"] = i
                series_data["描述"] = series.Description if hasattr(series, "Description") else f"数据系列 {i}"
                
                # 获取X轴标签
                x_label = series.XLabel if hasattr(series, "XLabel") else "频率 (cycles/mm)"
                series_data["X轴标签"] = x_label
                
                # 获取X数据 (IVectorData类型)
                if hasattr(series, "XData") and series.XData is not None:
                    try:
                        # 使用Data属性获取真实数据
                        x_data = list(series.XData.Data)
                        series_data["X数据"] = x_data
                    except Exception as ex:
                        logger.warning(f"获取X数据时出错: {ex}")
                
                # 获取Y数据 (IMatrixData类型)
                if hasattr(series, "YData") and series.YData is not None:
                    try:
                        # 获取行数和列数
                        rows = series.YData.Rows
                        cols = series.YData.Cols
                        
                        # 创建Y数据矩阵
                        y_data = []
                        for r in range(rows):
                            row_data = []
                            for c in range(cols):
                                row_data.append(series.YData.GetValueAt(r, c))
                            y_data.append(row_data)
                        
                        series_data["Y数据"] = y_data
                        series_data["行数"] = rows
                        series_data["列数"] = cols
                    except Exception as ex:
                        logger.warning(f"获取Y数据时出错: {ex}")
                
                # 获取系列标签
                if hasattr(series, "SeriesLabels") and series.SeriesLabels is not None:
                    try:
                        # 检查SeriesLabels是否有Data属性
                        if hasattr(series.SeriesLabels, "Data"):
                            series_labels = list(series.SeriesLabels.Data)
                        else:
                            # 如果没有Data属性，尝试直接转换
                            series_labels = list(series.SeriesLabels)
                        series_data["系列标签"] = series_labels
                    except Exception as ex:
                        logger.warning(f"获取系列标签时出错: {ex}")
                
                # 获取系列数量
                if hasattr(series, "NumSeries"):
                    num_series = series.NumSeries
                    series_data["系列数量"] = num_series
                
                # 添加到数据系列列表
                mtf_data["数据系列"].append(series_data)
            
            # 保存结果供后续使用
            self.mtf_data = mtf_data
            logger.info(f"成功提取{series_count}个MTF数据系列")
            
            return mtf_data
        except Exception as e:
            logger.error(f"提取MTF数据时出错: {str(e)}")
            raise MtfDataError(f"提取MTF数据时出错: {str(e)}")

    def print_mtf_info(self) -> None:
        """
        打印MTF分析信息。

        Raises:
            MtfDataError: 如果MTF数据未提取。
        """
        try:
            if self.mtf_data is None:
                raise MtfDataError("MTF数据未提取，请先调用extract_mtf_data方法")

            logger.info("======= FFT MTF分析信息 =======")
            logger.info(f"数据系列数量: {self.mtf_data['数据系列数量']}")

            # 打印每个数据系列的信息
            for i, series in enumerate(self.mtf_data["数据系列"]):
                logger.info(f"数据系列 {i+1}:")
                logger.info(f"  描述: {series['描述']}")
                logger.info(f"  X轴标签: {series['X轴标签']}")

                if "系列标签" in series:
                    logger.info(f"  系列标签: {', '.join(series['系列标签'])}")

                if "系列数量" in series:
                    logger.info(f"  系列数量: {series['系列数量']}")

                if "行数" in series and "列数" in series:
                    logger.info(f"  数据维度: {series['行数']}×{series['列数']}")

            logger.info("==============================")
        except Exception as e:
            logger.error(f"打印MTF信息时出错: {str(e)}")
            raise MtfDataError(f"打印MTF信息时出错: {str(e)}")

    def get_mtf_at_frequency(self, frequency: float) -> Dict[int, float]:
        """
        获取指定空间频率对应的MTF值。

        Args:
            frequency (float): 指定的空间频率。

        Returns:
            Dict[int, float]: MTF值，键为系列索引，值为对应的MTF值。

        Raises:
            MtfDataError: 如果数据未提取或频率无效。
        """
        try:
            if self.mtf_data is None:
                raise MtfDataError("MTF数据未提取")

            results = {}
            for series_idx, series in enumerate(self.mtf_data["数据系列"]):
                if "X数据" not in series or "Y数据" not in series:
                    logger.warning(f"数据系列 {series_idx} 缺少X或Y数据，已跳过")
                    continue

                x_data = series["X数据"]
                y_data = np.array(series["Y数据"])

                # 查找与指定频率最接近的索引
                closest_index = (np.abs(np.array(x_data) - frequency)).argmin()
                results[series_idx] = y_data[closest_index]  # 提取对应的MTF值

            return results
        except Exception as e:
            logger.error(f"获取MTF值时出错: {str(e)}")
            raise MtfDataError(f"获取MTF值时出错: {str(e)}")

    def save_results(self, file_path: str) -> None:
        """
        保存MTF分析结果到文件。

        Args:
            file_path (str): 保存文件路径。

        Raises:
            MtfResultError: 如果保存MTF分析结果时出错。
        """
        try:
            if self.mtf_results is None:
                raise MtfResultError("MTF分析结果未获取，请先调用get_results方法")

            # 保存结果
            self.mtf_results.GetTextFile(file_path)
            logger.info(f"MTF分析结果已保存到: {file_path}")
        except Exception as e:
            logger.error(f"保存MTF分析结果时出错: {str(e)}")
            raise MtfResultError(f"保存MTF分析结果时出错: {str(e)}")

    def close(self) -> None:
        """
        关闭MTF分析并释放资源。
        """
        try:
            # 关闭分析
            if hasattr(self, 'mtf_analysis') and self.mtf_analysis is not None:
                self.mtf_analysis.Close()
                self.mtf_analysis = None

            # 释放结果
            if hasattr(self, 'mtf_results'):
                self.mtf_results = None

            # 释放数据
            if hasattr(self, 'mtf_data'):
                self.mtf_data = None

            logger.info("MTF分析已关闭")
        except Exception as e:
            logger.error(f"关闭MTF分析时出错: {str(e)}")

    def __del__(self):
        """
        析构函数，确保资源被正确释放。
        """
        self.close()
