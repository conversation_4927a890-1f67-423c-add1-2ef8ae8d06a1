#!/usr/bin/env python3
"""
测试URDF变换计算的脚本
"""

import sys
import os
import numpy as np

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from urdf_viewer_standalone import SimpleURDFParser

def test_urdf_transforms():
    """测试URDF变换计算"""
    print("Testing URDF Transform Calculations")
    print("=" * 50)
    
    # 创建解析器
    parser = SimpleURDFParser()
    
    # 解析URDF文件
    urdf_file = "../aubo_i5.urdf"
    if not os.path.exists(urdf_file):
        print(f"URDF file not found: {urdf_file}")
        return False
    
    success = parser.parse_urdf(urdf_file)
    if not success:
        print("Failed to parse URDF file")
        return False
    
    print(f"Successfully parsed URDF: {parser.robot_name}")
    print(f"Links: {len(parser.links)}")
    print(f"Joints: {len(parser.joints)}")
    print()
    
    # 显示关节信息
    print("Joint Information:")
    print("-" * 30)
    for joint in parser.joints:
        print(f"Joint: {joint['name']}")
        print(f"  Type: {joint['type']}")
        print(f"  Parent: {joint['parent']}")
        print(f"  Child: {joint['child']}")
        print(f"  Origin XYZ: {joint['origin']['xyz']}")
        print(f"  Origin RPY: {joint['origin']['rpy']}")
        print()
    
    # 测试变换链计算
    print("Transform Chain Calculations:")
    print("-" * 30)
    for link in parser.links:
        link_name = link['name']
        transform_chain = parser.get_transform_chain(link_name)
        
        print(f"Link: {link_name}")
        print(f"  Transform chain length: {len(transform_chain)}")
        
        if transform_chain:
            cumulative_transform = parser.compute_cumulative_transform(transform_chain)
            translation = cumulative_transform[:3, 3]
            print(f"  Final position: [{translation[0]:.3f}, {translation[1]:.3f}, {translation[2]:.3f}]")
        else:
            print(f"  Final position: [0.000, 0.000, 0.000] (root link)")
        print()
    
    return True

def test_transform_matrix():
    """测试变换矩阵计算"""
    print("Testing Transform Matrix Calculation")
    print("=" * 50)
    
    parser = SimpleURDFParser()
    
    # 测试简单变换
    xyz = [1.0, 2.0, 3.0]
    rpy = [0.0, 0.0, np.pi/2]  # 90度绕Z轴旋转
    
    transform = parser.create_transform_matrix(xyz, rpy)
    
    print("Test transform:")
    print(f"XYZ: {xyz}")
    print(f"RPY: {rpy}")
    print("Transform matrix:")
    print(transform)
    print()
    
    # 验证平移部分
    expected_translation = np.array(xyz)
    actual_translation = transform[:3, 3]
    
    print(f"Expected translation: {expected_translation}")
    print(f"Actual translation: {actual_translation}")
    print(f"Translation correct: {np.allclose(expected_translation, actual_translation)}")
    print()
    
    return True

if __name__ == "__main__":
    print("URDF Transform Test Suite")
    print("=" * 50)
    
    # 测试变换矩阵计算
    test_transform_matrix()
    
    # 测试URDF变换计算
    test_urdf_transforms()
    
    print("Test completed!")
