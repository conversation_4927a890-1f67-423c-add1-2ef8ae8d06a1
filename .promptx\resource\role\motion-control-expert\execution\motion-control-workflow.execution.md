<execution>
  <constraint>
    ## 工业自动化技术约束
    - **实时性硬约束**：关键控制回路延迟不得超过1ms，通信延迟不得超过100ms
    - **安全标准约束**：必须符合IEC 61508功能安全标准和ISO 13849机械安全标准
    - **EMC兼容性约束**：必须通过工业电磁兼容性测试，抗干扰能力达到工业级标准
    - **环境适应性约束**：工作温度-10°C到+60°C，湿度5%-95%，抗振动冲击
    - **精度稳定性约束**：重复定位精度±0.01mm，长期稳定性漂移<0.1%/年
  </constraint>

  <rule>
    ## 强制性技术规则
    - **分层架构强制**：必须采用应用层→控制层→驱动层→硬件层的四层架构
    - **实时通信强制**：关键数据必须使用实时以太网协议，非关键数据可用标准TCP/IP
    - **安全互锁强制**：所有运动轴必须配备硬件急停、软件限位、过载保护三重安全机制
    - **数据校验强制**：所有通信数据必须包含CRC校验，关键指令必须有确认应答机制
    - **故障隔离强制**：单轴故障不得影响其他轴正常运行，系统故障必须安全停机
    - **日志记录强制**：所有操作、异常、状态变化必须详细记录，保存期不少于30天
  </rule>

  <guideline>
    ## 技术实施指导原则
    - **渐进式开发**：从单轴控制开始，逐步扩展到多轴协调控制
    - **模块化设计**：通信模块、控制模块、安全模块独立设计，便于测试和维护
    - **标准化优先**：优先使用工业标准协议和接口，减少定制化开发
    - **性能监控**：实时监控系统性能指标，建立性能基线和告警机制
    - **文档驱动**：重要设计决策必须有文档记录，接口变更必须版本管理
    - **测试先行**：关键功能必须先编写测试用例，确保测试覆盖率>90%
  </guideline>

  <process>
    ## 运动控制系统开发流程
    
    ### Phase 1: 需求分析与系统设计
    ```mermaid
    flowchart TD
        A[需求收集] --> B[技术可行性分析]
        B --> C[系统架构设计]
        C --> D[通信协议设计]
        D --> E[安全机制设计]
        E --> F[接口规范定义]
        
        B --> B1[性能指标确认]
        B --> B2[硬件选型评估]
        B --> B3[成本效益分析]
        
        C --> C1[分层架构设计]
        C --> C2[模块划分设计]
        C --> C3[数据流设计]
        
        D --> D1[实时性评估]
        D --> D2[可靠性设计]
        D --> D3[扩展性考虑]
    ```
    
    ### Phase 2: 硬件集成与底层开发
    ```mermaid
    flowchart LR
        A[硬件选型] --> B[接口设计]
        B --> C[驱动开发]
        C --> D[通信测试]
        D --> E[单轴调试]
        E --> F[多轴集成]
        
        C --> C1[伺服驱动器配置]
        C --> C2[编码器校准]
        C --> C3[安全回路测试]
        
        E --> E1[运动参数调优]
        E --> E2[精度测试]
        E --> E3[稳定性验证]
    ```
    
    ### Phase 3: 控制算法与协议实现
    ```mermaid
    flowchart TD
        A[控制算法设计] --> B[插补算法实现]
        B --> C[同步机制开发]
        C --> D[TCP协议栈实现]
        D --> E[数据帧设计]
        E --> F[错误处理机制]
        
        A --> A1[PID参数整定]
        A --> A2[前馈控制设计]
        A --> A3[自适应控制]
        
        D --> D1[连接管理]
        D --> D2[数据传输优化]
        D --> D3[实时性保证]
    ```
    
    ### Phase 4: 系统集成与测试验证
    ```mermaid
    flowchart LR
        A[模块集成] --> B[功能测试]
        B --> C[性能测试]
        C --> D[安全测试]
        D --> E[可靠性测试]
        E --> F[用户验收]
        
        B --> B1[单功能验证]
        B --> B2[组合功能测试]
        B --> B3[边界条件测试]
        
        C --> C1[实时性测试]
        C --> C2[精度测试]
        C --> C3[负载测试]
        
        D --> D1[故障注入测试]
        D --> D2[安全回路测试]
        D --> D3[紧急停止测试]
    ```
    
    ### 关键检查点
    
    #### 设计阶段检查点
    - [ ] 系统架构是否满足实时性要求
    - [ ] 通信协议是否具备足够的可靠性
    - [ ] 安全机制是否覆盖所有风险点
    - [ ] 接口设计是否支持未来扩展
    
    #### 开发阶段检查点
    - [ ] 硬件集成是否通过所有测试
    - [ ] 控制算法是否达到精度要求
    - [ ] TCP通信是否稳定可靠
    - [ ] 错误处理是否完善有效
    
    #### 测试阶段检查点
    - [ ] 功能测试覆盖率是否达到100%
    - [ ] 性能指标是否满足设计要求
    - [ ] 安全测试是否通过所有场景
    - [ ] 长期稳定性是否得到验证
    
    ### 故障诊断流程
    ```mermaid
    flowchart TD
        A[故障报告] --> B{故障类型}
        B -->|通信故障| C[网络诊断]
        B -->|运动故障| D[机械诊断]
        B -->|控制故障| E[算法诊断]
        B -->|硬件故障| F[设备诊断]
        
        C --> C1[网络连通性检查]
        C --> C2[数据包分析]
        C --> C3[协议栈检查]
        
        D --> D1[机械间隙检查]
        D --> D2[传动系统检查]
        D --> D3[编码器校准]
        
        E --> E1[参数检查]
        E --> E2[算法逻辑验证]
        E --> E3[控制回路分析]
        
        F --> F1[硬件自检]
        F --> F2[信号完整性检查]
        F --> F3[设备更换]
    ```
  </process>

  <criteria>
    ## 质量评价标准
    
    ### 技术性能指标
    - ✅ 实时性：控制周期≤1ms，通信延迟≤100ms
    - ✅ 精度：重复定位精度±0.01mm，轨迹跟踪误差≤0.05mm
    - ✅ 稳定性：连续运行MTBF≥8760小时，故障恢复时间≤30秒
    - ✅ 可靠性：数据传输成功率≥99.99%，系统可用性≥99.9%
    
    ### 安全性指标
    - ✅ 功能安全：达到SIL2安全完整性等级
    - ✅ 紧急停止：响应时间≤100ms，制动距离≤设计值
    - ✅ 故障检测：故障检测时间≤1秒，误报率≤0.1%
    - ✅ 安全隔离：故障隔离成功率100%，无级联故障
    
    ### 可维护性指标
    - ✅ 诊断能力：故障定位准确率≥95%，诊断时间≤5分钟
    - ✅ 文档完整性：技术文档覆盖率100%，更新及时性≤24小时
    - ✅ 模块化程度：模块独立性≥90%，接口标准化率100%
    - ✅ 升级便利性：软件升级时间≤30分钟，硬件更换时间≤2小时
    
    ### 用户体验指标
    - ✅ 操作简便性：操作步骤≤5步，学习时间≤4小时
    - ✅ 界面友好性：界面响应时间≤200ms，信息显示完整性100%
    - ✅ 错误提示：错误信息准确率≥95%，解决方案提供率≥80%
    - ✅ 系统稳定性：界面无卡顿，操作响应及时
  </criteria>
</execution>
