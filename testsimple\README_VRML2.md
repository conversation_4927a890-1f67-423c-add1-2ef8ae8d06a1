# VRML 2.0 查看器使用说明

## 功能描述

`vrml2_viewer.py` 是一个基于VTK原生支持的VRML 2.0文件查看器，支持交互式3D模型显示。

## 特性

- ✅ 使用VTK原生vtkVRMLImporter（支持VRML 2.0完整特性）
- ✅ 命令行交互式文件输入
- ✅ 支持Qt界面和VTK原生窗口两种显示模式
- ✅ 完整的3D交互操作（旋转、缩放、平移）
- ✅ 自动相机定位和场景适配
- ✅ 详细的日志输出和错误处理

## 系统要求

### 必需依赖
- Python 3.6+
- VTK (推荐9.0+)

### 可选依赖
- PyQt5 或 PyQt6 (用于Qt界面)
- VTK-Qt集成模块

## 安装依赖

```bash
# 基础依赖
pip install vtk

# Qt界面支持（可选）
pip install PyQt5
# 或
pip install PyQt6
```

## 使用方法

### 1. 启动程序

```bash
cd testsimple
python vrml2_viewer.py
```

### 2. 输入文件路径

程序启动后会提示输入VRML 2.0文件路径：

```
请输入VRML 2.0文件路径: your_model.wrl
```

支持的输入格式：
- 相对路径：`../data/model.wrl`
- 绝对路径：`C:\path\to\model.wrl`
- 带引号的路径：`"path with spaces.wrl"`

### 3. 3D交互操作

文件加载成功后，会打开3D查看窗口：

**鼠标操作：**
- 🖱️ **左键拖拽**：旋转模型
- 🖱️ **右键拖拽**：缩放模型
- 🖱️ **中键拖拽**：平移模型

**键盘操作：**
- ⌨️ **'q'键**：退出程序
- ⌨️ **关闭窗口**：退出程序

### 4. 退出程序

在文件输入阶段，可以输入以下命令退出：
- `quit`
- `exit`
- `q`
- `Ctrl+C`

## 支持的文件格式

- `.wrl` - VRML 2.0文件
- `.vrml` - VRML文件

## 界面模式

### Qt界面模式（推荐）
- 现代化的窗口界面
- 更好的用户体验
- 需要PyQt5/PyQt6和VTK-Qt集成

### VTK原生窗口模式
- 基础的3D显示窗口
- 无需额外依赖
- 作为Qt界面的备选方案

## 示例用法

```bash
# 启动程序
python vrml2_viewer.py

# 输入示例
请输入VRML 2.0文件路径: ../data/ZF/meshes/model.wrl
```

## 故障排除

### 1. VTK导入失败
```
错误：VTK导入失败
解决：pip install vtk
```

### 2. Qt界面不可用
```
提示：VTK-Qt集成不可用，将使用VTK原生窗口
解决：pip install PyQt5（可选，不影响基本功能）
```

### 3. 文件加载失败
```
错误：未导入任何3D对象
原因：文件可能不是有效的VRML 2.0格式
解决：检查文件格式，确保是VRML 2.0标准
```

### 4. 显示异常
```
问题：模型显示不完整或位置异常
解决：程序会自动调整相机位置，如仍有问题请检查模型文件
```

## 技术特性

### VTK原生支持
- 使用`vtk.vtkVRMLImporter`进行文件导入
- 完整支持VRML 2.0规范
- 自动处理材质、纹理、光照等

### 错误处理
- 文件存在性检查
- 格式验证
- 详细的错误日志
- 优雅的异常处理

### 性能优化
- 自动相机定位
- 高效的渲染管线
- 内存管理和资源清理

## 开发信息

- **文件位置**：`testsimple/vrml2_viewer.py`
- **开发语言**：Python 3
- **图形库**：VTK
- **界面框架**：PyQt5/PyQt6（可选）
- **日志级别**：INFO

## 版本历史

- **v1.0**：初始版本，支持VRML 2.0文件查看和基本3D交互
