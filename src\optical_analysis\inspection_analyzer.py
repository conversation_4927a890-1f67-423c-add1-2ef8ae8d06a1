#!/usr/bin/env python3
"""
Inspection分析器 - 基于OpticalRealSim项目的Zernike系数处理功能

主要功能:
1. 导入和解析Zernike系数数据文件(.dat格式)
2. 将Zernike系数应用到Zemax光学系统中
3. 提供实测数据与仿真数据的对比分析
4. 支持多个镜面的Zernike系数管理

作者: AI Assistant
基于: OpticalRealSim项目的addRealZernike功能
"""

import os
import logging
from typing import List, Dict, Optional, Tuple
import numpy as np

logger = logging.getLogger(__name__)

class ZernikeData:
    """Zernike系数数据结构"""
    
    def __init__(self, surface_id: int, norm_radius: float, coefficients: List[float]):
        """
        初始化Zernike数据
        
        Args:
            surface_id: 镜面ID
            norm_radius: 归一化半径
            coefficients: 37项Zernike系数
        """
        self.surface_id = surface_id
        self.norm_radius = norm_radius
        self.coefficients = coefficients
        
        if len(coefficients) != 37:
            raise ValueError(f"Zernike系数必须是37项，当前为{len(coefficients)}项")
    
    def __str__(self):
        return f"ZernikeData(surface_id={self.surface_id}, norm_radius={self.norm_radius:.6f}, coefficients={len(self.coefficients)}项)"

class InspectionAnalyzer:
    """
    Inspection分析器
    
    基于OpticalRealSim项目的实测数据处理功能，提供Zernike系数的导入、管理和应用功能
    """
    
    def __init__(self, zemax_app=None):
        """
        初始化Inspection分析器
        
        Args:
            zemax_app: Zemax应用程序实例
        """
        self.zemax_app = zemax_app
        self.zernike_data_list: List[ZernikeData] = []
        self.logger = logging.getLogger(__name__)
        
    def set_zemax_application(self, zemax_app):
        """设置Zemax应用程序实例"""
        self.zemax_app = zemax_app
        self.logger.info("Zemax应用程序实例已设置")
    
    def load_zernike_file(self, file_path: str) -> ZernikeData:
        """
        加载Zernike系数文件 (基于OpticalRealSim的addRealZernike方法)
        
        Args:
            file_path: .dat文件路径
            
        Returns:
            ZernikeData: 解析后的Zernike数据
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 文件格式错误
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as file:
                lines = file.readlines()
            
            # 检查文件格式 - 必须是39行
            if len(lines) != 39:
                raise ValueError(f"文件格式错误，要求39行，实际{len(lines)}行")
            
            # 读取第一行和第二行
            zernike_num = float(lines[0].strip())
            norm_radius = float(lines[1].strip())
            
            # 验证Zernike项数
            if zernike_num != 37:
                raise ValueError(f"文件格式错误，应该使用37项Zernike，实际{zernike_num}项")
            
            # 遍历读取剩余的37行系数
            zernike_coefficients = [float(line.strip()) for line in lines[2:]]
            
            # 创建ZernikeData对象 (默认surface_id为0，后续可以修改)
            zernike_data = ZernikeData(
                surface_id=0,
                norm_radius=norm_radius,
                coefficients=zernike_coefficients
            )
            
            self.logger.info(f"成功加载Zernike文件: {file_path}")
            self.logger.info(f"归一化半径: {norm_radius:.6f}")
            self.logger.info(f"Zernike系数: {len(zernike_coefficients)}项")
            
            return zernike_data
            
        except Exception as e:
            self.logger.error(f"加载Zernike文件失败: {e}")
            raise
    
    def add_zernike_data(self, zernike_data: ZernikeData) -> bool:
        """
        添加Zernike数据到管理列表
        
        Args:
            zernike_data: Zernike数据对象
            
        Returns:
            bool: 添加成功返回True
        """
        try:
            self.zernike_data_list.append(zernike_data)
            self.logger.info(f"添加Zernike数据: {zernike_data}")
            return True
        except Exception as e:
            self.logger.error(f"添加Zernike数据失败: {e}")
            return False
    
    def remove_zernike_data(self, index: int) -> bool:
        """
        移除指定索引的Zernike数据
        
        Args:
            index: 数据索引
            
        Returns:
            bool: 移除成功返回True
        """
        try:
            if 0 <= index < len(self.zernike_data_list):
                removed_data = self.zernike_data_list.pop(index)
                self.logger.info(f"移除Zernike数据: {removed_data}")
                return True
            else:
                self.logger.warning(f"无效的索引: {index}")
                return False
        except Exception as e:
            self.logger.error(f"移除Zernike数据失败: {e}")
            return False
    
    def clear_all_zernike_data(self):
        """清空所有Zernike数据"""
        self.zernike_data_list.clear()
        self.logger.info("已清空所有Zernike数据")
    
    def get_zernike_data_list(self) -> List[ZernikeData]:
        """获取所有Zernike数据列表"""
        return self.zernike_data_list.copy()
    
    def update_surface_id(self, index: int, surface_id: int) -> bool:
        """
        更新指定Zernike数据的镜面ID
        
        Args:
            index: 数据索引
            surface_id: 新的镜面ID
            
        Returns:
            bool: 更新成功返回True
        """
        try:
            if 0 <= index < len(self.zernike_data_list):
                self.zernike_data_list[index].surface_id = surface_id
                self.logger.info(f"更新镜面ID: 索引{index} -> 镜面{surface_id}")
                return True
            else:
                self.logger.warning(f"无效的索引: {index}")
                return False
        except Exception as e:
            self.logger.error(f"更新镜面ID失败: {e}")
            return False
    
    def apply_zernike_to_zemax(self) -> bool:
        """
        将所有Zernike数据应用到Zemax系统中 (基于OpticalRealSim的updateRealZernike方法)
        
        Returns:
            bool: 应用成功返回True
        """
        try:
            if not self.zemax_app:
                raise ValueError("Zemax应用程序未连接")
            
            if not self.zernike_data_list:
                self.logger.warning("没有Zernike数据需要应用")
                return False
            
            # 获取Zemax系统的LDE (Lens Data Editor)
            the_system = self.zemax_app.TheSystem
            the_lde = the_system.LDE
            
            # 遍历所有Zernike数据并应用到对应的镜面
            for i, zernike_data in enumerate(self.zernike_data_list):
                surface_id = zernike_data.surface_id
                
                self.logger.info(f"应用Zernike数据到镜面 {surface_id}")
                
                # 获取镜面并设置为Zernike Fringe Phase类型
                surface = the_lde.GetSurfaceAt(surface_id)
                
                # 导入ZOSAPI模块 (需要在运行时导入)
                import clr
                clr.AddReference("ZOSAPI_Interfaces")
                import ZOSAPI
                
                # 设置镜面类型为Zernike Fringe Phase
                surface_type_settings = surface.GetSurfaceTypeSettings(ZOSAPI.Editors.LDE.SurfaceType.ZernikeFringePhase)
                surface.ChangeType(surface_type_settings)
                
                # 设置Zernike项数为37
                surface.GetSurfaceCell(ZOSAPI.Editors.LDE.SurfaceColumn.Par13).Value = str(37)
                
                # 设置37项Zernike系数 (Par14到Par50)
                for j, coefficient in enumerate(zernike_data.coefficients):
                    param_column = ZOSAPI.Editors.LDE.SurfaceColumn.Par13 + j + 1
                    surface.GetSurfaceCell(param_column).Value = str(coefficient)
                
                self.logger.info(f"镜面 {surface_id} Zernike系数应用完成")
            
            self.logger.info(f"所有Zernike数据应用完成，共处理{len(self.zernike_data_list)}个镜面")
            return True
            
        except Exception as e:
            self.logger.error(f"应用Zernike数据到Zemax失败: {e}")
            return False
    
    def get_available_surfaces(self) -> List[int]:
        """
        获取Zemax系统中可用的镜面ID列表
        
        Returns:
            List[int]: 镜面ID列表
        """
        try:
            if not self.zemax_app:
                return []
            
            the_system = self.zemax_app.TheSystem
            the_lde = the_system.LDE
            num_surfaces = the_lde.NumberOfSurfaces
            
            return list(range(num_surfaces))
            
        except Exception as e:
            self.logger.error(f"获取镜面列表失败: {e}")
            return []
    
    def get_analysis_summary(self) -> Dict:
        """
        获取分析摘要信息
        
        Returns:
            Dict: 包含分析摘要的字典
        """
        summary = {
            'total_zernike_data': len(self.zernike_data_list),
            'zemax_connected': self.zemax_app is not None,
            'available_surfaces': len(self.get_available_surfaces()) if self.zemax_app else 0,
            'data_details': []
        }
        
        for i, data in enumerate(self.zernike_data_list):
            detail = {
                'index': i,
                'surface_id': data.surface_id,
                'norm_radius': data.norm_radius,
                'rms_value': np.sqrt(np.mean(np.array(data.coefficients)**2)),  # RMS of coefficients
                'max_coefficient': max(abs(c) for c in data.coefficients)
            }
            summary['data_details'].append(detail)
        
        return summary
