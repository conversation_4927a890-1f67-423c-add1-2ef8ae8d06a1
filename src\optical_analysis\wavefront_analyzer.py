#!/usr/bin/env python3
"""
25AutoAssembly 波前分析器

基于OpticalRealSim和zpy_wavefront.py的实现，提供波前数据计算和分析功能。
严格参考OpticalRealSim-main/src/UI_OpticalRealSim.py的update_wavefront_map方法。

主要功能：
- 波前数据计算和获取
- 参数设置和配置
- 数据处理和转换
- 与Zemax API的交互

作者: AI Assistant
版本: 1.0.0
"""

import logging
import numpy as np
from typing import Optional, Dict, Any, Tuple
from PyQt5.QtCore import QObject, pyqtSignal

from .worker_threads import ZemaxApplication
from .exceptions import ZemaxBaseException

# 配置日志
logger = logging.getLogger(__name__)


class WavefrontAnalysisError(ZemaxBaseException):
    """波前分析错误"""
    pass


class WavefrontAnalyzer(QObject):
    """
    波前分析器
    
    基于OpticalRealSim的update_wavefront_map方法实现，提供完整的波前分析功能。
    严格参考OpticalRealSim-main/src/UI_OpticalRealSim.py的实现方式。
    """
    
    # 信号定义
    analysis_started = pyqtSignal()
    analysis_progress = pyqtSignal(str)
    analysis_completed = pyqtSignal(dict)
    analysis_failed = pyqtSignal(str)
    
    def __init__(self, zemax_app: Optional[ZemaxApplication] = None):
        super().__init__()
        
        self.zemax_app = zemax_app
        self.wavefront_analysis = None
        self.wavefront_results = None
        self.current_data = None
        
        logger.info("WavefrontAnalyzer初始化完成")
    
    def set_zemax_application(self, zemax_app: ZemaxApplication):
        """设置Zemax应用实例"""
        self.zemax_app = zemax_app
        logger.info("Zemax应用实例已设置")
    
    def analyze_wavefront(self, 
                         sampling: str = "128x128",
                         rotation: int = 0,
                         wavelength_index: int = 0,
                         field_index: int = 0) -> Optional[Dict[str, Any]]:
        """
        执行波前分析（严格参考OpticalRealSim的update_wavefront_map实现）
        
        Args:
            sampling: 采样大小 ('32x32', '64x64', '128x128', '256x256', '512x512')
            rotation: 旋转角度 (0, 90, 180, 270)
            wavelength_index: 波长索引
            field_index: 视场索引
            
        Returns:
            分析结果字典，包含波前数据和相关信息
        """
        if not self.zemax_app:
            error_msg = "Zemax应用程序对象未设置，无法执行波前分析"
            logger.error(error_msg)
            self.analysis_failed.emit(error_msg)
            return None
        
        try:
            self.analysis_started.emit()
            self.analysis_progress.emit("开始波前分析...")
            
            # {{ AURA: Add - 严格参考OpticalRealSim的波前分析创建方式 }}
            # 创建波前分析
            self.analysis_progress.emit("创建波前分析...")
            ZOSAPI = self.zemax_app.ZOSAPI
            TheSystem = self.zemax_app.TheSystem
            
            self.wavefront_analysis = TheSystem.Analyses.New_Analysis(ZOSAPI.Analysis.AnalysisIDM.WavefrontMap)
            
            # {{ AURA: Add - 获取设置API并配置参数 }}
            # 获取设置API
            wavefront_setting = self.wavefront_analysis.GetSettings()
            
            # {{ AURA: Add - 从参数设置采样大小 }}
            # 设置采样大小（严格参考OpticalRealSim的实现）
            self.analysis_progress.emit(f"设置采样大小: {sampling}")
            if sampling == '32x32':
                wavefront_setting.Sampling = ZOSAPI.Analysis.SampleSizes.S_32x32
            elif sampling == '64x64':
                wavefront_setting.Sampling = ZOSAPI.Analysis.SampleSizes.S_64x64
            elif sampling == '128x128':
                wavefront_setting.Sampling = ZOSAPI.Analysis.SampleSizes.S_128x128
            elif sampling == '256x256':
                wavefront_setting.Sampling = ZOSAPI.Analysis.SampleSizes.S_256x256
            elif sampling == '512x512':
                wavefront_setting.Sampling = ZOSAPI.Analysis.SampleSizes.S_512x512
            else:
                # 默认使用128x128
                wavefront_setting.Sampling = ZOSAPI.Analysis.SampleSizes.S_128x128
                logger.warning(f"未知的采样大小 {sampling}，使用默认值 128x128")
            
            # {{ AURA: Fix - 修复索引传递错误，UI索引需要+1才能正确对应Zemax API }}
            # 设置波长和视场（修复索引映射问题）
            # UI显示"1,2,3"但currentIndex()返回0,1,2，需要+1才能正确对应Zemax API中的1,2,3
            zemax_wavelength_number = wavelength_index + 1
            zemax_field_number = field_index + 1
            self.analysis_progress.emit(f"设置波长编号: {zemax_wavelength_number}, 视场编号: {zemax_field_number}")
            wavefront_setting.Wavelength.SetWavelengthNumber(zemax_wavelength_number)
            wavefront_setting.Field.SetFieldNumber(zemax_field_number)
            
            # {{ AURA: Add - 执行分析计算 }}
            # 根据更新后的参数重新计算
            self.analysis_progress.emit("执行波前计算...")
            self.wavefront_analysis.ApplyAndWaitForCompletion()
            
            # {{ AURA: Add - 获取计算结果 }}
            # 获取计算结果
            self.analysis_progress.emit("获取分析结果...")
            self.wavefront_results = self.wavefront_analysis.GetResults()
            wavefront_values = self.wavefront_results.GetDataGrid(0).Values
            
            # {{ AURA: Add - 数据转换和处理 }}
            # 将.NET数组转换为numpy数组（参考OpticalRealSim的reshape方法）
            self.analysis_progress.emit("处理波前数据...")
            wavefront_npdata = self._reshape_net_array(wavefront_values, 
                                                     wavefront_values.GetLength(0),
                                                     wavefront_values.GetLength(1))
            
            # {{ AURA: Add - 计算PV和RMS值 }}
            # 计算PV RMS（参考OpticalRealSim的实现）
            pv_value = self._calculate_pv(wavefront_values)
            rms_value = self._calculate_rms(wavefront_npdata)
            
            # {{ AURA: Add - 构建结果字典 }}
            # 构建分析结果
            result = {
                'wavefront_data': wavefront_npdata,
                'pv_value': pv_value,
                'rms_value': rms_value,
                'sampling': sampling,
                'rotation': rotation,
                'wavelength_index': wavelength_index,
                'field_index': field_index,
                'data_shape': wavefront_npdata.shape,
                'success': True
            }
            
            self.current_data = result
            self.analysis_progress.emit("波前分析完成")
            self.analysis_completed.emit(result)
            
            logger.info(f"波前分析完成，数据形状: {wavefront_npdata.shape}, PV: {pv_value:.6f}")
            return result
            
        except Exception as e:
            error_msg = f"波前分析失败: {str(e)}"
            logger.error(error_msg)
            self.analysis_failed.emit(error_msg)
            return None
    
    def _reshape_net_array(self, net_array, rows: int, cols: int) -> np.ndarray:
        """
        将.NET数组转换为numpy数组（参考OpticalRealSim的reshape实现）
        
        Args:
            net_array: .NET数组对象
            rows: 行数
            cols: 列数
            
        Returns:
            numpy数组
        """
        try:
            # 创建numpy数组
            np_array = np.zeros((rows, cols))
            
            # 复制数据
            for i in range(rows):
                for j in range(cols):
                    np_array[i, j] = net_array[i, j]
            
            return np_array
            
        except Exception as e:
            logger.error(f"数组转换失败: {e}")
            # 返回默认数组
            return np.zeros((rows, cols))
    
    def _calculate_pv(self, np_array: np.ndarray) -> float:
        """
        计算PV值（Peak-to-Valley）

        Args:
            np_array: numpy数组

        Returns:
            PV值（波长单位）
        """
        try:
            # {{ AURA: Fix - 使用numpy方法替代.NET风格的API }}
            # 过滤掉NaN和无效值
            if np_array is None or np_array.size == 0:
                logger.warning("输入数组为空，返回PV=0")
                return 0.0

            # 移除NaN值
            valid_data = np_array[~np.isnan(np_array)]

            if len(valid_data) == 0:
                logger.warning("没有有效数据点，返回PV=0")
                return 0.0

            # 计算PV值：最大值 - 最小值
            max_val = np.max(valid_data)
            min_val = np.min(valid_data)
            pv_value = max_val - min_val

            return float(pv_value)

        except Exception as e:
            logger.error(f"PV计算失败: {e}")
            return 0.0
    
    def _calculate_rms(self, np_array: np.ndarray) -> float:
        """
        计算RMS值（参考ref/zpy_wavefront.py的标准RMS计算方法）

        Args:
            np_array: numpy数组

        Returns:
            RMS值
        """
        try:
            # {{ AURA: Fix - 修正RMS计算方法，参考ref/zpy_wavefront.py }}
            # 标准RMS计算：先减去均值，然后平方，求平均，再开方
            # 过滤掉NaN和无效值
            valid_data = np_array[~np.isnan(np_array)]
            if len(valid_data) > 0:
                mean_val = np.mean(valid_data)
                rms_value = np.sqrt(np.mean(np.square(valid_data - mean_val)))
                return float(rms_value)
            else:
                return 0.0

        except Exception as e:
            logger.error(f"RMS计算失败: {e}")
            return 0.0
    
    def get_current_data(self) -> Optional[Dict[str, Any]]:
        """获取当前分析数据"""
        return self.current_data
    
    def clear_analysis(self):
        """清理分析资源"""
        try:
            if self.wavefront_analysis:
                self.wavefront_analysis.Close()
                self.wavefront_analysis = None
            
            self.wavefront_results = None
            self.current_data = None
            
            logger.info("波前分析资源已清理")
            
        except Exception as e:
            logger.error(f"清理分析资源失败: {e}")
