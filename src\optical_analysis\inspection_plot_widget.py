#!/usr/bin/env python3
"""
Inspection绘图组件 - 用于显示Zernike系数数据的可视化

主要功能:
1. Zernike系数柱状图显示
2. Zernike系数热力图显示
3. 多组数据对比显示
4. 统计信息显示

作者: AI Assistant
基于: OpticalRealSim项目的PlotDataWidget
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, QPushButton
from PyQt5.QtCore import Qt
import logging

logger = logging.getLogger(__name__)

class InspectionPlotWidget(QWidget):
    """
    Inspection绘图组件
    
    提供Zernike系数数据的多种可视化方式
    """
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.zernike_data_list = []
        self.current_plot_type = "bar"  # bar, heatmap, comparison
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 控制面板
        control_panel = QHBoxLayout()
        
        # 绘图类型选择
        plot_type_label = QLabel("绘图类型:")
        self.plot_type_combo = QComboBox()
        self.plot_type_combo.addItems(["柱状图", "热力图", "对比图"])
        self.plot_type_combo.currentTextChanged.connect(self.on_plot_type_changed)
        
        # 数据选择
        data_label = QLabel("数据选择:")
        self.data_combo = QComboBox()
        self.data_combo.addItem("所有数据")
        self.data_combo.currentTextChanged.connect(self.update_plot)
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.update_plot)
        
        control_panel.addWidget(plot_type_label)
        control_panel.addWidget(self.plot_type_combo)
        control_panel.addWidget(data_label)
        control_panel.addWidget(self.data_combo)
        control_panel.addWidget(refresh_btn)
        control_panel.addStretch()
        
        layout.addLayout(control_panel)
        
        # 创建matplotlib图形
        self.figure = Figure(figsize=(12, 8), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        
        # 设置样式
        self.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #333;
            }
            QComboBox {
                min-width: 100px;
                padding: 2px;
            }
            QPushButton {
                min-width: 60px;
                padding: 4px 8px;
                background-color: #007ACC;
                color: white;
                border: none;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #005A9E;
            }
        """)
    
    def set_zernike_data(self, zernike_data_list):
        """
        设置Zernike数据列表
        
        Args:
            zernike_data_list: ZernikeData对象列表
        """
        self.zernike_data_list = zernike_data_list
        self.update_data_combo()
        self.update_plot()
    
    def update_data_combo(self):
        """更新数据选择下拉框"""
        self.data_combo.clear()
        self.data_combo.addItem("所有数据")
        
        for i, data in enumerate(self.zernike_data_list):
            self.data_combo.addItem(f"镜面 {data.surface_id} (R={data.norm_radius:.3f})")
    
    def on_plot_type_changed(self, plot_type_text):
        """绘图类型改变事件"""
        type_mapping = {
            "柱状图": "bar",
            "热力图": "heatmap", 
            "对比图": "comparison"
        }
        self.current_plot_type = type_mapping.get(plot_type_text, "bar")
        self.update_plot()
    
    def update_plot(self):
        """更新绘图"""
        try:
            if not self.zernike_data_list:
                self.plot_empty_message()
                return
            
            if self.current_plot_type == "bar":
                self.plot_bar_chart()
            elif self.current_plot_type == "heatmap":
                self.plot_heatmap()
            elif self.current_plot_type == "comparison":
                self.plot_comparison()
            else:
                self.plot_bar_chart()
                
        except Exception as e:
            logger.error(f"更新绘图失败: {e}")
            self.plot_error_message(str(e))
    
    def plot_empty_message(self):
        """显示空数据消息"""
        self.figure.clear()
        ax = self.figure.add_subplot(111)
        ax.text(0.5, 0.5, "暂无Zernike数据\n请导入.dat文件", 
                ha='center', va='center', fontsize=16, color='#666',
                transform=ax.transAxes)
        ax.set_xticks([])
        ax.set_yticks([])
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['bottom'].set_visible(False)
        ax.spines['left'].set_visible(False)
        self.canvas.draw()
    
    def plot_error_message(self, error_msg):
        """显示错误消息"""
        self.figure.clear()
        ax = self.figure.add_subplot(111)
        ax.text(0.5, 0.5, f"绘图错误:\n{error_msg}", 
                ha='center', va='center', fontsize=12, color='red',
                transform=ax.transAxes)
        ax.set_xticks([])
        ax.set_yticks([])
        self.canvas.draw()
    
    def plot_bar_chart(self):
        """绘制Zernike系数柱状图"""
        self.figure.clear()
        
        selected_index = self.data_combo.currentIndex() - 1  # 减1因为第一项是"所有数据"
        
        if selected_index == -1:  # 所有数据
            if len(self.zernike_data_list) == 1:
                # 只有一组数据，显示详细的系数柱状图
                data = self.zernike_data_list[0]
                self.plot_single_zernike_bar(data)
            else:
                # 多组数据，显示RMS对比
                self.plot_multiple_rms_bar()
        else:
            # 单组数据详细显示
            if 0 <= selected_index < len(self.zernike_data_list):
                data = self.zernike_data_list[selected_index]
                self.plot_single_zernike_bar(data)
    
    def plot_single_zernike_bar(self, zernike_data):
        """绘制单组Zernike系数柱状图"""
        ax = self.figure.add_subplot(111)
        
        # Zernike项索引 (1-37)
        zernike_indices = list(range(1, 38))
        coefficients = zernike_data.coefficients
        
        # 创建柱状图
        bars = ax.bar(zernike_indices, coefficients, alpha=0.7, color='steelblue')
        
        # 高亮显示较大的系数
        max_coeff = max(abs(c) for c in coefficients)
        for i, (bar, coeff) in enumerate(zip(bars, coefficients)):
            if abs(coeff) > max_coeff * 0.5:  # 高亮显示大于最大值50%的系数
                bar.set_color('orange')
        
        ax.set_xlabel('Zernike项', fontsize=12)
        ax.set_ylabel('系数值', fontsize=12)
        ax.set_title(f'镜面 {zernike_data.surface_id} Zernike系数分布\n(归一化半径: {zernike_data.norm_radius:.6f})', 
                     fontsize=14, fontweight='bold')
        ax.grid(True, alpha=0.3)
        
        # 添加统计信息
        rms_value = np.sqrt(np.mean(np.array(coefficients)**2))
        pv_value = max(coefficients) - min(coefficients)
        ax.text(0.02, 0.98, f'RMS: {rms_value:.6f}\nPV: {pv_value:.6f}', 
                transform=ax.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        self.canvas.draw()
    
    def plot_multiple_rms_bar(self):
        """绘制多组数据的RMS对比柱状图"""
        ax = self.figure.add_subplot(111)
        
        surface_ids = [data.surface_id for data in self.zernike_data_list]
        rms_values = [np.sqrt(np.mean(np.array(data.coefficients)**2)) for data in self.zernike_data_list]
        
        bars = ax.bar(range(len(surface_ids)), rms_values, alpha=0.7, color='lightcoral')
        
        ax.set_xlabel('镜面ID', fontsize=12)
        ax.set_ylabel('RMS值', fontsize=12)
        ax.set_title('各镜面Zernike系数RMS对比', fontsize=14, fontweight='bold')
        ax.set_xticks(range(len(surface_ids)))
        ax.set_xticklabels([f'镜面 {sid}' for sid in surface_ids])
        ax.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, rms in zip(bars, rms_values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                   f'{rms:.4f}', ha='center', va='bottom', fontsize=10)
        
        self.canvas.draw()
    
    def plot_heatmap(self):
        """绘制Zernike系数热力图"""
        self.figure.clear()
        
        if not self.zernike_data_list:
            return
        
        # 创建系数矩阵
        coefficients_matrix = np.array([data.coefficients for data in self.zernike_data_list])
        
        ax = self.figure.add_subplot(111)
        
        # 创建热力图
        im = ax.imshow(coefficients_matrix, cmap='RdBu_r', aspect='auto', interpolation='nearest')
        
        # 设置标签
        ax.set_xlabel('Zernike项', fontsize=12)
        ax.set_ylabel('镜面', fontsize=12)
        ax.set_title('Zernike系数热力图', fontsize=14, fontweight='bold')
        
        # 设置刻度
        ax.set_xticks(range(0, 37, 5))
        ax.set_xticklabels(range(1, 38, 5))
        ax.set_yticks(range(len(self.zernike_data_list)))
        ax.set_yticklabels([f'镜面 {data.surface_id}' for data in self.zernike_data_list])
        
        # 添加颜色条
        cbar = self.figure.colorbar(im, ax=ax)
        cbar.set_label('系数值', fontsize=12)
        
        self.canvas.draw()
    
    def plot_comparison(self):
        """绘制对比图"""
        self.figure.clear()
        
        if len(self.zernike_data_list) < 2:
            ax = self.figure.add_subplot(111)
            ax.text(0.5, 0.5, "对比图需要至少2组数据", 
                    ha='center', va='center', fontsize=16, color='#666',
                    transform=ax.transAxes)
            ax.set_xticks([])
            ax.set_yticks([])
            self.canvas.draw()
            return
        
        ax = self.figure.add_subplot(111)
        
        # 绘制前两组数据的对比
        zernike_indices = list(range(1, 38))
        
        data1 = self.zernike_data_list[0]
        data2 = self.zernike_data_list[1]
        
        ax.plot(zernike_indices, data1.coefficients, 'o-', label=f'镜面 {data1.surface_id}', 
                linewidth=2, markersize=4, alpha=0.8)
        ax.plot(zernike_indices, data2.coefficients, 's-', label=f'镜面 {data2.surface_id}', 
                linewidth=2, markersize=4, alpha=0.8)
        
        # 如果有更多数据，继续添加
        colors = ['red', 'green', 'purple', 'orange']
        markers = ['^', 'v', 'D', 'p']
        
        for i, data in enumerate(self.zernike_data_list[2:6]):  # 最多显示6组数据
            color = colors[i % len(colors)]
            marker = markers[i % len(markers)]
            ax.plot(zernike_indices, data.coefficients, marker=marker, linestyle='-', 
                   label=f'镜面 {data.surface_id}', linewidth=2, markersize=4, 
                   alpha=0.8, color=color)
        
        ax.set_xlabel('Zernike项', fontsize=12)
        ax.set_ylabel('系数值', fontsize=12)
        ax.set_title('Zernike系数对比', fontsize=14, fontweight='bold')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)
        
        # 调整布局以适应图例
        self.figure.tight_layout()
        
        self.canvas.draw()
    
    def export_plot(self, filename):
        """
        导出当前绘图
        
        Args:
            filename: 保存文件名
        """
        try:
            self.figure.savefig(filename, dpi=300, bbox_inches='tight')
            logger.info(f"绘图已导出: {filename}")
            return True
        except Exception as e:
            logger.error(f"导出绘图失败: {e}")
            return False
