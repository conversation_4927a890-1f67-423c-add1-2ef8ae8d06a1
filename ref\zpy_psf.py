import os
import sys
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Union, Any, Optional
from matplotlib import cm

# 导入基础连接模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# 导入PythonStandaloneApplication类
from zpy_base.zpy_connection import PythonStandaloneApplication

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为 SimHei
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示问题

class PsfAnalysisError(Exception):
    """PSF分析时的一般错误。"""
    pass

class PsfCreationError(PsfAnalysisError):
    """创建PSF分析时的错误。"""
    pass

class PsfResultError(PsfAnalysisError):
    """获取PSF分析结果时的错误。"""
    pass

class PsfDataError(PsfAnalysisError):
    """提取PSF数据时的错误。"""
    pass

class PsfVisualizationError(PsfAnalysisError):
    """PSF可视化时的错误。"""
    pass

class ZemaxPsfAnalysis:
    """
    用于进行Zemax光学系统PSF分析的类。
    提供创建PSF分析、读取结果、提取信息和可视化绘制功能。
    """

    def __init__(self, zos: PythonStandaloneApplication):
        """
        初始化与Zemax的连接。

        Args:
            zos (PythonStandaloneApplication): Zemax连接实例。

        Raises:
            Exception: 如果初始化失败。
        """
        try:
            # 创建与Zemax的连接
            self.zos = zos
            self.ZOSAPI = self.zos.ZOSAPI
            self.TheApplication = self.zos.TheApplication
            self.TheSystem = self.zos.TheSystem

            # 初始化PSF分析相关变量
            self.psf_analysis = None
            self.psf_results = None
            self.psf_data = None
        except Exception as e:
            raise Exception(f"Zemax API初始化失败: {str(e)}")

    def load_file(self, file_path: str, save_if_needed: bool = False) -> None:
        """
        加载Zemax文件。

        Args:
            file_path (str): Zemax文件路径。
            save_if_needed (bool, optional): 如果需要，是否保存当前文件。默认为False。

        Raises:
            FileNotFoundError: 如果文件不存在。
            Exception: 如果加载文件时出错。
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")

            self.TheSystem.LoadFile(file_path, save_if_needed)
            print(f"文件加载成功: {file_path}")
        except FileNotFoundError as e:
            raise
        except Exception as e:
            raise Exception(f"加载文件时出错: {str(e)}")

    def _get_psf_sampling_enum(self, sampling_size: int):
        """
        获取PSF采样枚举值 - 基于ZOSAPI.Analysis.Settings.Psf.PsfSampling

        Args:
            sampling_size (int): 采样大小

        Returns:
            PSF采样枚举值
        """
        sampling_map = {
            32: "PsfS_32x32",
            64: "PsfS_64x64",
            128: "PsfS_128x128",
            256: "PsfS_256x256",
            512: "PsfS_512x512",
            1024: "PsfS_1024x1024",
            2048: "PsfS_2048x2048",
            4096: "PsfS_4096x4096",
            8192: "PsfS_8192x8192",
            16384: "PsfS_16384x16384"
        }

        try:
            enum_name = sampling_map.get(sampling_size, "PsfS_256x256")
            return getattr(self.ZOSAPI.Analysis.Settings.Psf.PsfSampling, enum_name)
        except AttributeError:
            print(f"警告: 不支持的PSF采样大小 {sampling_size}，使用默认值256x256")
            return self.ZOSAPI.Analysis.Settings.Psf.PsfSampling.PsfS_256x256

    def _get_psf_rotation_enum(self, rotation: int):
        """
        获取PSF旋转枚举值 - 基于ZOSAPI.Analysis.Settings.Psf.PsfRotation

        Args:
            rotation (int): 旋转角度 (0, 90, 180, 270)

        Returns:
            PSF旋转枚举值
        """
        rotation_map = {
            0: "CW0",
            90: "CW90",
            180: "CW180",
            270: "CW270"
        }

        try:
            enum_name = rotation_map.get(rotation, "CW0")
            return getattr(self.ZOSAPI.Analysis.Settings.Psf.PsfRotation, enum_name)
        except AttributeError:
            print(f"警告: 不支持的PSF旋转角度 {rotation}，使用默认值0度")
            return self.ZOSAPI.Analysis.Settings.Psf.PsfRotation.CW0

    def _get_fft_psf_type_enum(self, psf_type: str):
        """
        获取FFT PSF类型枚举值 - 基于ZOSAPI.Analysis.Settings.Psf.FftPsfType

        Args:
            psf_type (str): PSF类型 ("Linear", "Log", "Phase", "Real", "Imaginary")

        Returns:
            FFT PSF类型枚举值
        """
        type_map = {
            "Linear": "Linear",
            "Log": "Log",
            "Phase": "Phase",
            "Real": "Real",
            "Imaginary": "Imaginary"
        }

        try:
            enum_name = type_map.get(psf_type, "Linear")
            return getattr(self.ZOSAPI.Analysis.Settings.Psf.FftPsfType, enum_name)
        except AttributeError:
            print(f"警告: 不支持的PSF类型 {psf_type}，使用默认值Linear")
            return self.ZOSAPI.Analysis.Settings.Psf.FftPsfType.Linear

    def create_psf_analysis(self,
                           field_number: int = 1,
                           wavelength_number: int = 1,
                           surface_number: int = -1,  # -1表示像面
                           psf_sampling: int = 256,   # 采样大小
                           output_size: int = 512,    # 输出大小
                           rotation: int = 0,         # 旋转角度 (0, 90, 180, 270)
                           image_delta: float = 0.0,  # 图像增量
                           use_polarization: bool = False,  # 是否使用偏振
                           fft_psf_type: str = "Linear",    # PSF类型
                           normalize: bool = False) -> None:
        """
        创建PSF分析。

        Args:
            field_number (int, optional): 视场编号。默认为1。
            wavelength_number (int, optional): 波长编号。默认为1。
            surface_number (int, optional): 表面编号，-1表示像面。默认为-1。
            psf_sampling (int, optional): PSF采样大小。默认为256。
            output_size (int, optional): 输出大小。默认为512。
            rotation (int, optional): 旋转角度。默认为0。
            image_delta (float, optional): 图像增量。默认为0.0。
            use_polarization (bool, optional): 是否使用偏振。默认为False。
            fft_psf_type (str, optional): PSF类型。默认为"Linear"。
            normalize (bool, optional): 是否归一化。默认为False。

        Raises:
            PsfCreationError: 如果创建PSF分析时出错。
        """
        try:
            # 创建FFT PSF分析
            print("正在创建FFT PSF分析...")
            self.psf_analysis = self.TheSystem.Analyses.New_Analysis(
                self.ZOSAPI.Analysis.AnalysisIDM.FftPsf)

            print(f"PSF分析对象: {self.psf_analysis}")
            print(f"PSF分析类型: {type(self.psf_analysis)}")

            if self.psf_analysis is None:
                raise PsfCreationError("无法创建FFT PSF分析")

            # 获取设置接口
            settings = self.psf_analysis.GetSettings()

            # 设置基本参数（参考wavefront实现）
            settings.Field.SetFieldNumber(field_number)
            settings.Wavelength.SetWavelengthNumber(wavelength_number)
            settings.Surface.SetSurfaceNumber(surface_number)

            # 设置PSF特有参数
            settings.SampleSize = self._get_psf_sampling_enum(psf_sampling)
            settings.OutputSize = self._get_psf_sampling_enum(output_size)
            settings.Rotation = self._get_psf_rotation_enum(rotation)
            settings.ImageDelta = image_delta
            settings.UsePolarization = use_polarization
            settings.Type = self._get_fft_psf_type_enum(fft_psf_type)
            settings.Normalize = normalize

            print("PSF分析创建成功")
        except Exception as e:
            raise PsfCreationError(f"创建PSF分析时出错: {str(e)}")

    def run_analysis(self) -> None:
        """
        运行PSF分析。

        Raises:
            PsfCreationError: 如果PSF分析未创建。
            Exception: 如果运行分析时出错。
        """
        try:
            if self.psf_analysis is None:
                raise PsfCreationError("PSF分析未创建，请先调用create_psf_analysis方法")

            # 运行分析
            self.psf_analysis.ApplyAndWaitForCompletion()
            print("PSF分析运行完成")

            # 添加小延迟确保分析完全完成
            import time
            time.sleep(1)
        except Exception as e:
            raise Exception(f"运行PSF分析时出错: {str(e)}")

    def get_results(self) -> None:
        """
        获取PSF分析结果。

        Raises:
            PsfResultError: 如果获取PSF分析结果时出错。
        """
        try:
            if self.psf_analysis is None:
                raise PsfResultError("PSF分析未创建，请先调用create_psf_analysis方法")

            # 获取分析结果
            self.psf_results = self.psf_analysis.GetResults()

            # 如果GetResults()返回None，尝试直接使用分析对象
            if self.psf_results is None:
                # 对于PSF分析，可能需要直接从分析对象获取数据
                self.psf_results = self.psf_analysis
                print("注意: 使用分析对象本身作为结果对象")

            print("获取PSF分析结果成功")
        except Exception as e:
            raise PsfResultError(f"获取PSF分析结果时出错: {str(e)}")

    def extract_psf_data(self, field_index: int = 0, wavelength_index: int = 0) -> Dict[str, Any]:
        """
        提取PSF数据并结构化输出。

        Args:
            field_index (int, optional): 视场索引，从0开始。默认为0。
            wavelength_index (int, optional): 波长索引，从0开始。默认为0。

        Returns:
            Dict[str, Any]: 包含PSF数据的字典。

        Raises:
            PsfDataError: 如果提取PSF数据时出错。
        """
        try:
            if self.psf_results is None:
                raise PsfDataError("PSF分析结果未获取，请先调用get_results方法")

            # 初始化数据字典
            data = {}
            data["视场索引"] = field_index
            data["波长索引"] = wavelength_index

            # 检查是否有DataGrid数据
            data_grid = None
            grid_index = 0

            if hasattr(self.psf_results, 'NumberOfDataGrids'):
                num_data_grids = self.psf_results.NumberOfDataGrids
                if num_data_grids > 0:
                    data_grid = self.psf_results.GetDataGrid(grid_index)
                else:
                    raise PsfDataError("没有可用的PSF数据网格")
            elif hasattr(self.psf_results, 'GetDataGrid'):
                # 直接尝试获取DataGrid
                try:
                    data_grid = self.psf_results.GetDataGrid(grid_index)
                except:
                    raise PsfDataError("无法直接获取DataGrid")
            else:
                raise PsfDataError("PSF结果对象没有DataGrid相关方法")

            if data_grid is None:
                raise PsfDataError(f"无法获取索引为{grid_index}的DataGrid")

            # 获取DataGrid的属性
            data["PSF类型"] = "PSF强度分布数据"

            # 获取网格尺寸
            nx = data_grid.Nx
            ny = data_grid.Ny
            data["PSF采样点数X"] = nx
            data["PSF采样点数Y"] = ny

            # 提取网格值
            grid_data = data_grid.Values

            # 创建PSF数据矩阵
            if hasattr(self, 'zos') and hasattr(self.zos, 'reshape'):
                psf_matrix = np.array(self.zos.reshape(grid_data, nx, ny, True))
            else:
                # 手动转换
                psf_matrix = np.zeros((nx, ny))
                for i in range(nx):
                    for j in range(ny):
                        psf_matrix[i, j] = grid_data[i * ny + j]

            # 创建掩码矩阵（有效数据：大于0且非NaN）
            mask_matrix = ~np.isnan(psf_matrix) & (psf_matrix > 0)

            # 计算PSF特征参数
            valid_data = psf_matrix[mask_matrix]
            if len(valid_data) > 0:
                data["峰值强度"] = np.max(psf_matrix)
                data["总能量"] = np.sum(valid_data)
                data["平均强度"] = np.mean(valid_data)
                data["强度标准差"] = np.std(valid_data)

                # 计算质心位置
                y_indices, x_indices = np.indices(psf_matrix.shape)
                total_intensity = np.sum(psf_matrix)
                if total_intensity > 0:
                    centroid_x = np.sum(x_indices * psf_matrix) / total_intensity
                    centroid_y = np.sum(y_indices * psf_matrix) / total_intensity
                    data["质心X"] = centroid_x
                    data["质心Y"] = centroid_y
            else:
                data["峰值强度"] = 0
                data["总能量"] = 0
                data["平均强度"] = 0
                data["强度标准差"] = 0
                data["质心X"] = 0
                data["质心Y"] = 0

            # 存储PSF数据矩阵和掩码
            data["PSF数据矩阵"] = psf_matrix
            data["掩码矩阵"] = mask_matrix

            # 保存PSF数据供后续使用
            self.psf_data = data

            return data
        except Exception as e:
            raise PsfDataError(f"提取PSF数据时出错: {str(e)}")

    def plot_psf_map(self, title: str = "PSF分析图") -> None:
        """
        绘制PSF二维伪彩色图。

        Args:
            title (str, optional): 图表标题。默认为"PSF分析图"。

        Raises:
            PsfVisualizationError: 如果绘制PSF图时出错。
        """
        try:
            if self.psf_data is None:
                raise PsfVisualizationError("PSF数据未提取，请先调用extract_psf_data方法")

            psf_matrix = self.psf_data["PSF数据矩阵"]
            mask_matrix = self.psf_data["掩码矩阵"]

            # 获取矩阵尺寸
            ny, nx = psf_matrix.shape

            # 计算以中心为原点的坐标范围
            extent = [-nx/2, nx/2, -ny/2, ny/2]

            # 创建图形
            fig, ax = plt.subplots(figsize=(10, 8))

            # 应用掩码，将无效数据设为NaN
            masked_psf = np.where(mask_matrix, psf_matrix, np.nan)

            # 绘制伪彩色图，使用以中心为原点的坐标系统
            im = ax.imshow(masked_psf, cmap='jet', origin='lower', aspect='equal', extent=extent)

            # 添加颜色条
            cbar = plt.colorbar(im, ax=ax, shrink=0.8)
            cbar.set_label('PSF强度', fontsize=12)

            # 设置标题和标签
            ax.set_title(title, fontsize=14, fontweight='bold')
            ax.set_xlabel('X方向像素', fontsize=12)
            ax.set_ylabel('Y方向像素', fontsize=12)

            # 添加PSF信息文本
            info_text = (
                f"峰值强度: {self.psf_data['峰值强度']:.3e}\n"
                f"总能量: {self.psf_data['总能量']:.3e}\n"
                f"质心: ({self.psf_data['质心X']:.1f}, {self.psf_data['质心Y']:.1f})\n"
                f"采样: {self.psf_data['PSF采样点数X']}×{self.psf_data['PSF采样点数Y']}"
            )

            ax.text(0.02, 0.98, info_text, transform=ax.transAxes,
                    verticalalignment='top', bbox=dict(boxstyle='round',
                    facecolor='white', alpha=0.8), fontsize=10)

            plt.tight_layout()
            plt.show()

        except Exception as e:
            raise PsfVisualizationError(f"绘制PSF图时出错: {str(e)}")

    def print_psf_info(self) -> None:
        """
        打印PSF分析信息。
        """
        try:
            if self.psf_data is None:
                print("PSF数据未提取，请先调用extract_psf_data方法")
                return

            print("\n" + "="*50)
            print("PSF分析结果")
            print("="*50)
            print(f"视场索引: {self.psf_data['视场索引']}")
            print(f"波长索引: {self.psf_data['波长索引']}")
            print(f"PSF类型: {self.psf_data['PSF类型']}")
            print(f"采样点数: {self.psf_data['PSF采样点数X']} × {self.psf_data['PSF采样点数Y']}")
            print(f"峰值强度: {self.psf_data['峰值强度']:.6e}")
            print(f"总能量: {self.psf_data['总能量']:.6e}")
            print(f"平均强度: {self.psf_data['平均强度']:.6e}")
            print(f"强度标准差: {self.psf_data['强度标准差']:.6e}")
            print(f"质心位置: ({self.psf_data['质心X']:.2f}, {self.psf_data['质心Y']:.2f})")
            print("="*50)
        except Exception as e:
            print(f"打印PSF信息时出错: {str(e)}")

    def save_results(self, file_path: str) -> None:
        """
        保存PSF分析结果到文件。

        Args:
            file_path (str): 保存文件路径。

        Raises:
            PsfResultError: 如果保存PSF分析结果时出错。
        """
        try:
            if self.psf_results is None:
                raise PsfResultError("PSF分析结果未获取，请先调用get_results方法")

            # 保存结果
            self.psf_results.GetTextFile(file_path)
            print(f"PSF分析结果已保存到: {file_path}")
        except Exception as e:
            raise PsfResultError(f"保存PSF分析结果时出错: {str(e)}")

    def close(self) -> None:
        """
        关闭PSF分析并清理资源。
        """
        try:
            if self.psf_analysis is not None:
                self.psf_analysis.Close()
                self.psf_analysis = None
                print("PSF分析已关闭")
        except Exception as e:
            print(f"关闭PSF分析时出错: {str(e)}")
        finally:
            # 清理变量
            self.psf_results = None
            self.psf_data = None


if __name__ == "__main__":
    """
    主程序入口，演示ZemaxPsfAnalysis类的用法。
    """
    # 示例文件路径
    sample_file = os.path.abspath(os.path.join("data", "4FAN-trans.ZOS"))
    output_file = os.path.abspath(os.path.join("output", "psf_results.txt"))

    try:
        # 创建目录（如果不存在）
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        # 创建Zemax连接
        zos = PythonStandaloneApplication()

        # 创建ZemaxPsfAnalysis实例
        psf_analyzer = ZemaxPsfAnalysis(zos)

        # 加载示例文件
        psf_analyzer.load_file(sample_file)

        # 创建PSF分析
        psf_analyzer.create_psf_analysis(
            field_number=1,
            wavelength_number=1,
            surface_number=-1,
            psf_sampling=64,
            output_size=128,
            rotation=0,
            image_delta=0.0,
            use_polarization=False,
            fft_psf_type="Linear",
            normalize=False
        )

        # 运行分析
        psf_analyzer.run_analysis()

        # 获取结果
        psf_analyzer.get_results()

        # 提取PSF数据 (注意：索引从0开始，而编号从1开始)
        psf_data = psf_analyzer.extract_psf_data(field_index=0, wavelength_index=1)

        # 打印PSF信息
        psf_analyzer.print_psf_info()

        # 保存结果
        psf_analyzer.save_results(output_file)

        # 可视化PSF图
        psf_analyzer.plot_psf_map(title="PSF分析图 - 视场1, 波长1")

        # 多视场、多波长批量分析
        from zpy_base.zpy_sysinfo import ZemaxSystemInfo

        # 创建ZemaxSystemInfo实例
        zemax_info = ZemaxSystemInfo(zos)
        zemax_info.load_file(sample_file)

        # 获取所有视场信息
        field_info = zemax_info.get_field_info()
        field_count = field_info['点数']

        # 获取所有波长信息
        wavelength_info = zemax_info.get_wavelength_info()
        wave_count = wavelength_info['波长数量']

        # 根据视场和波长的数量进行PSF分析
        for field_index in range(field_count):
            for wavelength_index in range(wave_count):
                psf_analyzer.create_psf_analysis(
                    field_number=field_index + 1,
                    wavelength_number=wavelength_index + 1,
                    surface_number=-1,
                    psf_sampling=256,
                    output_size=512,
                    rotation=0,
                    image_delta=0.0,
                    use_polarization=False,
                    fft_psf_type="Linear",
                    normalize=False
                )

                # 运行分析
                psf_analyzer.run_analysis()

                # 获取结果
                psf_analyzer.get_results()

                # 提取PSF数据
                psf_data = psf_analyzer.extract_psf_data(
                    field_index=field_index,
                    wavelength_index=wavelength_index
                )

                # 计算PSF特征参数
                peak_intensity = psf_data.get("峰值强度", None)
                total_energy = psf_data.get("总能量", None)
                centroid_x = psf_data.get("质心X", None)
                centroid_y = psf_data.get("质心Y", None)

                # 结构化输出
                print(f"视场索引: {field_index + 1}, 波长索引: {wavelength_index + 1}, "
                      f"峰值强度: {peak_intensity}, 总能量: {total_energy}, "
                      f"质心: ({centroid_x:.1f}, {centroid_y:.1f})")

    except Exception as e:
        print(f"错误: {str(e)}")
    finally:
        # 确保资源被释放
        if 'psf_analyzer' in locals():
            psf_analyzer.close()