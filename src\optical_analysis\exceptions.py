"""
Zemax连接相关异常定义

从OpticalRealSim-main移植的异常类，用于处理Zemax ZOS-API连接过程中的各种异常情况。

异常层次结构：
- ZemaxBaseException (基础异常)
  - ZemaxLicenseException (许可证异常)
  - ZemaxConnectionException (连接异常)
  - ZemaxInitializationException (初始化异常)
  - ZemaxSystemNotPresentException (系统不存在异常)

作者: AI Assistant (移植自OpticalRealSim-main)
版本: 1.0.0
"""


class ZemaxBaseException(Exception):
    """Zemax异常基类"""
    def __init__(self, message="Zemax操作异常"):
        self.message = message
        super().__init__(self.message)


class ZemaxLicenseException(ZemaxBaseException):
    """
    Zemax许可证异常
    
    当Zemax许可证无效、过期或不支持API使用时抛出此异常
    """
    def __init__(self, message="Zemax许可证无效或不支持API使用"):
        super().__init__(message)


class ZemaxConnectionException(ZemaxBaseException):
    """
    Zemax连接异常
    
    当无法建立与Zemax的.NET连接时抛出此异常
    """
    def __init__(self, message="无法建立与Zemax的连接"):
        super().__init__(message)


class ZemaxInitializationException(ZemaxBaseException):
    """
    Zemax初始化异常
    
    当无法获取Zemax应用程序实例时抛出此异常
    """
    def __init__(self, message="无法初始化Zemax应用程序"):
        super().__init__(message)


class ZemaxSystemNotPresentException(ZemaxBaseException):
    """
    Zemax系统不存在异常
    
    当无法获取Zemax主光学系统时抛出此异常
    """
    def __init__(self, message="无法获取Zemax光学系统"):
        super().__init__(message)


# 异常映射字典，用于将英文异常信息转换为中文用户友好信息
EXCEPTION_MESSAGE_MAP = {
    "Unable to initialize .NET connection to ZOSAPI": "无法建立与Zemax的.NET连接，请检查Zemax是否正在运行",
    "Unable to acquire ZOSAPI application": "无法获取Zemax应用程序，请检查Zemax是否正确安装",
    "License is not valid for ZOSAPI use": "Zemax许可证无效或不支持API使用，请检查许可证状态",
    "Unable to acquire Primary system": "无法获取Zemax主光学系统，请检查Zemax状态",
    "Unable to locate Zemax OpticStudio": "无法找到Zemax OpticStudio安装路径，请检查软件安装"
}


def get_user_friendly_message(technical_message: str) -> str:
    """
    将技术异常信息转换为用户友好的中文信息
    
    Args:
        technical_message: 技术异常信息
        
    Returns:
        用户友好的中文异常信息
    """
    return EXCEPTION_MESSAGE_MAP.get(technical_message, f"Zemax操作异常: {technical_message}")
