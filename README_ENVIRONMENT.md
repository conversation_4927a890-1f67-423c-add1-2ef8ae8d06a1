# 25AutoAssembly 环境配置指南

## 🎯 项目概述

25AutoAssembly是一个集成Zemax OpticStudio的精密运动控制系统，用于光学设备的自动装配和位姿调整。项目需要特定的Python环境以支持Zemax ZOS-API集成。

## 📋 系统要求

### 硬件要求
- **CPU**: Intel i5或更高
- **内存**: 8GB RAM (推荐16GB)
- **硬盘**: 至少5GB可用空间
- **网络**: 支持TCP/IP通信，能访问192.168.0.x网段

### 软件要求
- **操作系统**: Windows 10/11 (64位)
- **Anaconda**: Anaconda3或Miniconda3
- **Zemax OpticStudio**: 2020.2或更高版本
- **.NET Framework**: 4.7.2或更高版本

## 🚀 快速开始

### 方法一：自动配置（推荐）

1. **运行自动配置脚本**
   ```bash
   # 双击运行或在命令行执行
   setup_environment.bat
   ```

2. **激活环境**
   ```bash
   conda activate 25autoassembly
   ```

3. **验证环境**
   ```bash
   python test_environment.py
   ```

4. **启动程序**
   ```bash
   python main.py
   ```

### 方法二：手动配置

1. **创建环境**
   ```bash
   conda env create -f environment_zemax_correct.yml
   ```

2. **激活环境**
   ```bash
   conda activate 25autoassembly
   ```

3. **验证安装**
   ```bash
   python -c "import PyQt5, numpy, matplotlib, watchdog, clr; print('环境配置成功！')"
   ```

## 🔧 环境配置详情

### Python版本要求
- **版本**: Python 3.6.15
- **原因**: Zemax ZOS-API官方支持版本
- **注意**: 不能使用Python 3.7+版本

### 核心依赖包

#### GUI框架
- **PyQt5 5.12.3**: 图形用户界面
- **Qt 5.12.9**: Qt框架

#### 数值计算
- **NumPy 1.19.5**: 数值计算基础
- **SciPy 1.5.4**: 科学计算
- **Pandas 1.1.5**: 数据处理

#### 数据可视化
- **Matplotlib 3.3.4**: 光学数据绘图

#### Zemax集成
- **pythonnet 2.5.2**: .NET互操作
- **clr**: .NET公共语言运行时

#### 系统工具
- **watchdog 2.3.1**: 文件监控
- **netifaces 0.11.0**: 网络接口

## 🔍 环境验证

### 自动验证
```bash
python test_environment.py
```

### 手动验证
```python
# 验证Python版本
import sys
print(f"Python版本: {sys.version}")

# 验证核心依赖
import PyQt5, numpy, matplotlib, pandas, watchdog
print("✅ 核心依赖导入成功")

# 验证Zemax集成
import clr
print("✅ .NET集成可用")

# 检查Zemax安装
import winreg, os
aKey = winreg.OpenKey(winreg.ConnectRegistry(None, winreg.HKEY_CURRENT_USER), r"Software\Zemax", 0, winreg.KEY_READ)
zemaxData = winreg.QueryValueEx(aKey, 'ZemaxRoot')
print(f"✅ Zemax路径: {zemaxData[0]}")
```

## ⚠️ 常见问题

### 1. Python版本错误
**问题**: 使用了Python 3.7+版本
**解决**: 必须使用Python 3.6.x版本
```bash
conda install python=3.6.15
```

### 2. pythonnet导入失败
**问题**: `ImportError: No module named 'clr'`
**解决**: 重新安装pythonnet
```bash
pip install pythonnet==2.5.2
```

### 3. Zemax注册表未找到
**问题**: 无法找到Zemax安装路径
**解决**: 
- 确保Zemax OpticStudio已正确安装
- 检查注册表项：`HKEY_CURRENT_USER\Software\Zemax\ZemaxRoot`

### 4. DLL文件缺失
**问题**: ZOSAPI_NetHelper.dll未找到
**解决**: 
- 重新安装Zemax OpticStudio
- 确保ZOS-API组件已安装

### 5. 网络连接问题
**问题**: 无法连接到运动控制设备
**解决**: 
- 检查网络配置
- 确保能访问192.168.0.x网段
- 检查防火墙设置

## 📁 文件说明

### 环境配置文件
- `environment_zemax_correct.yml`: Anaconda环境配置
- `setup_environment.bat`: 自动配置脚本
- `test_environment.py`: 环境验证脚本

### 项目核心文件
- `main.py`: 主程序入口
- `config/config.json`: 系统配置
- `gui/motion_control_ui.py`: 用户界面
- `device_communication/tcp_motion_controller.py`: TCP通信

### Zemax集成文件
- `src/optical_analysis/`: 光学分析模块
- `ref/zpy_connection.py`: Zemax连接参考
- `ref/zpy_fftmtf.py`: MTF分析参考
- `ref/zpy_psf.py`: PSF分析参考

## 🎯 使用流程

1. **环境准备**
   - 安装Anaconda
   - 安装Zemax OpticStudio
   - 配置Python环境

2. **项目启动**
   - 激活conda环境
   - 运行环境测试
   - 启动主程序

3. **功能使用**
   - 连接运动控制设备
   - 加载光学数据
   - 执行运动控制
   - 进行光学分析

## 📞 技术支持

如果遇到环境配置问题，请：

1. 运行 `test_environment.py` 获取详细错误信息
2. 检查系统要求是否满足
3. 参考常见问题解决方案
4. 确保所有依赖软件正确安装

## 📝 更新日志

- **v1.0.0**: 初始版本，支持Zemax ZOS-API集成
- 基于Python 3.6.15和PyQt5的稳定配置
- 完整的光学分析和运动控制功能
