"""
VRML 处理器
基于VTK原生支持的VRML 2.0文件处理器，替换原有的VRML 1.0自定义解析器

作者: AI Assistant  
日期: 2025-01-09
版本: 2.0 (VTK原生支持)
"""

import os
import vtk
from typing import List, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class VRMLProcessor:
    """VRML 2.0处理器 - 基于VTK原生支持"""
    
    def __init__(self):
        """初始化VRML处理器"""
        self.importer = None
        self.temp_renderer = None
        self.temp_render_window = None
        
    def parse_file(self, filename: str) -> List[vtk.vtkActor]:
        """
        解析VRML文件并返回Actor列表
        
        Args:
            filename: VRML文件路径
            
        Returns:
            List[vtk.vtkActor]: Actor列表，失败时返回空列表
        """
        try:
            # 1. 文件验证
            if not self._validate_file(filename):
                return []
            
            logger.info(f"开始使用VTK原生支持加载VRML文件: {filename}")
            
            # 2. 创建VTK导入器
            self.importer = vtk.vtkVRMLImporter()
            self.importer.SetFileName(filename)
            
            # 3. 创建临时渲染环境
            self._setup_temp_rendering()
            
            # 4. 设置导入器的渲染窗口
            self.importer.SetRenderWindow(self.temp_render_window)
            
            # 5. 执行VRML导入
            logger.info("执行VTK VRML导入...")
            self.importer.Read()
            
            # 6. 提取所有Actor
            actors = self._extract_actors()
            
            if actors:
                logger.info(f"成功从VRML文件提取 {len(actors)} 个Actor: {filename}")
            else:
                logger.warning(f"未从VRML文件中提取到任何Actor: {filename}")
            
            return actors
            
        except Exception as e:
            logger.error(f"VRML文件处理失败 {filename}: {e}")
            import traceback
            traceback.print_exc()
            return []
        finally:
            # 7. 清理临时环境
            self._cleanup_resources()
    
    def _validate_file(self, filename: str) -> bool:
        """验证VRML文件"""
        try:
            # 检查文件是否存在
            if not os.path.exists(filename):
                logger.error(f"VRML文件不存在: {filename}")
                return False
            
            # 检查文件扩展名
            if not filename.lower().endswith(('.wrl', '.vrml')):
                logger.warning(f"文件扩展名不是标准VRML格式: {filename}")
            
            # 检查文件是否可读
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    first_line = f.readline().strip()
                    # 检查VRML版本标识
                    if not (first_line.startswith('#VRML') or 'VRML' in first_line):
                        logger.warning(f"文件可能不是有效的VRML格式: {filename}")
            except Exception as e:
                logger.error(f"无法读取文件: {filename}, 错误: {e}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"文件验证失败: {e}")
            return False
    
    def _setup_temp_rendering(self):
        """设置临时渲染环境"""
        try:
            # 创建临时渲染器
            self.temp_renderer = vtk.vtkRenderer()
            self.temp_renderer.SetBackground(0.1, 0.1, 0.2)  # 深蓝色背景
            
            # 创建临时渲染窗口
            self.temp_render_window = vtk.vtkRenderWindow()
            self.temp_render_window.AddRenderer(self.temp_renderer)
            self.temp_render_window.SetSize(800, 600)
            self.temp_render_window.SetOffScreenRendering(1)  # 离屏渲染
            
            logger.debug("临时渲染环境设置完成")
            
        except Exception as e:
            logger.error(f"设置临时渲染环境失败: {e}")
            raise
    
    def _extract_actors(self) -> List[vtk.vtkActor]:
        """从渲染器中提取所有Actor"""
        try:
            actors = []
            
            if not self.temp_renderer:
                logger.warning("临时渲染器不存在，无法提取Actor")
                return actors
            
            # 获取渲染器中的所有Actor
            actor_collection = self.temp_renderer.GetActors()
            actor_collection.InitTraversal()
            
            for i in range(actor_collection.GetNumberOfItems()):
                original_actor = actor_collection.GetNextActor()
                if original_actor:
                    # 创建新的Actor副本
                    new_actor = vtk.vtkActor()
                    
                    # 复制Mapper
                    if original_actor.GetMapper():
                        new_actor.SetMapper(original_actor.GetMapper())
                    
                    # 深度复制属性
                    new_actor.GetProperty().DeepCopy(original_actor.GetProperty())
                    
                    # 复制变换
                    if original_actor.GetUserTransform():
                        new_actor.SetUserTransform(original_actor.GetUserTransform())
                    
                    actors.append(new_actor)
                    logger.debug(f"提取Actor {i+1}: Mapper={original_actor.GetMapper() is not None}")
            
            logger.info(f"成功提取 {len(actors)} 个Actor")
            return actors
            
        except Exception as e:
            logger.error(f"提取Actor失败: {e}")
            return []
    
    def _cleanup_resources(self):
        """清理临时资源"""
        try:
            if self.temp_render_window:
                self.temp_render_window.Finalize()
                self.temp_render_window = None
            
            if self.temp_renderer:
                self.temp_renderer = None
            
            if self.importer:
                self.importer = None
            
            logger.debug("临时资源清理完成")
            
        except Exception as e:
            logger.warning(f"清理资源时出现警告: {e}")


# 为了保持向后兼容性，创建别名
VRML10Parser = VRMLProcessor
