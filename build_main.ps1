# 25AutoAssembly Build Script (Working)
# PyInstaller packaging script with Zemax support and conda environment

param(
    [switch]$Clean,
    [switch]$Debug,
    [switch]$Test,
    [string]$CondaEnv = "pyzemax"
)

# Error handling
$ErrorActionPreference = "Stop"

# Get script directory
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location $ScriptDir

Write-Host "======================================================" -ForegroundColor Cyan
Write-Host "25AutoAssembly Build Script (Working Version)" -ForegroundColor Green
Write-Host "======================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "[1/6] Setting up environment..." -ForegroundColor Yellow
Write-Host "Project root: $ScriptDir" -ForegroundColor Cyan

# Simplified conda environment setup
Write-Host "Using conda environment: $CondaEnv" -ForegroundColor Cyan

# Verify conda environment
$testResult = conda run -n $CondaEnv python --version 2>$null
if ($LASTEXITCODE -ne 0) {
    Write-Host "Error: conda environment '$CondaEnv' is not available" -ForegroundColor Red
    Write-Host "Please check if the environment exists" -ForegroundColor Yellow
    exit 1
}
Write-Host "Conda environment verification successful" -ForegroundColor Green
Write-Host ""

Write-Host "[2/6] Verifying Python environment..." -ForegroundColor Yellow
# Use conda run to ensure correct Python environment
$pythonVersion = conda run -n $CondaEnv python --version 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "Python version: $pythonVersion" -ForegroundColor Green
} else {
    Write-Host "Error: Python environment verification failed" -ForegroundColor Red
    exit 1
}

# Check critical packages
$pyqt5Check = conda run -n $CondaEnv python -c "import PyQt5; print('PyQt5 installed successfully')" 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "PyQt5 installed successfully" -ForegroundColor Green
} else {
    Write-Host "Error: PyQt5 not properly installed" -ForegroundColor Red
    exit 1
}

$pythonnetCheck = conda run -n $CondaEnv python -c "import pythonnet; print('PythonNET installed successfully')" 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "PythonNET installed successfully" -ForegroundColor Green
} else {
    Write-Host "Warning: pythonnet not properly installed, Zemax functionality may not be available" -ForegroundColor Yellow
}

Write-Host "Environment verification completed" -ForegroundColor Green
Write-Host ""

Write-Host "[3/6] Cleaning old build files..." -ForegroundColor Yellow
# Clean old build files
if ($Clean) {
    Write-Host "Performing cleanup operation..." -ForegroundColor Yellow
    if (Test-Path "build") {
        Write-Host "Removing build directory..." -ForegroundColor Cyan
        Remove-Item -Recurse -Force "build"
    }
    if (Test-Path "dist") {
        Write-Host "Removing dist directory..." -ForegroundColor Cyan
        Remove-Item -Recurse -Force "dist"
    }
    Write-Host "Cleanup completed" -ForegroundColor Green
} else {
    Write-Host "Skipping cleanup step (use -Clean parameter to enable)" -ForegroundColor Cyan
}
Write-Host ""

Write-Host "[4/6] Verifying required files..." -ForegroundColor Yellow
# Verify required files
$requiredFiles = @("main.py", "main.spec")
$requiredDirs = @("src", "logs")

foreach ($file in $requiredFiles) {
    if (-not (Test-Path $file)) {
        Write-Host "Error: Main file not found: $file" -ForegroundColor Red
        exit 1
    } else {
        Write-Host "✓ $file found" -ForegroundColor Green
    }
}

foreach ($dir in $requiredDirs) {
    if (-not (Test-Path $dir)) {
        Write-Host "Error: Required directory not found: $dir" -ForegroundColor Red
        exit 1
    } else {
        Write-Host "✓ $dir directory found" -ForegroundColor Green
    }
}

# Verify core modules in src directory
$srcModules = @("src\gui", "src\config", "src\data_models", "src\device_communication", "src\utils", "src\optical_analysis")
foreach ($module in $srcModules) {
    if (-not (Test-Path $module)) {
        Write-Host "Error: Core module not found: $module" -ForegroundColor Red
        exit 1
    } else {
        Write-Host "✓ $module module found" -ForegroundColor Green
    }
}

# Verify Zemax DLL files
$zosDir = "src\optical_analysis\ZOS"
if (Test-Path $zosDir) {
    Write-Host "✓ ZOS directory found: $zosDir" -ForegroundColor Green
    $zemaXDlls = @("ZOSAPI.dll", "ZOSAPI_Interfaces.dll", "ZOSAPI_NetHelper.dll", "Python.Runtime.dll")
    foreach ($dll in $zemaXDlls) {
        $dllPath = Join-Path $zosDir $dll
        if (Test-Path $dllPath) {
            Write-Host "  ✓ $dll found" -ForegroundColor Green
        } else {
            Write-Host "  Warning: $dll not found, Zemax functionality may not be available" -ForegroundColor Yellow
        }
    }
} else {
    Write-Host "Warning: ZOS directory not found: $zosDir" -ForegroundColor Yellow
    Write-Host "Zemax functionality may not work properly" -ForegroundColor Yellow
}

Write-Host "File verification completed" -ForegroundColor Green
Write-Host ""

Write-Host "[5/6] Checking PyInstaller..." -ForegroundColor Yellow
# Use conda run to check and install PyInstaller
$pyinstallerVersion = conda run -n $CondaEnv python -c "import PyInstaller; print(PyInstaller.__version__)" 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "PyInstaller version: $pyinstallerVersion" -ForegroundColor Green
} else {
    Write-Host "PyInstaller not installed, installing..." -ForegroundColor Yellow
    conda run -n $CondaEnv pip install pyinstaller
    if ($LASTEXITCODE -ne 0) {
        Write-Host "PyInstaller installation failed" -ForegroundColor Red
        exit 1
    }
    Write-Host "PyInstaller installation successful" -ForegroundColor Green
}
Write-Host ""

Write-Host "[6/6] Using PyInstaller for packaging..." -ForegroundColor Yellow
Write-Host "Using spec file: main.spec" -ForegroundColor Cyan

$buildArgs = @("--clean", "--noconfirm")
if ($Debug) {
    $buildArgs += "--debug=all"
    Write-Host "Debug mode enabled" -ForegroundColor Cyan
}

# Use conda run to execute PyInstaller
Write-Host "Executing command: conda run -n $CondaEnv pyinstaller $($buildArgs -join ' ') main.spec" -ForegroundColor Cyan
& conda run -n $CondaEnv pyinstaller @buildArgs main.spec
if ($LASTEXITCODE -ne 0) {
    Write-Host "Error: PyInstaller packaging failed" -ForegroundColor Red
    exit 1
}
Write-Host "PyInstaller packaging successful" -ForegroundColor Green
Write-Host ""

Write-Host "Fixing critical DLL dependencies..." -ForegroundColor Yellow

# Use conda run to get environment path and fix DLL dependencies
$condaEnvPath = conda run -n $CondaEnv python -c "import sys; print(sys.prefix)" 2>$null
if ($LASTEXITCODE -eq 0) {
    Write-Host "Conda environment path: $condaEnvPath" -ForegroundColor Cyan

    $sourceDll = Join-Path $condaEnvPath "Lib\site-packages\Python.Runtime.dll"
    $targetDir = "dist\25AutoAssembly"

    Write-Host "Source DLL path: $sourceDll" -ForegroundColor Cyan
    Write-Host "Target directory: $targetDir" -ForegroundColor Cyan

    # Check if source DLL exists
    if (Test-Path $sourceDll) {
        # Check if target directory exists
        if (Test-Path $targetDir) {
            Write-Host "Copying Python.Runtime.dll..." -ForegroundColor Cyan
            Copy-Item $sourceDll $targetDir -Force
            Write-Host "✓ Python.Runtime.dll copied successfully" -ForegroundColor Green
        } else {
            Write-Host "Error: Target directory not found: $targetDir" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "Warning: Source Python.Runtime.dll not found: $sourceDll" -ForegroundColor Yellow
    }
} else {
    Write-Host "Warning: Unable to get conda environment path" -ForegroundColor Yellow
}
Write-Host ""

Write-Host "Final verification and testing..." -ForegroundColor Yellow
$exePath = "dist\25AutoAssembly\25AutoAssembly.exe"
if (Test-Path $exePath) {
    $fileSize = (Get-Item $exePath).Length / 1MB
    Write-Host "✓ Executable file generated: $exePath" -ForegroundColor Green
    Write-Host "File size: $([math]::Round($fileSize, 2)) MB" -ForegroundColor Cyan
} else {
    Write-Host "Error: Executable file not generated" -ForegroundColor Red
    exit 1
}

# Check critical DLL files
$finalDll = "dist\25AutoAssembly\Python.Runtime.dll"
if (Test-Path $finalDll) {
    Write-Host "✓ Python.Runtime.dll found in final build" -ForegroundColor Green
} else {
    Write-Host "Warning: Python.Runtime.dll not found in final build" -ForegroundColor Yellow
}

# Check Zemax DLL files
$zemaXDlls = @("ZOSAPI.dll", "ZOSAPI_Interfaces.dll", "ZOSAPI_NetHelper.dll")
foreach ($dll in $zemaXDlls) {
    $dllPath = "dist\25AutoAssembly\$dll"
    if (Test-Path $dllPath) {
        Write-Host "✓ $dll included" -ForegroundColor Green
    } else {
        Write-Host "Warning: $dll not found" -ForegroundColor Yellow
    }
}

Write-Host "Final verification completed" -ForegroundColor Green
Write-Host ""

# Test run
if ($Test) {
    Write-Host "Executing quick startup test..." -ForegroundColor Yellow
    try {
        Start-Process -FilePath $exePath -WindowStyle Normal
        Start-Sleep -Seconds 3
        Write-Host "Startup test completed" -ForegroundColor Green
        Write-Host "Please manually verify program functionality" -ForegroundColor Yellow
    } catch {
        Write-Host "Program startup test failed: $_" -ForegroundColor Red
    }
}

Write-Host "======================================================" -ForegroundColor Cyan
Write-Host "Packaging completed successfully!" -ForegroundColor Green
Write-Host "======================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "Output location: dist\25AutoAssembly\" -ForegroundColor Cyan
Write-Host "Executable file: $exePath" -ForegroundColor Cyan
Write-Host ""
Write-Host "Fixed content:" -ForegroundColor Yellow
Write-Host "- Added Zemax DLL file support" -ForegroundColor White
Write-Host "- Optimized hidden import configuration" -ForegroundColor White
Write-Host "- Ensured Python.Runtime.dll is properly included" -ForegroundColor White
Write-Host "- Added icon file support" -ForegroundColor White
Write-Host "- Used conda environment for building" -ForegroundColor White
Write-Host ""

# Usage instructions
Write-Host "Usage instructions:" -ForegroundColor Yellow
Write-Host "   - Direct run: .\dist\25AutoAssembly\25AutoAssembly.exe" -ForegroundColor White
Write-Host "   - Clean rebuild: .\build_main.ps1 -Clean" -ForegroundColor White
Write-Host "   - Debug mode: .\build_main.ps1 -Debug" -ForegroundColor White
Write-Host "   - Build and test: .\build_main.ps1 -Test" -ForegroundColor White
Write-Host "   - Specify conda environment: .\build_main.ps1 -CondaEnv your_env" -ForegroundColor White
