@echo off
REM 25AutoAssembly - Zemax集成版环境配置脚本
REM 自动创建和配置Anaconda环境

echo ========================================
echo 25AutoAssembly - Zemax集成版环境配置
echo ========================================
echo.

REM 检查conda是否可用
conda --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到conda命令
    echo    请确保Anaconda或Miniconda已正确安装并添加到PATH
    pause
    exit /b 1
)

echo ✅ 检测到conda环境
conda --version
echo.

REM 检查环境配置文件是否存在
if not exist "environment_zemax_correct.yml" (
    echo ❌ 错误: 未找到环境配置文件 environment_zemax_correct.yml
    echo    请确保该文件在当前目录中
    pause
    exit /b 1
)

echo ✅ 找到环境配置文件: environment_zemax_correct.yml
echo.

REM 检查环境是否已存在
conda env list | findstr "25autoassembly" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  环境 '25autoassembly' 已存在
    set /p choice="是否删除现有环境并重新创建? (y/N): "
    if /i "%choice%"=="y" (
        echo 🗑️  删除现有环境...
        conda env remove -n 25autoassembly -y
        echo ✅ 现有环境已删除
    ) else (
        echo 📋 跳过环境创建，使用现有环境
        goto :activate_env
    )
)

echo 🚀 开始创建环境...
echo    这可能需要几分钟时间，请耐心等待...
echo.

REM 创建环境
conda env create -f environment_zemax_correct.yml
if %errorlevel% neq 0 (
    echo ❌ 环境创建失败
    echo    请检查网络连接和配置文件
    pause
    exit /b 1
)

echo ✅ 环境创建成功!
echo.

:activate_env
echo 🔧 激活环境并进行验证...
call conda activate 25autoassembly

REM 验证Python版本
echo 📋 验证Python版本...
python -c "import sys; print(f'Python版本: {sys.version}')"
if %errorlevel% neq 0 (
    echo ❌ Python验证失败
    pause
    exit /b 1
)

REM 验证核心依赖
echo 📋 验证核心依赖...
python -c "
try:
    import PyQt5
    print('✅ PyQt5 导入成功')
    
    import numpy as np
    print('✅ NumPy 导入成功')
    
    import matplotlib.pyplot as plt
    print('✅ Matplotlib 导入成功')
    
    import pandas as pd
    print('✅ Pandas 导入成功')
    
    import watchdog
    print('✅ Watchdog 导入成功')
    
    import netifaces
    print('✅ Netifaces 导入成功')
    
    print('\\n🎉 核心依赖验证完成!')
    
except ImportError as e:
    print(f'❌ 依赖导入失败: {e}')
    exit(1)
"

if %errorlevel% neq 0 (
    echo ❌ 依赖验证失败
    pause
    exit /b 1
)

REM 验证Zemax集成
echo 📋 验证Zemax集成...
python -c "
try:
    import clr
    print('✅ pythonnet (.NET集成) 导入成功')
    
    # 检查Zemax注册表
    import winreg
    import os
    try:
        aKey = winreg.OpenKey(
            winreg.ConnectRegistry(None, winreg.HKEY_CURRENT_USER), 
            r'Software\\Zemax', 0, winreg.KEY_READ
        )
        zemaxData = winreg.QueryValueEx(aKey, 'ZemaxRoot')
        zemax_root = zemaxData[0]
        winreg.CloseKey(aKey)
        print(f'✅ Zemax安装路径: {zemax_root}')
        
        # 检查关键DLL
        dll_path = os.path.join(zemax_root, r'ZOS-API\\Libraries\\ZOSAPI_NetHelper.dll')
        if os.path.exists(dll_path):
            print('✅ ZOSAPI_NetHelper.dll 存在')
        else:
            print('⚠️  ZOSAPI_NetHelper.dll 未找到，请检查Zemax安装')
            
    except Exception as e:
        print(f'⚠️  Zemax注册表检查失败: {e}')
        print('   请确保Zemax OpticStudio已正确安装')
        
except ImportError as e:
    print(f'❌ pythonnet导入失败: {e}')
    exit(1)
"

echo.
echo ========================================
echo 🎉 环境配置完成!
echo ========================================
echo.
echo 📋 使用说明:
echo    1. 激活环境: conda activate 25autoassembly
echo    2. 运行程序: python main.py
echo    3. 快速启动: python run.py
echo.
echo 📋 环境信息:
echo    - 环境名称: 25autoassembly
echo    - Python版本: 3.6.15
echo    - 支持Zemax ZOS-API集成
echo    - 包含完整的光学分析功能
echo.
echo ⚠️  注意事项:
echo    - 确保Zemax OpticStudio已安装并有有效许可证
echo    - 首次运行可能需要配置Zemax连接
echo    - 建议在Windows环境下使用
echo.

pause
