#!/usr/bin/env python3
"""
25AutoAssembly 运动控制界面
四层布局设计的运动控制界面
"""

import sys
import time
import logging
import numpy as np
from pathlib import Path
from typing import Optional

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
src_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(src_root))

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QGroupBox, QLabel, QPushButton, QLineEdit, QDoubleSpinBox,
    QGridLayout, QTextEdit, QRadioButton, QButtonGroup,
    QMessageBox, QFileDialog, QComboBox, QSpinBox, QSizePolicy,
    QStackedWidget, QSplitter, QTableWidget, QTableWidgetItem,
    QHeaderView, QMenuBar, QStatusBar, QAction, QActionGroup, QTabWidget,
    QCheckBox
)
from PyQt5.QtCore import Qt, QTimer, QSettings
from PyQt5.QtGui import QFont

from src.device_communication.tcp_motion_controller import TCPMotionController, DeviceType, get_all_local_ips
from src.data_models.zfr_data import ZFRData
from src.gui.industrial_styles import get_complete_style, INDUSTRIAL_COLORS
from src.optical_analysis.zemax_connector import ZemaxConnector
from src.optical_analysis.spot_diagram_analyzer import SpotDiagramAnalyzer, SpotPlotWidget
from src.optical_analysis.wavefront_plot_widget import WavefrontPlotWidget
from src.optical_analysis.wavefront_analyzer import WavefrontAnalyzer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MotionControlMainWindow(QMainWindow):
    """25AutoAssembly 运动控制主界面"""

    def __init__(self):
        super().__init__()

        # 设置窗口属性
        self.setWindowTitle("DigitalTwinAssemblyPlatform——数字孪生驱动的高性能装配平台")
        self.setMinimumSize(1200, 800)
        self.resize(1600, 1000)

        # 设置窗口居中
        self._center_window()

        # 核心组件
        self.tcp_controller = TCPMotionController()

        # {{ AURA: Add - 初始化Zemax连接器 }}
        # Zemax连接器
        self.zemax_connector = ZemaxConnector()
        self._setup_zemax_signals()

        # {{ AURA: Add - 初始化光学分析组件 }}
        # 光学分析组件（延迟初始化，等待Zemax连接后再创建）
        self.spot_analyzer = None
        self.wavefront_analyzer = WavefrontAnalyzer()

        # 当前数据
        self.current_zfr_data = None
        self.current_pose = None
        self.motion_level = 0.001  # 运动等级
        self.translation_rotation_ratio = 4  # 平移旋转比例因子
        self.data_path = ""  # ZFR数据路径

        # 状态标志
        self.is_connected = False
        self.device_selected = False
        self.zfr_data_loaded = False

        # 文件传输相关
        self.file_transfer_socket = None
        self.is_file_transfer_connected = False
        self.storage_path = ""
        self.current_transfer_zfr_data = None

        # 查询结果存储
        self.latest_query_result = None  # 存储最新的查询结果，供装配仿真使用

        # 装配仿真相关
        self.current_urdf_path = ""  # 当前加载的URDF文件路径
        self.urdf_parser = None  # URDF解析器
        self.motion_control_window = None  # 运动控制窗口

        # 布局管理相关
        self.right_column_widget = None  # 右栏容器引用
        self.display_areas_data = None  # 存储显示区域的数据和组件





        # 初始化显示区域配置数据
        self._init_display_areas_data()

        # 初始化QSettings - 使用INI文件格式，存储在程序运行目录
        import os
        import sys

        # 获取程序运行目录
        if getattr(sys, 'frozen', False):
            # 如果是打包后的exe程序
            app_dir = os.path.dirname(sys.executable)
        else:
            # 如果是开发环境
            app_dir = os.path.dirname(os.path.abspath(__file__))
            app_dir = os.path.dirname(os.path.dirname(app_dir))  # 回到项目根目录

        # 设置INI文件路径
        ini_file_path = os.path.join(app_dir, "DTAP.ini")
        self.settings = QSettings(ini_file_path, QSettings.IniFormat)
        self.settings.setIniCodec("UTF-8")  # 设置编码支持中文

        self._log_message(f"📄 配置文件路径: {ini_file_path}", "调试")

        # 加载文件传输配置
        self._load_file_transfer_config()

        # UI组件
        self.device_buttons = {}
        self.button_group = QButtonGroup()
        self.motion_inputs = {}

        # 创建菜单栏、状态栏
        self._create_menu_bar()
        self._create_status_bar()

        self._setup_ui()
        self._connect_signals()

        # 在UI创建完成后加载持久化设置
        self._load_persistent_settings()

        logger.info("DigitalTwinAssemblyPlatform 数字孪生装配平台初始化完成")

    def _setup_zemax_signals(self):
        """设置Zemax连接器的信号连接"""
        self.zemax_connector.connection_status_changed.connect(self._on_zemax_status_changed)
        self.zemax_connector.progress_updated.connect(self._on_zemax_progress)
        self.zemax_connector.connection_started.connect(self._on_zemax_connection_started)
        self.zemax_connector.connection_finished.connect(self._on_zemax_connection_finished)

    def _setup_wavefront_signals(self):
        """设置波前分析信号连接"""
        try:
            # {{ AURA: Add - 连接参数控制信号到更新方法 }}
            # 连接参数变化信号到波前更新方法
            self.wavefront_sampling_combo.currentTextChanged.connect(self._update_wavefront_map)
            self.wavefront_rotation_combo.currentTextChanged.connect(self._update_wavefront_map)
            self.wavefront_wavelength_combo.currentIndexChanged.connect(self._update_wavefront_map)
            self.wavefront_field_combo.currentIndexChanged.connect(self._update_wavefront_map)

            # {{ AURA: Add - 连接波前分析器信号 }}
            # 连接波前分析器信号
            self.wavefront_analyzer.analysis_started.connect(
                lambda: self._log_message("🔄 开始波前分析...")
            )
            self.wavefront_analyzer.analysis_progress.connect(
                lambda msg: self._log_message(f"📊 {msg}")
            )
            self.wavefront_analyzer.analysis_completed.connect(
                self._on_wavefront_analysis_completed
            )
            self.wavefront_analyzer.analysis_failed.connect(
                lambda error: self._log_message(f"❌ 波前分析失败: {error}")
            )

            logger.info("波前分析信号连接完成")

        except Exception as e:
            logger.error(f"设置波前分析信号失败: {e}")

    def _update_wavefront_map(self):
        """更新波前图显示（严格参考OpticalRealSim的update_wavefront_map实现）"""
        try:
            # {{ AURA: Fix - 参考点列图分析的连接检查方式 }}
            # 检查Zemax连接状态（参考_run_spot_diagram_analysis的实现）
            zemax_app = self.zemax_connector.get_zemax_app()
            if not zemax_app:
                self._log_message("⚠️ 无法获取Zemax应用程序对象，请先连接Zemax")
                self.wavefront_plot_widget.show_message("请先连接Zemax", "波前图")
                return

            # {{ AURA: Add - 获取当前参数设置 }}
            # 获取UI参数
            sampling = self.wavefront_sampling_combo.currentText()
            rotation = int(self.wavefront_rotation_combo.currentText())
            wavelength_index = self.wavefront_wavelength_combo.currentIndex()
            field_index = self.wavefront_field_combo.currentIndex()

            # {{ AURA: Add - 参数变化时重置PV和RMS显示 }}
            # 清空PV和RMS显示，等待新的分析结果
            self.wavefront_pv_label.setText("--")
            self.wavefront_rms_label.setText("--")

            self._log_message(f"📊 开始波前分析: 采样={sampling}, 旋转={rotation}°, 波长={wavelength_index+1}, 视场={field_index+1}")

            # {{ AURA: Fix - 直接设置Zemax应用实例 }}
            # 设置Zemax应用实例到分析器
            self.wavefront_analyzer.set_zemax_application(zemax_app)

            # {{ AURA: Add - 执行波前分析 }}
            # 执行分析
            self.wavefront_analyzer.analyze_wavefront(
                sampling=sampling,
                rotation=rotation,
                wavelength_index=wavelength_index,
                field_index=field_index
            )

        except Exception as e:
            error_msg = f"波前分析失败: {str(e)}"
            logger.error(error_msg)
            self._log_message(f"❌ {error_msg}")
            self.wavefront_plot_widget.show_message(f"分析失败:\n{str(e)}", "波前图错误")

    def _on_wavefront_analysis_completed(self, result: dict):
        """处理波前分析完成事件"""
        try:
            # {{ AURA: Add - 更新波前图显示 }}
            if result.get('success', False) and 'wavefront_data' in result:
                wavefront_data = result['wavefront_data']

                # 构建标题
                sampling = result.get('sampling', 'Unknown')
                wavelength_idx = result.get('wavelength_index', 0)
                field_idx = result.get('field_index', 0)
                title = f"波前图 - {sampling}, 波长{wavelength_idx+1}, 视场{field_idx+1}"

                # 绘制波前图
                self.wavefront_plot_widget.plot_grid_image(wavefront_data, title=title)

                # {{ AURA: Add - 更新PV和RMS显示，参考ref/zpy_wavefront.py }}
                # 显示统计信息
                pv_value = result.get('pv_value', 0.0)
                rms_value = result.get('rms_value', 0.0)

                # 更新UI中的PV和RMS显示
                self.wavefront_pv_label.setText(f"{pv_value:.6f}")
                self.wavefront_rms_label.setText(f"{rms_value:.6f}")

                self._log_message(f"✅ 波前分析完成 - PV: {pv_value:.6f}, RMS: {rms_value:.6f}")

            else:
                # {{ AURA: Add - 分析失败时重置PV和RMS显示 }}
                self.wavefront_pv_label.setText("--")
                self.wavefront_rms_label.setText("--")
                self._log_message("❌ 波前分析结果无效")
                self.wavefront_plot_widget.show_message("分析结果无效", "波前图错误")

        except Exception as e:
            error_msg = f"处理波前分析结果失败: {str(e)}"
            logger.error(error_msg)
            self._log_message(f"❌ {error_msg}")

    def _init_display_areas_data(self):
        """初始化显示区域配置数据"""
        self.display_areas_data = [
            {
                "title": "Inspection",
                "content": "装配仿真功能模块\n(待开发)",
                "bg_color": "#E3F2FD",
                "is_assembly": True  # 标记为装配仿真区域，需要特殊处理
            },
            {
                "title": "Spot Diagram",
                "content": "光学实测数据仿真\n(待开发)",
                "bg_color": "#F3E5F5",
                "is_assembly": False
            },
            {
                "title": "Wavefront Map",
                "content": "系统状态监控模块\n(待开发)",
                "bg_color": "#E8F5E8",
                "is_assembly": False
            },
            {
                "title": "MTF",
                "content": "其他扩展功能\n(待开发)",
                "bg_color": "#FFF3E0",
                "is_assembly": False
            },
            {
                "title": "PSF",
                "content": "PSF分析功能\n(待开发)",
                "bg_color": "#E1F5FE",
                "is_assembly": False
            },
            {
                "title": "Zernike",
                "content": "Zernike数据分析功能",
                "bg_color": "#F1F8E9",
                "is_assembly": False,
                "is_zernike": True  # 标记为Zernike标签页，需要特殊处理
            }
        ]

    def _create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu('文件(&F)')

        # 连接服务器
        connect_action = QAction('连接服务器(&C)', self)
        connect_action.setShortcut('Ctrl+C')
        connect_action.setStatusTip('连接到文件传输服务器')
        connect_action.triggered.connect(self._connect_to_server)
        file_menu.addAction(connect_action)

        # 断开连接
        disconnect_action = QAction('断开连接(&D)', self)
        disconnect_action.setShortcut('Ctrl+D')
        disconnect_action.setStatusTip('断开文件传输服务器连接')
        disconnect_action.triggered.connect(self._disconnect_from_server)
        file_menu.addAction(disconnect_action)

        file_menu.addSeparator()

        # 接收文件
        receive_action = QAction('接收文件(&R)', self)
        receive_action.setShortcut('Ctrl+R')
        receive_action.setStatusTip('从服务器接收ZFR文件')
        receive_action.triggered.connect(self._receive_file)
        file_menu.addAction(receive_action)

        # 设置存储路径
        storage_action = QAction('设置存储路径(&S)', self)
        storage_action.setStatusTip('设置文件存储路径')
        storage_action.triggered.connect(self._browse_storage_path)
        file_menu.addAction(storage_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction('退出(&X)', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.setStatusTip('退出应用程序')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 运动控制菜单
        motion_menu = menubar.addMenu('运动控制(&M)')

        # 启动TCP服务器
        start_server_action = QAction('启动TCP服务器(&S)', self)
        start_server_action.setStatusTip('启动运动控制TCP服务器')
        start_server_action.triggered.connect(self._start_tcp_server)
        motion_menu.addAction(start_server_action)

        # 停止TCP服务器
        stop_server_action = QAction('停止TCP服务器(&T)', self)
        stop_server_action.setStatusTip('停止运动控制TCP服务器')
        stop_server_action.triggered.connect(self._stop_tcp_server)
        motion_menu.addAction(stop_server_action)

        motion_menu.addSeparator()

        # 刷新位姿
        refresh_pose_action = QAction('刷新位姿(&P)', self)
        refresh_pose_action.setShortcut('F5')
        refresh_pose_action.setStatusTip('刷新当前设备位姿')
        refresh_pose_action.triggered.connect(self._refresh_pose)
        motion_menu.addAction(refresh_pose_action)

        # 执行运动
        execute_motion_action = QAction('执行运动(&E)', self)
        execute_motion_action.setShortcut('Ctrl+E')
        execute_motion_action.setStatusTip('执行6轴运动指令')
        execute_motion_action.triggered.connect(self._execute_motion)
        motion_menu.addAction(execute_motion_action)

        # 数据菜单
        data_menu = menubar.addMenu('数据(&D)')

        # 清空历史记录
        clear_history_action = QAction('清空历史记录(&C)', self)
        clear_history_action.setStatusTip('清空Zernike数据历史记录')
        clear_history_action.triggered.connect(self._clear_zernike_history)
        data_menu.addAction(clear_history_action)

        # 导出历史记录
        export_history_action = QAction('导出历史记录(&E)', self)
        export_history_action.setStatusTip('导出Zernike数据历史记录到CSV文件')
        export_history_action.triggered.connect(self._export_zernike_history)
        data_menu.addAction(export_history_action)



        # 帮助菜单
        help_menu = menubar.addMenu('帮助(&H)')

        # 关于
        about_action = QAction('关于(&A)', self)
        about_action.setStatusTip('关于DigitalTwinAssemblyPlatform数字孪生装配平台')
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)



    def _create_status_bar(self):
        """创建状态栏"""
        self.status_bar = self.statusBar()

        # 连接状态标签
        self.connection_status_label = QLabel("未连接")
        self.connection_status_label.setStyleSheet("color: red; font-weight: bold;")
        self.status_bar.addWidget(self.connection_status_label)

        self.status_bar.addPermanentWidget(QLabel("|"))

        # TCP服务器状态标签
        self.server_status_label = QLabel("TCP服务器: 未启动")
        self.server_status_label.setStyleSheet("color: red; font-weight: bold;")
        self.status_bar.addPermanentWidget(self.server_status_label)

        self.status_bar.addPermanentWidget(QLabel("|"))

        # 设备状态标签
        self.device_status_label = QLabel("设备: 未选择")
        self.device_status_label.setStyleSheet("color: orange; font-weight: bold;")
        self.status_bar.addPermanentWidget(self.device_status_label)

        self.status_bar.addPermanentWidget(QLabel("|"))

        # 位姿缓存状态标签
        self.cache_status_label = QLabel("智能缓存: 开启")
        self.cache_status_label.setStyleSheet("color: green; font-weight: bold;")
        self.status_bar.addPermanentWidget(self.cache_status_label)

        # 默认状态消息
        self.status_bar.showMessage("就绪 - DigitalTwinAssemblyPlatform数字孪生装配平台")

    def _show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于DigitalTwinAssemblyPlatform",
                         "DigitalTwinAssemblyPlatform\n"
                         "数字孪生驱动的高性能装配平台\n\n"
                         "版本: 1.0.0\n"
                         "功能: 数字孪生装配建模、光机系统仿真、装配过程检测、装配过程运动控制\n"
                         "特性: 智能装配、实时监控、精密控制\n\n"
                         "© 2024 北京航空航天大学数字化制造项目组")

    def _center_window(self):
        """将窗口居中显示"""
        from PyQt5.QtWidgets import QDesktopWidget
        screen = QDesktopWidget().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )

    def _setup_ui(self):
        """设置用户界面 - 三栏布局"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 应用工业级样式
        #self.setStyleSheet(get_complete_style())

        # 设置全局字体
        font = QFont("Microsoft YaHei", 11)
        font.setWeight(QFont.Normal)
        self.setFont(font)

        # 主布局：垂直布局包含主要splitter
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(8)

        # 主垂直splitter：分隔三栏区域和日志区域
        self.main_vertical_splitter = QSplitter(Qt.Vertical)
        self.main_vertical_splitter.setChildrenCollapsible(True)  # 允许日志区域折叠隐藏

        # 两栏主要内容区域 - 使用QSplitter实现可拖拽调整
        self.two_column_splitter = QSplitter(Qt.Horizontal)
        self.two_column_splitter.setChildrenCollapsible(False)  # 防止栏目被完全折叠

        # 左栏：运动控制区域（TCP服务器、设备选择、位姿显示、控制）
        left_column_widget = QWidget()
        left_column_widget.setMinimumWidth(400)
        self._create_middle_motion_column(left_column_widget)
        self.two_column_splitter.addWidget(left_column_widget)

        # 右栏：标签页显示区域
        self.right_column_widget = QWidget()
        self.right_column_widget.setMinimumWidth(500)
        self._create_right_display_column(self.right_column_widget)
        self.two_column_splitter.addWidget(self.right_column_widget)

        # 设置两栏的初始大小比例 (左:右 = 400:600)
        self.two_column_splitter.setSizes([400, 600])

        # 设置拉伸因子 (左:中:右 = 3:3:4)
        self.three_column_splitter.setStretchFactor(0, 3)  # 左栏
        self.three_column_splitter.setStretchFactor(1, 3)  # 中栏
        self.three_column_splitter.setStretchFactor(2, 4)  # 右栏

        # 将两栏区域添加到主垂直splitter
        self.main_vertical_splitter.addWidget(self.two_column_splitter)

        # 创建系统日志区域
        log_widget = QWidget()
        self._create_system_log_section_widget(log_widget)
        self.main_vertical_splitter.addWidget(log_widget)

        # 设置主垂直splitter的初始大小比例 (主要内容:日志 = 4:1)
        self.main_vertical_splitter.setSizes([800, 200])
        self.main_vertical_splitter.setStretchFactor(0, 4)  # 三栏主要内容
        self.main_vertical_splitter.setStretchFactor(1, 1)  # 系统日志

        main_layout.addWidget(self.main_vertical_splitter)



    def _create_zernike_tab_content(self, parent_layout):
        """创建Zernike标签页内容 - 迁移左栏的所有Zernike控件"""
        parent_layout.setContentsMargins(5, 5, 5, 5)
        parent_layout.setSpacing(8)

        # 1. Zernike存储配置
        storage_group = QGroupBox("Zernike存储配置")
        storage_layout = QVBoxLayout(storage_group)
        storage_layout.setSpacing(5)

        # 存储路径选择
        path_layout = QHBoxLayout()
        path_layout.addWidget(QLabel("存储路径:"))
        self.storage_path_edit = QLineEdit()
        self.storage_path_edit.setPlaceholderText("选择存储路径...")
        self.storage_path_edit.setReadOnly(True)
        path_layout.addWidget(self.storage_path_edit)

        self.browse_btn = QPushButton("路径选择")
        self.browse_btn.clicked.connect(self._browse_storage_path)
        path_layout.addWidget(self.browse_btn)
        storage_layout.addLayout(path_layout)

        parent_layout.addWidget(storage_group)

        # 2. Zernike连接配置
        connection_group = QGroupBox("Zernike连接配置")
        connection_layout = QVBoxLayout(connection_group)
        connection_layout.setSpacing(5)

        # 服务器IP和端口
        server_layout = QGridLayout()
        server_layout.addWidget(QLabel("服务器IP:"), 0, 0)
        self.server_ip_combo = QComboBox()
        self.server_ip_combo.setEditable(True)
        self.server_ip_combo.setMinimumWidth(140)  # 确保能显示完整IP地址
        self.server_ip_combo.addItem("************")
        server_layout.addWidget(self.server_ip_combo, 0, 1)

        server_layout.addWidget(QLabel("端口:"), 1, 0)
        self.server_port_edit = QLineEdit("8899")
        server_layout.addWidget(self.server_port_edit, 1, 1)

        connection_layout.addLayout(server_layout)

        # 连接控制按钮
        button_layout = QHBoxLayout()
        self.connect_btn = QPushButton("连接服务器")
        self.connect_btn.clicked.connect(self._connect_to_server)
        button_layout.addWidget(self.connect_btn)

        self.disconnect_btn = QPushButton("断开连接")
        self.disconnect_btn.clicked.connect(self._disconnect_from_server)
        self.disconnect_btn.setEnabled(False)
        button_layout.addWidget(self.disconnect_btn)

        self.receive_btn = QPushButton("接收文件")
        self.receive_btn.clicked.connect(self._receive_file)
        self.receive_btn.setEnabled(False)
        button_layout.addWidget(self.receive_btn)

        connection_layout.addLayout(button_layout)
        parent_layout.addWidget(connection_group)

        # 3. Zernike数据分析
        analysis_group = QGroupBox("Zernike数据分析")
        analysis_layout = QVBoxLayout(analysis_group)
        analysis_layout.setSpacing(5)

        # ZFR数据显示
        zfr_grid = QGridLayout()
        zfr_labels = ["PV:", "RMS:", "Power:", "Coma_X:", "Coma_Y:"]
        self.transfer_zfr_edits = {}

        for i, label_text in enumerate(zfr_labels):
            label = QLabel(label_text)
            zfr_grid.addWidget(label, i, 0)

            edit = QLineEdit()
            edit.setReadOnly(True)
            edit.setStyleSheet("background-color: #f0f0f0;")
            zfr_grid.addWidget(edit, i, 1)

            # 保存编辑框引用
            key = label_text.lower().replace(":", "").replace("_", "_")
            self.transfer_zfr_edits[key] = edit

        analysis_layout.addLayout(zfr_grid)

        # 控制按钮
        control_layout = QHBoxLayout()
        self.transfer_power_control_btn = QPushButton("Power控制")
        self.transfer_power_control_btn.clicked.connect(self._transfer_power_motion_control)
        self.transfer_power_control_btn.setEnabled(False)
        control_layout.addWidget(self.transfer_power_control_btn)

        self.transfer_coma_x_control_btn = QPushButton("Coma_X控制")
        self.transfer_coma_x_control_btn.clicked.connect(self._transfer_coma_x_motion_control)
        self.transfer_coma_x_control_btn.setEnabled(False)
        control_layout.addWidget(self.transfer_coma_x_control_btn)

        self.transfer_coma_y_control_btn = QPushButton("Coma_Y控制")
        self.transfer_coma_y_control_btn.clicked.connect(self._transfer_coma_y_motion_control)
        self.transfer_coma_y_control_btn.setEnabled(False)
        control_layout.addWidget(self.transfer_coma_y_control_btn)

        analysis_layout.addLayout(control_layout)
        parent_layout.addWidget(analysis_group)

        # 4. Zernike数据历史记录
        history_group = QGroupBox("Zernike数据历史记录")
        history_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)  # 允许组随窗口拉伸
        history_layout = QVBoxLayout(history_group)
        history_layout.setSpacing(5)

        # 创建历史记录表格
        self.zernike_history_table = QTableWidget()
        self.zernike_history_table.setColumnCount(6)  # 6列：时间、PV、RMS、Power、Coma_X、Coma_Y

        # 设置表头
        headers = ["时间", "PV", "RMS", "Power", "Coma_X", "Coma_Y"]
        self.zernike_history_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.zernike_history_table.setAlternatingRowColors(True)  # 交替行颜色
        self.zernike_history_table.setSelectionBehavior(QTableWidget.SelectRows)  # 选择整行
        self.zernike_history_table.setEditTriggers(QTableWidget.NoEditTriggers)  # 只读
        self.zernike_history_table.setMinimumHeight(150)  # 设置最小高度
        # 移除最大高度限制，允许表格随窗口拉伸
        self.zernike_history_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 设置表头拉伸模式 - 所有列等宽并填充整个区域
        header = self.zernike_history_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)  # 所有列等宽拉伸填充

        # 设置表格样式
        self.zernike_history_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #e0e0e0;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)

        history_layout.addWidget(self.zernike_history_table)

        # 历史记录控制按钮
        history_control_layout = QHBoxLayout()

        # 清空历史记录按钮
        self.clear_history_btn = QPushButton("清空历史")
        self.clear_history_btn.clicked.connect(self._clear_zernike_history)
        history_control_layout.addWidget(self.clear_history_btn)

        # 导出历史记录按钮
        self.export_history_btn = QPushButton("导出历史")
        self.export_history_btn.clicked.connect(self._export_zernike_history)
        history_control_layout.addWidget(self.export_history_btn)

        history_control_layout.addStretch()  # 添加弹性空间
        history_layout.addLayout(history_control_layout)

        parent_layout.addWidget(history_group)

        # 保持兼容性的引用
        self.transfer_pv_edit = self.transfer_zfr_edits["pv"]
        self.transfer_rms_edit = self.transfer_zfr_edits["rms"]
        self.transfer_power_edit = self.transfer_zfr_edits["power"]
        self.transfer_coma_x_edit = self.transfer_zfr_edits["coma_x"]
        self.transfer_coma_y_edit = self.transfer_zfr_edits["coma_y"]

    def _create_middle_motion_column(self, parent_widget):
        """创建中栏：运动控制区域（TCP服务器、设备选择、位姿显示、控制）"""
        layout = QVBoxLayout(parent_widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(8)

        # 1. TCP服务器控制
        tcp_group = QGroupBox("TCP服务器控制")
        tcp_layout = QVBoxLayout(tcp_group)
        tcp_layout.setSpacing(5)

        # IP和端口配置
        ip_layout = QHBoxLayout()
        ip_layout.addWidget(QLabel("本机IP:"))
        self.local_ip_combo = QComboBox()
        self.local_ip_combo.setMinimumWidth(140)  # 确保能显示完整IP地址
        self._populate_ip_addresses()
        self.local_ip_combo.currentTextChanged.connect(self._on_ip_selection_changed)
        ip_layout.addWidget(self.local_ip_combo)

        self.refresh_ip_btn = QPushButton("🔄")
        self.refresh_ip_btn.setMaximumWidth(30)
        self.refresh_ip_btn.clicked.connect(self._populate_ip_addresses)
        ip_layout.addWidget(self.refresh_ip_btn)

        ip_layout.addWidget(QLabel("端口:"))
        self.port_spinbox = QSpinBox()
        self.port_spinbox.setRange(1, 65535)
        self.port_spinbox.setValue(5050)
        self.port_spinbox.setMinimumWidth(80)
        ip_layout.addWidget(self.port_spinbox)

        tcp_layout.addLayout(ip_layout)

        # 服务器控制按钮
        server_button_layout = QHBoxLayout()
        self.start_server_btn = QPushButton("启动服务器")
        self.start_server_btn.clicked.connect(self._start_tcp_server)
        server_button_layout.addWidget(self.start_server_btn)

        self.stop_server_btn = QPushButton("停止服务器")
        self.stop_server_btn.clicked.connect(self._stop_tcp_server)
        self.stop_server_btn.setEnabled(False)
        server_button_layout.addWidget(self.stop_server_btn)

        self.query_btn = QPushButton("查询")
        self.query_btn.clicked.connect(self._query_devices)
        self.query_btn.setEnabled(False)
        server_button_layout.addWidget(self.query_btn)

        tcp_layout.addLayout(server_button_layout)
        layout.addWidget(tcp_group)

        # 2. 设备选择
        device_group = QGroupBox("设备选择")
        device_layout = QHBoxLayout(device_group)
        device_layout.setSpacing(15)  # 增加间距

        for device_type in DeviceType:
            device_container = QWidget()
            device_container.setStyleSheet("""
                QWidget {
                    border: 2px solid #ddd;
                    border-radius: 8px;
                    background-color: #f9f9f9;
                    padding: 8px;
                }
                QWidget:hover {
                    border-color: #007acc;
                    background-color: #f0f8ff;
                }
            """)
            device_container_layout = QVBoxLayout(device_container)
            device_container_layout.setContentsMargins(10, 10, 10, 10)
            device_container_layout.setSpacing(8)

            # 设备名称（更大字体）
            radio = QRadioButton(f"{device_type.display_name}")
            radio.setStyleSheet("""
                QRadioButton {
                    font-size: 16px;
                    font-weight: bold;
                    color: #333;
                }
                QRadioButton::indicator {
                    width: 18px;
                    height: 18px;
                }
            """)

            # IP地址（更大字体，更明显）
            ip_label = QLabel(device_type.ip_address)
            ip_label.setStyleSheet("""
                QLabel {
                    color: #007acc;
                    font-size: 14px;
                    font-weight: 500;
                    background-color: #e8f4fd;
                    border: 1px solid #b3d9ff;
                    border-radius: 4px;
                    padding: 4px 8px;
                }
            """)
            ip_label.setAlignment(Qt.AlignCenter)

            device_container_layout.addWidget(radio)
            device_container_layout.addWidget(ip_label)
            device_layout.addWidget(device_container)

            self.device_buttons[device_type] = radio
            self.button_group.addButton(radio)
            radio.toggled.connect(lambda checked, dt=device_type: self._on_device_selected(dt, checked))

        # 默认选择次镜
        self.device_buttons[DeviceType.SUBMIRROR].setChecked(True)
        layout.addWidget(device_group)

        # 3. 当前位姿显示
        pose_group = QGroupBox("当前设备位姿")
        pose_layout = QVBoxLayout(pose_group)
        pose_layout.setSpacing(5)

        # 位姿数据网格
        pose_grid = QGridLayout()
        pose_grid.setSpacing(3)

        # 位移显示
        pos_labels = ["X(mm):", "Y(mm):", "Z(mm)"]
        self.pos_labels = []
        for i, label_text in enumerate(pos_labels):
            label = QLabel(label_text)
            pose_grid.addWidget(label, 0, i*2)

            value_edit = QLineEdit("--")
            value_edit.setReadOnly(True)
            value_edit.setStyleSheet("background-color: #f0f0f0;")
            pose_grid.addWidget(value_edit, 0, i*2+1)
            self.pos_labels.append(value_edit)

        # 角度显示
        rot_labels = ["A(°):", "B(°):", "C(°):"]
        self.rot_labels = []
        for i, label_text in enumerate(rot_labels):
            label = QLabel(label_text)
            pose_grid.addWidget(label, 1, i*2)

            value_edit = QLineEdit("--")
            value_edit.setReadOnly(True)
            value_edit.setStyleSheet("background-color: #f0f0f0;")
            pose_grid.addWidget(value_edit, 1, i*2+1)
            self.rot_labels.append(value_edit)

        pose_layout.addLayout(pose_grid)

        # 刷新按钮
        refresh_layout = QHBoxLayout()
        self.refresh_pose_btn = QPushButton("刷新位姿")
        self.refresh_pose_btn.clicked.connect(self._refresh_pose)
        self.refresh_pose_btn.setEnabled(False)
        refresh_layout.addWidget(self.refresh_pose_btn)

        # 位姿缓存控制按钮
        self.pose_cache_btn = QPushButton("智能缓存: 开")
        self.pose_cache_btn.clicked.connect(self._toggle_pose_cache)
        self.pose_cache_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 5px 10px;
                border-radius: 3px;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        refresh_layout.addWidget(self.pose_cache_btn)

        refresh_layout.addStretch()
        pose_layout.addLayout(refresh_layout)

        layout.addWidget(pose_group)

        # 保持原有的标签引用以兼容现有代码
        self.pos_x_label = self.pos_labels[0]
        self.pos_y_label = self.pos_labels[1]
        self.pos_z_label = self.pos_labels[2]
        self.rot_a_label = self.rot_labels[0]
        self.rot_b_label = self.rot_labels[1]
        self.rot_c_label = self.rot_labels[2]

        # 4. 运动参数设置
        params_group = QGroupBox("运动参数设置")
        params_layout = QHBoxLayout(params_group)
        params_layout.setSpacing(10)

        # 运动等级
        params_layout.addWidget(QLabel("运动等级:"))
        self.level_combo = QComboBox()
        self.level_combo.addItems(["精细 (0.0006)", "标准 (0.001)", "粗调 (0.01)"])
        self.level_combo.setCurrentIndex(1)
        self.level_combo.currentIndexChanged.connect(self._on_level_changed)
        params_layout.addWidget(self.level_combo)

        # 平移旋转比例因子
        params_layout.addWidget(QLabel("比例因子:"))
        self.ratio_spinbox = QSpinBox()
        self.ratio_spinbox.setRange(1, 10)
        self.ratio_spinbox.setValue(4)
        self.ratio_spinbox.valueChanged.connect(self._on_ratio_changed)
        params_layout.addWidget(self.ratio_spinbox)

        params_layout.addStretch()
        layout.addWidget(params_group)

        # 5. 6轴运动控制
        motion_group = QGroupBox("6轴运动控制")
        motion_layout = QVBoxLayout(motion_group)
        motion_layout.setSpacing(5)

        # 6轴输入网格
        axes_layout = QGridLayout()
        axes_layout.setSpacing(3)

        # 位移轴和旋转轴
        translation_axes = [('X', 'mm'), ('Y', 'mm'), ('Z', 'mm')]
        rotation_axes = [('A', '°'), ('B', '°'), ('C', '°')]

        # 位移轴 (第一行)
        for i, (axis, unit) in enumerate(translation_axes):
            axis_label = QLabel(f"{axis}轴:")
            axes_layout.addWidget(axis_label, 0, i*2)

            pos_input = QDoubleSpinBox()
            pos_input.setRange(-100.0, 100.0)
            pos_input.setValue(0.0)
            pos_input.setSingleStep(0.001)
            pos_input.setDecimals(4)
            pos_input.setSuffix(f" {unit}")
            pos_input.setMinimumWidth(120)  # 设置最小宽度以显示完整的小数点后4位
            axes_layout.addWidget(pos_input, 0, i*2+1)

            self.motion_inputs[axis.lower()] = pos_input

        # 旋转轴 (第二行)
        for i, (axis, unit) in enumerate(rotation_axes):
            axis_label = QLabel(f"{axis}轴:")
            axes_layout.addWidget(axis_label, 1, i*2)

            rot_input = QDoubleSpinBox()
            rot_input.setRange(-360.0, 360.0)
            rot_input.setValue(0.0)
            rot_input.setSingleStep(0.001)
            rot_input.setDecimals(4)
            rot_input.setSuffix(f" {unit}")
            rot_input.setMinimumWidth(120)  # 设置最小宽度以显示完整的小数点后4位
            axes_layout.addWidget(rot_input, 1, i*2+1)

            self.motion_inputs[axis.lower()] = rot_input

        motion_layout.addLayout(axes_layout)

        # 控制按钮
        button_layout = QHBoxLayout()
        self.execute_btn = QPushButton("执行运动")
        self.execute_btn.clicked.connect(self._execute_motion)
        self.execute_btn.setEnabled(False)
        button_layout.addWidget(self.execute_btn)

        self.stop_btn = QPushButton("紧急停止")
        self.stop_btn.clicked.connect(self._emergency_stop)
        button_layout.addWidget(self.stop_btn)

        button_layout.addStretch()
        motion_layout.addLayout(button_layout)

        layout.addWidget(motion_group)

        # 6. 运动控制历史记录
        motion_history_group = QGroupBox("运动控制历史记录")
        motion_history_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)  # 允许组随窗口拉伸
        motion_history_layout = QVBoxLayout(motion_history_group)
        motion_history_layout.setSpacing(5)

        # 创建运动控制历史记录表格
        self.motion_history_table = QTableWidget()
        self.motion_history_table.setColumnCount(14)  # 14列：时间、设备、X、Y、Z、A、B、C、运动X、运动Y、运动Z、运动A、运动B、运动C

        # 设置表头
        headers = ["时间", "设备", "X(mm)", "Y(mm)", "Z(mm)", "A(°)", "B(°)", "C(°)", "运动X", "运动Y", "运动Z", "运动A", "运动B", "运动C"]
        self.motion_history_table.setHorizontalHeaderLabels(headers)

        # 设置表格属性
        self.motion_history_table.setAlternatingRowColors(True)  # 交替行颜色
        self.motion_history_table.setSelectionBehavior(QTableWidget.SelectRows)  # 选择整行
        self.motion_history_table.setEditTriggers(QTableWidget.NoEditTriggers)  # 只读
        self.motion_history_table.setMinimumHeight(150)  # 设置最小高度
        # 移除最大高度限制，允许表格随窗口拉伸
        self.motion_history_table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        # 设置表头拉伸模式 - 所有列等宽并填充整个区域
        header = self.motion_history_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)  # 所有列等宽拉伸填充

        # 设置表格样式
        self.motion_history_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #e0e0e0;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
            QHeaderView::section {
                background-color: #f8f9fa;
                padding: 8px;
                border: 1px solid #dee2e6;
                font-weight: bold;
            }
        """)

        motion_history_layout.addWidget(self.motion_history_table)

        # 运动控制历史记录控制按钮
        motion_history_control_layout = QHBoxLayout()

        # 清空历史记录按钮
        self.clear_motion_history_btn = QPushButton("清空历史")
        self.clear_motion_history_btn.clicked.connect(self._clear_motion_history)
        motion_history_control_layout.addWidget(self.clear_motion_history_btn)

        # 导出历史记录按钮
        self.export_motion_history_btn = QPushButton("导出历史")
        self.export_motion_history_btn.clicked.connect(self._export_motion_history)
        motion_history_control_layout.addWidget(self.export_motion_history_btn)

        motion_history_control_layout.addStretch()  # 添加弹性空间
        motion_history_layout.addLayout(motion_history_control_layout)

        layout.addWidget(motion_history_group)

        # 添加弹性空间
        layout.addStretch()

    def _create_right_display_column(self, parent_widget):
        """创建右栏：四个显示占位区域，固定使用标签页布局"""
        # 检查是否已经有布局
        existing_layout = parent_widget.layout()

        if existing_layout is None:
            # 创建新布局
            layout = QVBoxLayout(parent_widget)
            layout.setContentsMargins(5, 5, 5, 5)
            layout.setSpacing(8)
        else:
            # 重用现有布局
            layout = existing_layout
            layout.setContentsMargins(5, 5, 5, 5)
            layout.setSpacing(8)

        # 直接创建标签页布局
        self._create_tab_layout(layout)



    def _create_tab_layout(self, parent_layout):
        """创建标签页布局"""
        # 创建标签页控件
        tab_widget = QTabWidget()
        tab_widget.setTabPosition(QTabWidget.North)  # 标签位置在顶部

        for i, area_data in enumerate(self.display_areas_data):
            # 创建显示区域组件
            area_widget = self._create_display_area_widget(area_data, i)
            # 添加到标签页
            tab_widget.addTab(area_widget, area_data["title"])

        parent_layout.addWidget(tab_widget)

    def _create_display_area_widget(self, area_data, index):
        """创建单个显示区域组件"""
        title = area_data["title"]
        content = area_data["content"]
        bg_color = area_data["bg_color"]
        is_assembly = area_data["is_assembly"]
        is_zernike = area_data.get("is_zernike", False)  # 检查是否为Zernike标签页

        # 创建显示区域组
        area_group = QGroupBox(title)
        # 设置大小策略为可扩展，使其能够随窗口拉伸
        area_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        area_layout = QVBoxLayout(area_group)
        area_layout.setSpacing(5)

        if is_zernike:  # Zernike标签页 - 特殊处理，创建Zernike控件
            self._create_zernike_tab_content(area_layout)
        elif is_assembly:  # 装配仿真显示 - 特殊处理，采用上下布局
            # 上方：控件区域
            control_widget = QWidget()
            control_layout = QHBoxLayout(control_widget)
            control_layout.setContentsMargins(5, 5, 5, 5)

            # 添加"打开"按钮用于打开URDF文件
            self.open_urdf_btn = QPushButton("打开几何模型")
            self.open_urdf_btn.clicked.connect(self._open_urdf_file)
            self.open_urdf_btn.setToolTip("打开几何模型文件进行装配仿真")
            control_layout.addWidget(self.open_urdf_btn)

            # 添加"运动控制"按钮
            self.motion_control_btn = QPushButton("运动控制")
            self.motion_control_btn.clicked.connect(self._open_motion_control)
            self.motion_control_btn.setToolTip("打开关节运动控制面板")
            self.motion_control_btn.setEnabled(False)  # 初始状态禁用，需要先加载URDF
            control_layout.addWidget(self.motion_control_btn)

            control_layout.addStretch()  # 添加弹性空间
            area_layout.addWidget(control_widget)

            # 下方：显示区域
            display_widget = QWidget()
            display_widget.setMinimumHeight(120)
            display_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            display_widget.setStyleSheet(f"""
                QWidget {{
                    background-color: {bg_color};
                    border-radius: 5px;
                    border: 1px solid #ccc;
                }}
            """)

            # 显示区域的布局
            display_layout = QVBoxLayout(display_widget)
            display_layout.setContentsMargins(10, 10, 10, 10)

            # 占位符标签
            self.assembly_display_label = QLabel("装配仿真显示区域\n请点击'打开几何模型'按钮加载模型文件")
            self.assembly_display_label.setAlignment(Qt.AlignCenter)
            self.assembly_display_label.setStyleSheet("""
                QLabel {
                    color: #666;
                    font-size: 14px;
                    background-color: transparent;
                    border: none;
                }
            """)
            display_layout.addWidget(self.assembly_display_label)

            area_layout.addWidget(display_widget)
        elif index == 1:  # 光学测量数据 - 特殊处理
            # 创建光学测量数据的完整UI
            self._create_optical_measurement_ui(area_layout, bg_color)
        else:
            # 其他区域保持原有的占位符布局
            placeholder_label = QLabel(content)
            placeholder_label.setAlignment(Qt.AlignCenter)
            placeholder_label.setStyleSheet(f"""
                QLabel {{
                    color: #666;
                    font-size: 14px;
                    padding: 20px;
                    background-color: {bg_color};
                    border-radius: 5px;
                    border: 1px dashed #ccc;
                }}
            """)
            # 设置最小高度并允许拉伸
            placeholder_label.setMinimumHeight(100)
            placeholder_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
            area_layout.addWidget(placeholder_label)

            # 为后续功能预留的按钮区域
            button_layout = QHBoxLayout()

            if index == 2:  # 系统状态监控
                monitor_btn = QPushButton("开始监控")
                monitor_btn.setEnabled(False)
                button_layout.addWidget(monitor_btn)
            elif index == 3:  # 扩展功能区
                extend_btn = QPushButton("扩展功能")
                extend_btn.setEnabled(False)
                button_layout.addWidget(extend_btn)

            button_layout.addStretch()
            area_layout.addLayout(button_layout)

        return area_group

    def _create_optical_measurement_ui(self, parent_layout, bg_color):
        """创建光学测量数据的完整UI界面"""
        # 1. Zemax连接控制区
        zemax_control_group = QGroupBox("光学分析后端")
        zemax_control_layout = QHBoxLayout(zemax_control_group)
        zemax_control_layout.setContentsMargins(10, 10, 10, 10)
        zemax_control_layout.setSpacing(10)

        # 连接Zemax按钮
        self.connect_zemax_btn = QPushButton("连接Zemax")
        self.connect_zemax_btn.setMinimumWidth(100)
        self.connect_zemax_btn.clicked.connect(self._connect_zemax)
        zemax_control_layout.addWidget(self.connect_zemax_btn)

        # 打开文件按钮
        self.open_zemax_file_btn = QPushButton("打开文件")
        self.open_zemax_file_btn.setMinimumWidth(100)
        self.open_zemax_file_btn.setEnabled(False)
        self.open_zemax_file_btn.clicked.connect(self._open_zemax_file)
        zemax_control_layout.addWidget(self.open_zemax_file_btn)

        # {{ AURA: Add - 添加更新按钮，用于控制光学分析的执行时机 }}
        # 更新按钮（用于触发光学分析）
        self.update_analysis_btn = QPushButton("更新")
        self.update_analysis_btn.setMinimumWidth(80)
        self.update_analysis_btn.setEnabled(False)  # 初始状态为禁用
        self.update_analysis_btn.setToolTip("点击执行光学分析（点列图、波前图等）")
        self.update_analysis_btn.clicked.connect(self._update_optical_analysis)
        zemax_control_layout.addWidget(self.update_analysis_btn)

        # 文件路径显示
        file_path_label = QLabel("文件路径:")
        zemax_control_layout.addWidget(file_path_label)

        self.zemax_file_path_edit = QLineEdit()
        self.zemax_file_path_edit.setReadOnly(True)
        self.zemax_file_path_edit.setPlaceholderText("未选择文件")
        zemax_control_layout.addWidget(self.zemax_file_path_edit)

        # 浏览按钮
        browse_btn = QPushButton("浏览...")
        browse_btn.setMaximumWidth(80)
        browse_btn.clicked.connect(self._browse_zemax_file)
        zemax_control_layout.addWidget(browse_btn)

        # 状态显示
        zemax_control_layout.addWidget(QLabel("状态:"))
        self.zemax_status_label = QLabel("○未连接")
        self.zemax_status_label.setStyleSheet("color: #666;")
        zemax_control_layout.addWidget(self.zemax_status_label)

        parent_layout.addWidget(zemax_control_group)

        # 2. 光学分析选项卡
        analysis_tab_widget = QTabWidget()
        analysis_tab_widget.setMinimumHeight(400)

        # 创建各个分析标签页
        self._create_inspection_tab(analysis_tab_widget)
        self._create_spot_diagram_tab(analysis_tab_widget)
        self._create_wavefront_map_tab(analysis_tab_widget)
        self._create_mtf_tab(analysis_tab_widget)
        self._create_psf_tab(analysis_tab_widget)

        parent_layout.addWidget(analysis_tab_widget)

        # {{ AURA: Add - 在UI创建完成后同步Zemax连接状态 }}
        # 同步ZemaxConnector的当前状态到新创建的UI组件
        # 这解决了布局切换时UI状态不一致的问题
        self._sync_zemax_ui_state()

    def _create_inspection_tab(self, tab_widget):
        """创建Inspection标签页"""
        inspection_widget = QWidget()
        inspection_layout = QVBoxLayout(inspection_widget)

        # 占位符内容
        placeholder = QLabel("Inspection分析功能\n(待实现)")
        placeholder.setAlignment(Qt.AlignCenter)
        placeholder.setStyleSheet("color: #666; font-size: 14px;")
        inspection_layout.addWidget(placeholder)

        tab_widget.addTab(inspection_widget, "Inspection")

    def _create_spot_diagram_tab(self, tab_widget):
        """创建Spot Diagram标签页"""
        spot_widget = QWidget()
        spot_layout = QVBoxLayout(spot_widget)
        spot_layout.setContentsMargins(0, 0, 0, 0)

        # {{ AURA: Add - 创建QSplitter控件实现左右可拖拽调整 }}
        # 创建水平分割器
        splitter = QSplitter(Qt.Horizontal)
        splitter.setChildrenCollapsible(False)  # 防止面板被完全折叠

        # 左侧参数控制面板
        params_group = QGroupBox("参数控制")
        # {{ AURA: Modify - 移除固定宽度限制，让splitter控制宽度 }}
        params_layout = QVBoxLayout(params_group)
        params_layout.setSpacing(10)

        # Ray Density
        ray_density_layout = QHBoxLayout()
        ray_density_layout.addWidget(QLabel("Ray Density:"))
        self.ray_density_spinbox = QSpinBox()
        self.ray_density_spinbox.setRange(10, 120)
        self.ray_density_spinbox.setValue(30)
        self.ray_density_spinbox.valueChanged.connect(self._update_spot_analysis)
        ray_density_layout.addWidget(self.ray_density_spinbox)
        params_layout.addLayout(ray_density_layout)

        # ReferTo
        refer_to_layout = QHBoxLayout()
        refer_to_layout.addWidget(QLabel("ReferTo:"))
        self.refer_to_combo = QComboBox()
        self.refer_to_combo.addItems(['Chief Ray', 'Centroid', 'Middle', 'Vertex'])
        self.refer_to_combo.setCurrentIndex(1)  # 默认Centroid
        self.refer_to_combo.currentTextChanged.connect(self._update_spot_analysis)
        refer_to_layout.addWidget(self.refer_to_combo)
        params_layout.addLayout(refer_to_layout)

        # Wavelength
        wavelength_layout = QHBoxLayout()
        wavelength_layout.addWidget(QLabel("Wavelength:"))
        self.wavelength_combo = QComboBox()
        self.wavelength_combo.addItem('All')
        self.wavelength_combo.currentTextChanged.connect(self._update_spot_analysis)
        wavelength_layout.addWidget(self.wavelength_combo)
        params_layout.addLayout(wavelength_layout)

        # Field
        field_layout = QHBoxLayout()
        field_layout.addWidget(QLabel("Field:"))
        self.field_combo = QComboBox()
        self.field_combo.currentTextChanged.connect(self._update_spot_analysis)
        field_layout.addWidget(self.field_combo)
        params_layout.addLayout(field_layout)

        # 显示所有视场复选框
        self.show_all_fields_checkbox = QCheckBox("显示所有视场")
        self.show_all_fields_checkbox.stateChanged.connect(self._update_spot_analysis)
        params_layout.addWidget(self.show_all_fields_checkbox)

        # 按钮区域
        button_layout = QVBoxLayout()
        self.spot_update_analysis_btn = QPushButton("更新分析")
        self.spot_update_analysis_btn.clicked.connect(self._update_spot_analysis)
        self.export_data_btn = QPushButton("导出数据")
        self.export_data_btn.clicked.connect(self._export_spot_data)
        button_layout.addWidget(self.spot_update_analysis_btn)
        button_layout.addWidget(self.export_data_btn)
        params_layout.addLayout(button_layout)

        # {{ AURA: Modify - 将RMS/GEO数据表格移动到左侧参数控制面板下方 }}
        # 创建数据表格（移动到左侧）
        data_table_group = QGroupBox("RMS/GEO数据")
        data_table_layout = QVBoxLayout(data_table_group)

        self.spot_data_table = QTableWidget()
        self.spot_data_table.setMaximumHeight(150)
        self.spot_data_table.setAlternatingRowColors(True)
        self.spot_data_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.spot_data_table.setEditTriggers(QTableWidget.NoEditTriggers)
        data_table_layout.addWidget(self.spot_data_table)

        params_layout.addWidget(data_table_group)
        params_layout.addStretch()

        # {{ AURA: Add - 将左侧控制面板添加到splitter }}
        splitter.addWidget(params_group)

        # {{ AURA: Modify - 右侧只保留点列图显示组件 }}
        # 右侧显示区域
        display_group = QGroupBox("分析结果")
        display_layout = QVBoxLayout(display_group)

        # 创建点列图绘制组件
        self.spot_plot_widget = SpotPlotWidget()
        self.spot_plot_widget.setMinimumHeight(250)
        display_layout.addWidget(self.spot_plot_widget)

        # {{ AURA: Add - 将右侧显示面板添加到splitter }}
        splitter.addWidget(display_group)

        # {{ AURA: Add - 设置splitter初始比例为50%:50% }}
        splitter.setSizes([1, 1])  # 设置初始比例1:1
        splitter.setStretchFactor(0, 0)  # 左侧面板不拉伸
        splitter.setStretchFactor(1, 1)  # 右侧面板可拉伸

        # 将splitter添加到主布局
        spot_layout.addWidget(splitter)
        tab_widget.addTab(spot_widget, "Spot Diagram")



    def _create_wavefront_map_tab(self, tab_widget):
        """创建Wavefront Map标签页"""
        wavefront_widget = QWidget()

        # {{ AURA: Add - 使用QSplitter替换QHBoxLayout，实现可拖拽的分隔条 }}
        # 创建水平分割器
        wavefront_splitter = QSplitter(Qt.Horizontal)
        wavefront_splitter.setChildrenCollapsible(False)  # 防止子组件被完全折叠

        # 创建主布局
        main_layout = QVBoxLayout(wavefront_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.addWidget(wavefront_splitter)

        # 左侧参数控制面板
        params_group = QGroupBox("参数控制")
        params_group.setMinimumWidth(200)
        params_group.setMaximumWidth(350)
        params_layout = QVBoxLayout(params_group)

        # Sampling
        sampling_layout = QHBoxLayout()
        sampling_layout.addWidget(QLabel("Sampling:"))
        self.wavefront_sampling_combo = QComboBox()
        self.wavefront_sampling_combo.addItems(['32x32', '64x64', '128x128', '256x256', '512x512'])
        self.wavefront_sampling_combo.setCurrentIndex(2)  # 默认128x128
        sampling_layout.addWidget(self.wavefront_sampling_combo)
        params_layout.addLayout(sampling_layout)

        # Rotation
        rotation_layout = QHBoxLayout()
        rotation_layout.addWidget(QLabel("Rotation:"))
        self.wavefront_rotation_combo = QComboBox()
        self.wavefront_rotation_combo.addItems(['0', '90', '180', '270'])
        rotation_layout.addWidget(self.wavefront_rotation_combo)
        params_layout.addLayout(rotation_layout)

        # Wavelength
        wf_wavelength_layout = QHBoxLayout()
        wf_wavelength_layout.addWidget(QLabel("Wavelength:"))
        self.wavefront_wavelength_combo = QComboBox()
        # {{ AURA: Add - 添加默认波长选项，避免初始化时为空 }}
        self.wavefront_wavelength_combo.addItems(['1', '2', '3'])  # 默认波长选项
        wf_wavelength_layout.addWidget(self.wavefront_wavelength_combo)
        params_layout.addLayout(wf_wavelength_layout)

        # Field
        wf_field_layout = QHBoxLayout()
        wf_field_layout.addWidget(QLabel("Field:"))
        self.wavefront_field_combo = QComboBox()
        # {{ AURA: Add - 添加默认视场选项，避免初始化时为空 }}
        self.wavefront_field_combo.addItems(['1', '2', '3', '4', '5'])  # 默认视场选项
        wf_field_layout.addWidget(self.wavefront_field_combo)
        params_layout.addLayout(wf_field_layout)

        # {{ AURA: Add - 添加PV和RMS显示区域，参考ref/zpy_wavefront.py }}
        # PV显示
        pv_layout = QHBoxLayout()
        pv_layout.addWidget(QLabel("PV:"))
        self.wavefront_pv_label = QLabel("--")
        self.wavefront_pv_label.setStyleSheet("""
            QLabel {
                background-color: white;
                border: 2px solid #d0d0d0;
                border-radius: 4px;
                padding: 5px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                min-width: 100px;
            }
        """)
        pv_layout.addWidget(self.wavefront_pv_label)
        params_layout.addLayout(pv_layout)

        # RMS显示
        rms_layout = QHBoxLayout()
        rms_layout.addWidget(QLabel("RMS:"))
        self.wavefront_rms_label = QLabel("--")
        self.wavefront_rms_label.setStyleSheet("""
            QLabel {
                background-color: white;
                border: 2px solid #d0d0d0;
                border-radius: 4px;
                padding: 5px;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                min-width: 100px;
            }
        """)
        rms_layout.addWidget(self.wavefront_rms_label)
        params_layout.addLayout(rms_layout)

        params_layout.addStretch()

        # {{ AURA: Add - 将参数控制面板添加到分割器 }}
        wavefront_splitter.addWidget(params_group)

        # {{ AURA: Modify - 右侧图表显示区域，使用WavefrontPlotWidget替换占位符 }}
        # 右侧图表显示区域
        chart_group = QGroupBox("波前图显示")
        chart_layout = QVBoxLayout(chart_group)

        # {{ AURA: Add - 创建波前图绘制组件 }}
        self.wavefront_plot_widget = WavefrontPlotWidget()
        self.wavefront_plot_widget.setMinimumHeight(300)
        chart_layout.addWidget(self.wavefront_plot_widget)

        # {{ AURA: Add - 将图表显示区域添加到分割器 }}
        wavefront_splitter.addWidget(chart_group)

        # {{ AURA: Add - 设置分割器的初始比例（左侧30%，右侧70%） }}
        wavefront_splitter.setSizes([250, 600])

        # {{ AURA: Add - 连接参数控制信号到更新方法 }}
        self._setup_wavefront_signals()

        tab_widget.addTab(wavefront_widget, "Wavefront Map")

    def _create_mtf_tab(self, tab_widget):
        """创建MTF标签页"""
        mtf_widget = QWidget()
        mtf_layout = QVBoxLayout(mtf_widget)
        mtf_layout.setContentsMargins(0, 0, 0, 0)

        # {{ AURA: Add - 导入MTF绘图控件 }}
        try:
            from ..optical_analysis.mtf_plot_widget import MtfPlotWidget

            # 创建MTF绘图控件
            self.mtf_plot_widget = MtfPlotWidget()

            # 连接分析请求信号
            self.mtf_plot_widget.analysis_requested.connect(self._on_mtf_analysis_requested)

            mtf_layout.addWidget(self.mtf_plot_widget)

        except ImportError as e:
            # 如果导入失败，显示错误信息
            error_label = QLabel(f"MTF分析功能加载失败\n错误: {str(e)}")
            error_label.setAlignment(Qt.AlignCenter)
            error_label.setStyleSheet("color: #ff6b6b; font-size: 14px;")
            mtf_layout.addWidget(error_label)

        tab_widget.addTab(mtf_widget, "MTF")

    def _on_mtf_analysis_requested(self, params):
        """处理MTF分析请求"""
        try:
            # 检查Zemax连接状态
            if not self.zemax_connector.is_connected():
                QMessageBox.warning(self, "连接错误", "请先连接到Zemax OpticStudio")
                return

            # 验证参数有效性
            if hasattr(self, 'mtf_plot_widget'):
                is_valid, error_msg = self.mtf_plot_widget.validate_parameters()
                if not is_valid:
                    QMessageBox.warning(self, "参数错误", f"参数验证失败:\n{error_msg}")
                    return

            # 禁用MTF控件，防止重复分析
            if hasattr(self, 'mtf_plot_widget'):
                self.mtf_plot_widget.set_enabled(False)

            # 创建MTF分析器
            from ..optical_analysis.mtf_analyzer import MtfAnalyzer

            # 获取Zemax应用程序对象
            zemax_app = self.zemax_connector.get_zemax_app()
            if zemax_app is None:
                QMessageBox.warning(self, "连接错误", "无法获取Zemax应用程序对象，请重新连接")
                return

            mtf_analyzer = MtfAnalyzer(zemax_app)

            # 创建MTF分析
            mtf_analyzer.create_fftmtf_analysis(
                field_number=params.get('field_number', 0),
                wavelength_number=params.get('wavelength_number', 0),
                surface_number=-1,  # 像面
                mtf_type=params.get('mtf_type', 'Modulation'),
                sample_size=params.get('sample_size', 64),
                show_diffraction_limit=params.get('show_diffraction_limit', True),
                use_dashes=params.get('use_dashes', False),
                use_polarization=params.get('use_polarization', False),
                maximum_frequency=params.get('maximum_frequency', 60.0)
            )

            # 运行分析
            mtf_analyzer.run_analysis()

            # 获取结果
            mtf_analyzer.get_results()

            # 提取MTF数据
            mtf_data = mtf_analyzer.extract_mtf_data()

            # 绘制MTF数据
            if hasattr(self, 'mtf_plot_widget') and mtf_data:
                # 绘制第一个系列的数据
                if len(mtf_data["数据系列"]) > 0:
                    self.mtf_plot_widget.plot_mtf_data(mtf_data, 0)

                # 如果有多个系列，也绘制比较图
                if len(mtf_data["数据系列"]) > 1:
                    self.mtf_plot_widget.plot_mtf_comparison(mtf_data, "切向")

            # 关闭分析器
            mtf_analyzer.close()

            # 更新状态栏
            self.status_bar.showMessage(f"MTF分析完成 - 共{len(mtf_data['数据系列'])}个数据系列", 3000)

        except Exception as e:
            logger.error(f"MTF分析失败: {str(e)}")
            QMessageBox.critical(self, "分析错误", f"MTF分析失败:\n{str(e)}")
        finally:
            # 重新启用MTF控件
            if hasattr(self, 'mtf_plot_widget'):
                self.mtf_plot_widget.set_enabled(True)

    def _create_psf_tab(self, tab_widget):
        """创建PSF标签页"""
        psf_widget = QWidget()
        psf_layout = QVBoxLayout(psf_widget)

        placeholder = QLabel("PSF分析功能\n(待实现)")
        placeholder.setAlignment(Qt.AlignCenter)
        placeholder.setStyleSheet("color: #666; font-size: 14px;")
        psf_layout.addWidget(placeholder)

        tab_widget.addTab(psf_widget, "PSF")

    def _create_top_toolbar_old(self, parent_layout):
        """创建顶部工具栏：TCP服务器控制（水平排列）"""
        toolbar_widget = QWidget()
        toolbar_layout = QHBoxLayout(toolbar_widget)
        toolbar_layout.setContentsMargins(0, 0, 0, 0)
        toolbar_layout.setSpacing(15)

        # 本机IP选择
        ip_label = QLabel("本机IP:")
        ip_label.setProperty("labelType", "data_label")
        toolbar_layout.addWidget(ip_label)

        self.local_ip_combo = QComboBox()
        self.local_ip_combo.setMinimumWidth(140)
        self.local_ip_combo.setToolTip("选择本机IP地址")
        self._populate_ip_addresses()
        self.local_ip_combo.currentTextChanged.connect(self._on_ip_selection_changed)
        toolbar_layout.addWidget(self.local_ip_combo)

        # 刷新IP按钮
        self.refresh_ip_btn = QPushButton("🔄")
        self.refresh_ip_btn.setToolTip("刷新IP地址列表")
        self.refresh_ip_btn.setMaximumWidth(30)
        self.refresh_ip_btn.clicked.connect(self._populate_ip_addresses)
        toolbar_layout.addWidget(self.refresh_ip_btn)

        # 监听端口
        port_label = QLabel("监听端口:")
        port_label.setProperty("labelType", "data_label")
        toolbar_layout.addWidget(port_label)

        self.port_spinbox = QSpinBox()
        self.port_spinbox.setRange(1, 65535)
        self.port_spinbox.setValue(5050)
        self.port_spinbox.setMinimumWidth(80)
        toolbar_layout.addWidget(self.port_spinbox)

        # 服务器控制按钮
        self.start_server_btn = QPushButton("启动服务器")
        self.start_server_btn.setProperty("buttonType", "success")
        self.start_server_btn.clicked.connect(self._start_tcp_server)
        toolbar_layout.addWidget(self.start_server_btn)

        self.stop_server_btn = QPushButton("停止服务器")
        self.stop_server_btn.setProperty("buttonType", "danger")
        self.stop_server_btn.clicked.connect(self._stop_tcp_server)
        self.stop_server_btn.setEnabled(False)
        toolbar_layout.addWidget(self.stop_server_btn)

        toolbar_layout.addStretch()  # 添加弹性空间
        parent_layout.addWidget(toolbar_widget)

    def _create_middle_section(self, parent_layout):
        """创建中间区域：左侧设备控制+位姿，右侧ZFR数据"""
        middle_widget = QWidget()
        middle_widget.setMinimumHeight(400)  # 设置中间区域最小高度，确保内容可见
        middle_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)  # 优先保持合理高度
        middle_layout = QHBoxLayout(middle_widget)
        middle_layout.setContentsMargins(0, 0, 0, 0)
        middle_layout.setSpacing(15)

        # 左侧：设备控制与位姿显示（合并区域）
        left_widget = QWidget()
        left_widget.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Expanding)
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        self._create_device_and_pose_panel(left_layout)
        middle_layout.addWidget(left_widget)

        # 右侧：偏心倾斜数据显示
        right_widget = QWidget()
        right_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        self._create_zfr_data_panel(right_layout)
        middle_layout.addWidget(right_widget)

        # 设置左右两侧的拉伸比例：左侧35%，右侧65%
        middle_layout.setStretch(0, 35)  # 左侧权重35
        middle_layout.setStretch(1, 65)  # 右侧权重65

        parent_layout.addWidget(middle_widget)

    def _create_device_and_pose_panel(self, parent_layout):
        """创建设备控制与位姿显示面板（合并）"""
        device_pose_group = QGroupBox("设备控制与位姿显示")
        device_pose_group.setMinimumHeight(300)  # 设置合理的最小高度
        device_pose_group.setMaximumHeight(450)  # 设置最大高度，避免过度拉伸
        device_pose_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)  # 优先保持合理高度
        device_pose_layout = QVBoxLayout(device_pose_group)

        # 上半部分：当前控制设备
        device_layout = QHBoxLayout()
        device_label = QLabel("当前控制设备:")
        device_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        device_layout.addWidget(device_label)

        from src.device_communication.tcp_motion_controller import DeviceType
        for device_type in DeviceType:
            device_container = QWidget()
            device_container_layout = QVBoxLayout(device_container)
            device_container_layout.setContentsMargins(5, 5, 5, 5)
            device_container_layout.setSpacing(2)

            radio = QRadioButton(f"{device_type.display_name}")
            radio.setStyleSheet("""
                QRadioButton {
                    font-size: 14px;
                    font-weight: bold;
                    padding: 8px;
                    min-height: 25px;
                    spacing: 5px;
                }
                QRadioButton::indicator {
                    width: 18px;
                    height: 18px;
                }
            """)

            ip_label = QLabel(device_type.ip_address)
            ip_label.setStyleSheet("color: #666; font-size: 12px; min-height: 20px; padding: 2px;")
            ip_label.setAlignment(Qt.AlignCenter)

            device_container_layout.addWidget(radio)
            device_container_layout.addWidget(ip_label)
            device_layout.addWidget(device_container)

            self.device_buttons[device_type] = radio
            self.button_group.addButton(radio)
            radio.toggled.connect(lambda checked, dt=device_type: self._on_device_selected(dt, checked))

        # 默认选择次镜
        self.device_buttons[DeviceType.SUBMIRROR].setChecked(True)
        device_pose_layout.addLayout(device_layout)

        # 下半部分：当前设备位姿
        pose_layout = QVBoxLayout()
        pose_title = QLabel("当前设备位姿:")
        pose_title.setStyleSheet("font-size: 14px; font-weight: bold; margin-top: 10px;")
        pose_title.setMinimumHeight(30)  # 设置标题最小高度
        pose_layout.addWidget(pose_title)

        # 位姿数据显示容器
        pose_data_widget = QWidget()
        pose_data_widget.setMinimumHeight(120)  # 设置合理的最小高度
        pose_data_widget.setMaximumHeight(180)  # 设置最大高度，避免过度拉伸
        pose_data_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)  # 优先保持合理高度
        pose_data_layout = QGridLayout(pose_data_widget)
        pose_data_layout.setSpacing(6)  # 设置控件间距
        pose_data_layout.setContentsMargins(8, 8, 8, 8)  # 设置边距

        # 设置行的最小高度，确保两行都能显示
        pose_data_layout.setRowMinimumHeight(0, 40)  # 第一行（位移）最小高度
        pose_data_layout.setRowMinimumHeight(1, 40)  # 第二行（角度）最小高度

        # 位移显示
        x_label = QLabel("X(mm):")
        x_label.setStyleSheet("font-size: 14px; font-weight: bold; min-height: 35px; padding: 5px;")
        pose_data_layout.addWidget(x_label, 0, 0)
        self.pos_x_label = QLabel("--")
        self.pos_x_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2196F3; min-height: 35px; padding: 5px; background-color: rgba(33, 150, 243, 0.1); border-radius: 3px;")
        pose_data_layout.addWidget(self.pos_x_label, 0, 1)

        y_label = QLabel("Y(mm):")
        y_label.setStyleSheet("font-size: 14px; font-weight: bold; min-height: 35px; padding: 5px;")
        pose_data_layout.addWidget(y_label, 0, 2)
        self.pos_y_label = QLabel("--")
        self.pos_y_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2196F3; min-height: 35px; padding: 5px; background-color: rgba(33, 150, 243, 0.1); border-radius: 3px;")
        pose_data_layout.addWidget(self.pos_y_label, 0, 3)

        z_label = QLabel("Z(mm):")
        z_label.setStyleSheet("font-size: 14px; font-weight: bold; min-height: 35px; padding: 5px;")
        pose_data_layout.addWidget(z_label, 0, 4)
        self.pos_z_label = QLabel("--")
        self.pos_z_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #2196F3; min-height: 35px; padding: 5px; background-color: rgba(33, 150, 243, 0.1); border-radius: 3px;")
        pose_data_layout.addWidget(self.pos_z_label, 0, 5)

        # 角度显示
        a_label = QLabel("A(°):")
        a_label.setStyleSheet("font-size: 14px; font-weight: bold; min-height: 35px; padding: 5px;")
        pose_data_layout.addWidget(a_label, 1, 0)
        self.rot_a_label = QLabel("--")
        self.rot_a_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #FF9800; min-height: 35px; padding: 5px; background-color: rgba(255, 152, 0, 0.1); border-radius: 3px;")
        pose_data_layout.addWidget(self.rot_a_label, 1, 1)

        b_label = QLabel("B(°):")
        b_label.setStyleSheet("font-size: 14px; font-weight: bold; min-height: 35px; padding: 5px;")
        pose_data_layout.addWidget(b_label, 1, 2)
        self.rot_b_label = QLabel("--")
        self.rot_b_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #FF9800; min-height: 35px; padding: 5px; background-color: rgba(255, 152, 0, 0.1); border-radius: 3px;")
        pose_data_layout.addWidget(self.rot_b_label, 1, 3)

        c_label = QLabel("C(°):")
        c_label.setStyleSheet("font-size: 14px; font-weight: bold; min-height: 35px; padding: 5px;")
        pose_data_layout.addWidget(c_label, 1, 4)
        self.rot_c_label = QLabel("--")
        self.rot_c_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #FF9800; min-height: 35px; padding: 5px; background-color: rgba(255, 152, 0, 0.1); border-radius: 3px;")
        pose_data_layout.addWidget(self.rot_c_label, 1, 5)

        pose_layout.addWidget(pose_data_widget)

        # 刷新位姿按钮容器
        refresh_widget = QWidget()
        refresh_widget.setMinimumHeight(60)  # 设置按钮区域最小高度
        refresh_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.MinimumExpanding)
        refresh_layout = QHBoxLayout(refresh_widget)
        refresh_layout.setContentsMargins(10, 10, 10, 10)

        self.refresh_pose_btn = QPushButton("刷新位姿")
        self.refresh_pose_btn.setMinimumHeight(40)  # 设置按钮最小高度
        self.refresh_pose_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
                min-height: 40px;
            }
            QPushButton:hover { background-color: #1976D2; }
            QPushButton:disabled { background-color: #cccccc; color: #666666; }
        """)
        self.refresh_pose_btn.clicked.connect(self._refresh_pose)
        self.refresh_pose_btn.setEnabled(False)
        refresh_layout.addWidget(self.refresh_pose_btn)
        refresh_layout.addStretch()

        pose_layout.addWidget(refresh_widget)
        device_pose_layout.addLayout(pose_layout)

        parent_layout.addWidget(device_pose_group)

    def _create_zfr_data_panel(self, parent_layout):
        """创建数据分析与文件传输面板（直接布局）"""
        # 创建主容器组
        main_group = QGroupBox("Zernike采集与分析")
        main_group.setMinimumHeight(380)  # 增加最小高度，确保所有内容可见
        main_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)  # 优先保持合理高度
        main_layout = QVBoxLayout(main_group)
        main_layout.setSpacing(6)  # 进一步减少内部间距
        main_layout.setContentsMargins(8, 12, 8, 8)  # 优化边距

        # 直接创建文件传输内容，不使用TabWidget
        self._create_file_transfer_content(main_layout)

        parent_layout.addWidget(main_group)



    def _create_file_transfer_content(self, parent_layout):
        """创建文件传输内容（直接添加到父布局）"""
        # 文件存储配置区域（调整到第一位）
        self._create_storage_config(parent_layout)

        # 连接配置区域
        self._create_connection_config(parent_layout)

        # ZFR数据显示区域 - 最重要的区域，确保始终可见
        self._create_transfer_zfr_display(parent_layout)

        # 减少弹性空间，确保ZFR数据分析区域优先显示
        parent_layout.addStretch(0)

    def _create_connection_config(self, parent_layout):
        """创建连接配置区域"""
        conn_group = QGroupBox("Zernike连接配置")
        conn_group.setMinimumHeight(100)  # 设置紧凑的最小高度
        conn_group.setMaximumHeight(140)  # 设置最大高度
        conn_layout = QVBoxLayout(conn_group)
        conn_layout.setSpacing(6)  # 减少间距
        conn_layout.setContentsMargins(8, 10, 8, 8)  # 优化边距

        # IP和端口配置
        config_layout = QGridLayout()

        # 服务器IP（改为下拉框）
        config_layout.addWidget(QLabel("服务器IP:"), 0, 0)
        self.server_ip_combo = QComboBox()
        self.server_ip_combo.setEditable(True)
        self.server_ip_combo.addItems(self._get_local_ips())
        # 设置保存的IP值
        if hasattr(self, 'client_config'):
            self.server_ip_combo.setCurrentText(self.client_config.get("server_ip", "127.0.0.1"))
        config_layout.addWidget(self.server_ip_combo, 0, 1)

        # 服务器端口
        config_layout.addWidget(QLabel("端口:"), 0, 2)
        self.server_port_edit = QLineEdit()
        # 设置保存的端口值
        if hasattr(self, 'client_config'):
            self.server_port_edit.setText(self.client_config.get("server_port", "8888"))
        else:
            self.server_port_edit.setText("8888")
        self.server_port_edit.setMaximumWidth(80)
        config_layout.addWidget(self.server_port_edit, 0, 3)

        # 连接按钮
        self.connect_btn = QPushButton("连接服务器")
        self.connect_btn.clicked.connect(self._connect_to_server)
        config_layout.addWidget(self.connect_btn, 0, 4)

        self.disconnect_btn = QPushButton("断开连接")
        self.disconnect_btn.clicked.connect(self._disconnect_from_server)
        self.disconnect_btn.setEnabled(False)
        config_layout.addWidget(self.disconnect_btn, 0, 5)

        # 接收文件按钮（移到断开连接按钮右侧）
        self.receive_btn = QPushButton("接收文件")
        self.receive_btn.clicked.connect(self._receive_file)
        self.receive_btn.setEnabled(False)
        config_layout.addWidget(self.receive_btn, 0, 6)

        config_layout.setColumnStretch(7, 1)  # 添加弹性空间

        conn_layout.addLayout(config_layout)
        parent_layout.addWidget(conn_group)

    def _create_transfer_zfr_display(self, parent_layout):
        """创建文件传输ZFR数据显示区域"""
        zfr_group = QGroupBox("Zernike数据分析")
        zfr_group.setMinimumHeight(140)  # 增加最小高度，确保1行9列布局可见
        zfr_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Minimum)  # 确保最小高度优先级
        zfr_layout = QVBoxLayout(zfr_group)
        zfr_layout.setSpacing(4)  # 进一步减少间距
        zfr_layout.setContentsMargins(6, 8, 6, 6)  # 进一步优化边距

        # PV和RMS显示
        summary_layout = QGridLayout()

        pv_label = QLabel("PV:")
        pv_label.setProperty("labelType", "data_label")
        summary_layout.addWidget(pv_label, 0, 0)
        self.transfer_pv_edit = QLineEdit("0.000000")
        self.transfer_pv_edit.setReadOnly(True)
        summary_layout.addWidget(self.transfer_pv_edit, 0, 1)

        rms_label = QLabel("RMS:")
        rms_label.setProperty("labelType", "data_label")
        summary_layout.addWidget(rms_label, 0, 2)
        self.transfer_rms_edit = QLineEdit("0.000000")
        self.transfer_rms_edit.setReadOnly(True)
        summary_layout.addWidget(self.transfer_rms_edit, 0, 3)

        zfr_layout.addLayout(summary_layout)

        # 详细数据 - 水平排列，1行9列布局
        data_layout = QGridLayout()

        # 1行9列布局：Power [显示区域] Power控制按钮 Coma_x [显示区域] Coma_x控制按钮 Coma_y [显示区域] Coma_y控制按钮

        # Power (列0-2)
        power_label = QLabel("Power:")
        power_label.setProperty("labelType", "data_label")
        data_layout.addWidget(power_label, 0, 0)
        self.transfer_power_edit = QLineEdit("0.000000")
        self.transfer_power_edit.setReadOnly(True)
        data_layout.addWidget(self.transfer_power_edit, 0, 1)
        self.transfer_power_control_btn = QPushButton("Power控制")
        self.transfer_power_control_btn.setProperty("buttonType", "primary")
        self.transfer_power_control_btn.clicked.connect(self._transfer_power_motion_control)
        self.transfer_power_control_btn.setEnabled(False)
        data_layout.addWidget(self.transfer_power_control_btn, 0, 2)

        # Coma_X (列3-5)
        coma_x_label = QLabel("Coma_X:")
        coma_x_label.setProperty("labelType", "data_label")
        data_layout.addWidget(coma_x_label, 0, 3)
        self.transfer_coma_x_edit = QLineEdit("0.000000")
        self.transfer_coma_x_edit.setReadOnly(True)
        data_layout.addWidget(self.transfer_coma_x_edit, 0, 4)
        self.transfer_coma_x_control_btn = QPushButton("Coma_X控制")
        self.transfer_coma_x_control_btn.setProperty("buttonType", "primary")
        self.transfer_coma_x_control_btn.clicked.connect(self._transfer_coma_x_motion_control)
        self.transfer_coma_x_control_btn.setEnabled(False)
        data_layout.addWidget(self.transfer_coma_x_control_btn, 0, 5)

        # Coma_Y (列6-8)
        coma_y_label = QLabel("Coma_Y:")
        coma_y_label.setProperty("labelType", "data_label")
        data_layout.addWidget(coma_y_label, 0, 6)
        self.transfer_coma_y_edit = QLineEdit("0.000000")
        self.transfer_coma_y_edit.setReadOnly(True)
        data_layout.addWidget(self.transfer_coma_y_edit, 0, 7)
        self.transfer_coma_y_control_btn = QPushButton("Coma_Y控制")
        self.transfer_coma_y_control_btn.setProperty("buttonType", "primary")
        self.transfer_coma_y_control_btn.clicked.connect(self._transfer_coma_y_motion_control)
        self.transfer_coma_y_control_btn.setEnabled(False)
        data_layout.addWidget(self.transfer_coma_y_control_btn, 0, 8)

        zfr_layout.addLayout(data_layout)
        parent_layout.addWidget(zfr_group)

    def _create_storage_config(self, parent_layout):
        """创建文件存储配置区域"""
        storage_group = QGroupBox("Zernike存储配置")
        storage_group.setMinimumHeight(80)  # 设置紧凑的最小高度
        storage_group.setMaximumHeight(120)  # 设置最大高度
        storage_layout = QVBoxLayout(storage_group)
        storage_layout.setSpacing(6)  # 减少间距
        storage_layout.setContentsMargins(8, 10, 8, 8)  # 优化边距

        # 存储路径配置
        path_layout = QHBoxLayout()
        path_layout.addWidget(QLabel("存储路径:"))

        self.storage_path_edit = QLineEdit()
        self.storage_path_edit.setReadOnly(True)
        self.storage_path_edit.setPlaceholderText("请选择文件存储目录...")
        # 设置保存的存储路径
        if hasattr(self, 'client_config'):
            saved_path = self.client_config.get("storage_path", "")
            if saved_path:
                self.storage_path_edit.setText(saved_path)
                self.storage_path = saved_path
        path_layout.addWidget(self.storage_path_edit)

        self.browse_storage_btn = QPushButton("浏览...")
        self.browse_storage_btn.clicked.connect(self._browse_storage_path)
        path_layout.addWidget(self.browse_storage_btn)

        storage_layout.addLayout(path_layout)
        parent_layout.addWidget(storage_group)





    def _create_motion_control_section(self, parent_layout):
        """创建运动参数与自动控制区域（合并）"""
        motion_group = QGroupBox("运动参数与自动控制")
        motion_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)  # 优先保持合理高度
        motion_layout = QVBoxLayout(motion_group)

        # 上部分：运动参数选择
        params_layout = QHBoxLayout()

        # 运动等级
        level_label = QLabel("运动等级:")
        level_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        params_layout.addWidget(level_label)

        self.level_combo = QComboBox()
        self.level_combo.addItems([
            "精细 (0.0006)",
            "标准 (0.001)",
            "粗调 (0.01)"
        ])
        self.level_combo.setCurrentIndex(1)  # 默认选择标准
        self.level_combo.setStyleSheet("font-size: 13px;")
        self.level_combo.currentIndexChanged.connect(self._on_level_changed)
        params_layout.addWidget(self.level_combo)

        params_layout.addSpacing(30)  # 添加间距

        # 平移旋转比例因子
        ratio_label = QLabel("平移旋转比例因子:")
        ratio_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        params_layout.addWidget(ratio_label)

        self.ratio_spinbox = QSpinBox()
        self.ratio_spinbox.setRange(1, 10)
        self.ratio_spinbox.setValue(5)  # 默认5
        self.ratio_spinbox.setStyleSheet("font-size: 13px;")
        self.ratio_spinbox.setToolTip("平移与旋转的比例因子，范围1-10的整数")
        self.ratio_spinbox.valueChanged.connect(self._on_ratio_changed)
        params_layout.addWidget(self.ratio_spinbox)

        params_layout.addStretch()  # 添加弹性空间
        motion_layout.addLayout(params_layout)

        # 中部分：6轴运动输入
        axes_layout = QGridLayout()

        # 位移轴 (X, Y, Z)
        translation_axes = [('X', 'mm'), ('Y', 'mm'), ('Z', 'mm')]
        rotation_axes = [('A', '°'), ('B', '°'), ('C', '°')]

        # 位移轴
        for i, (axis, unit) in enumerate(translation_axes):
            axis_label = QLabel(f"{axis}轴:")
            axis_label.setStyleSheet("font-size: 14px; font-weight: bold;")
            axes_layout.addWidget(axis_label, 0, i*2)

            pos_input = QDoubleSpinBox()
            pos_input.setRange(-100.0, 100.0)
            pos_input.setValue(0.0)
            pos_input.setSingleStep(0.001)
            pos_input.setDecimals(6)
            pos_input.setSuffix(f" {unit}")
            pos_input.setStyleSheet("font-size: 13px;")
            pos_input.setReadOnly(False)  # 可编辑
            axes_layout.addWidget(pos_input, 0, i*2+1)

            self.motion_inputs[axis.lower()] = pos_input

        # 旋转轴
        for i, (axis, unit) in enumerate(rotation_axes):
            axis_label = QLabel(f"{axis}轴:")
            axis_label.setStyleSheet("font-size: 14px; font-weight: bold;")
            axes_layout.addWidget(axis_label, 1, i*2)

            rot_input = QDoubleSpinBox()
            rot_input.setRange(-360.0, 360.0)
            rot_input.setValue(0.0)
            rot_input.setSingleStep(0.001)
            rot_input.setDecimals(6)
            rot_input.setSuffix(f" {unit}")
            rot_input.setStyleSheet("font-size: 13px;")
            rot_input.setReadOnly(False)  # 可编辑
            axes_layout.addWidget(rot_input, 1, i*2+1)

            self.motion_inputs[axis.lower()] = rot_input

        motion_layout.addLayout(axes_layout)

        # 下部分：控制按钮
        button_layout = QHBoxLayout()

        self.execute_btn = QPushButton("执行运动")
        self.execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover { background-color: #1976D2; }
            QPushButton:disabled { background-color: #cccccc; color: #666666; }
        """)
        self.execute_btn.clicked.connect(self._execute_motion)
        self.execute_btn.setEnabled(False)

        self.stop_btn = QPushButton("紧急停止")
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover { background-color: #da190b; }
        """)
        self.stop_btn.clicked.connect(self._emergency_stop)

        button_layout.addWidget(self.execute_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addStretch()

        motion_layout.addLayout(button_layout)
        parent_layout.addWidget(motion_group)

    def _create_system_log_section_widget(self, parent_widget):
        """创建系统日志区域作为独立widget"""
        layout = QVBoxLayout(parent_widget)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)

        log_group = QGroupBox("系统日志")
        log_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)  # 允许扩展
        log_layout = QVBoxLayout(log_group)

        # 日志控制栏
        control_layout = QHBoxLayout()

        # 日志级别选择
        level_label = QLabel("日志级别:")
        control_layout.addWidget(level_label)

        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["全部", "调试", "信息", "警告", "错误", "严重"])
        self.log_level_combo.setCurrentText("信息")  # 默认显示信息级别
        self.log_level_combo.currentTextChanged.connect(self._on_log_level_changed)
        control_layout.addWidget(self.log_level_combo)

        # 清空日志按钮
        clear_btn = QPushButton("清空日志")
        clear_btn.clicked.connect(self._clear_logs)
        control_layout.addWidget(clear_btn)

        control_layout.addStretch()  # 添加弹性空间
        log_layout.addLayout(control_layout)

        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setMinimumHeight(100)  # 减小最小高度，允许更好的折叠
        self.log_text.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)  # 允许充分扩展
        log_layout.addWidget(self.log_text)

        # 初始化日志存储
        self.log_messages = []  # 存储所有日志消息
        self.current_log_level = "信息"  # 当前显示的日志级别

        # 初始化位姿缓存机制
        self.last_pose_data = None  # 上次的位姿数据
        self.pose_change_threshold = 0.0001  # 位姿变化阈值（0.1mm或0.1°）
        self.pose_update_enabled = True  # 位姿更新开关

        layout.addWidget(log_group)

    def _create_system_log_section(self, parent_layout):
        """创建系统日志区域"""
        log_group = QGroupBox("系统日志")
        log_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)  # 允许扩展
        log_layout = QVBoxLayout(log_group)

        # 日志控制栏
        control_layout = QHBoxLayout()

        # 日志级别选择
        level_label = QLabel("日志级别:")
        control_layout.addWidget(level_label)

        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["全部", "调试", "信息", "警告", "错误", "严重"])
        self.log_level_combo.setCurrentText("信息")  # 默认显示信息级别
        self.log_level_combo.currentTextChanged.connect(self._on_log_level_changed)
        control_layout.addWidget(self.log_level_combo)

        # 清空日志按钮
        clear_btn = QPushButton("清空日志")
        clear_btn.clicked.connect(self._clear_logs)
        control_layout.addWidget(clear_btn)

        control_layout.addStretch()  # 添加弹性空间
        log_layout.addLayout(control_layout)

        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setMinimumHeight(200)  # 减少最小高度
        self.log_text.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)  # 允许充分扩展
        log_layout.addWidget(self.log_text)

        # 初始化日志存储
        self.log_messages = []  # 存储所有日志消息
        self.current_log_level = "信息"  # 当前显示的日志级别

        # 初始化位姿缓存机制
        self.last_pose_data = None  # 上次的位姿数据
        self.pose_change_threshold = 0.0001  # 位姿变化阈值（0.1mm或0.1°）
        self.pose_update_enabled = True  # 位姿更新开关

        parent_layout.addWidget(log_group)




    def _populate_ip_addresses(self):
        """填充IP地址下拉列表"""
        try:
            # 获取所有有效的IP地址
            ip_addresses = get_all_local_ips()

            # 清空现有项目
            self.local_ip_combo.clear()

            # 添加所有IP地址
            for ip in ip_addresses:
                self.local_ip_combo.addItem(ip)

            # 设置当前选中的IP（如果tcp_controller已有host）
            current_host = self.tcp_controller.host
            if current_host in ip_addresses:
                index = ip_addresses.index(current_host)
                self.local_ip_combo.setCurrentIndex(index)
            elif ip_addresses:
                # 如果当前host不在列表中，选择第一个
                self.local_ip_combo.setCurrentIndex(0)
                self.tcp_controller.host = ip_addresses[0]

            self._log_message(f"📡 检测到 {len(ip_addresses)} 个有效IP地址: {', '.join(ip_addresses)}")

        except Exception as e:
            self._log_message(f"❌ 获取IP地址失败: {str(e)}")
            # 添加默认IP作为备选
            self.local_ip_combo.addItem("127.0.0.1")
            self.tcp_controller.host = "127.0.0.1"

    def _on_ip_selection_changed(self, selected_ip):
        """处理IP选择变化"""
        if selected_ip and selected_ip != self.tcp_controller.host:
            old_ip = self.tcp_controller.host
            self.tcp_controller.host = selected_ip
            self._log_message(f"🔄 本机IP已更改: {old_ip} → {selected_ip}")

            # 如果服务器正在运行，提醒用户重启服务器
            if self.tcp_controller.server_running:
                self._log_message("⚠️  IP地址已更改，建议重启TCP服务器以使用新IP地址")





    def _log_message(self, message, level="信息"):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")

        # 定义日志级别映射
        level_map = {
            "调试": "DEBUG",
            "信息": "INFO",
            "警告": "WARNING",
            "错误": "ERROR",
            "严重": "CRITICAL"
        }

        # 定义日志级别颜色
        level_colors = {
            "调试": "#888888",    # 灰色
            "信息": "#000000",    # 黑色
            "警告": "#FF8C00",    # 橙色
            "错误": "#FF0000",    # 红色
            "严重": "#8B0000"     # 深红色
        }

        # 创建日志条目
        log_entry = {
            'timestamp': timestamp,
            'level': level,
            'message': message,
            'formatted': f"[{timestamp}] [{level}] {message}"
        }

        # 存储日志消息
        if hasattr(self, 'log_messages'):
            self.log_messages.append(log_entry)

            # 限制日志数量，避免内存过大
            if len(self.log_messages) > 1000:
                self.log_messages = self.log_messages[-800:]  # 保留最新800条

        # 检查是否应该显示此级别的日志
        if hasattr(self, 'current_log_level') and self._should_show_log(level):
            if hasattr(self, 'log_text'):
                # 使用HTML格式显示带颜色的日志
                color = level_colors.get(level, "#000000")
                html_message = f'<span style="color: {color};">[{timestamp}] [{level}] {message}</span>'
                self.log_text.append(html_message)

                # 自动滚动到底部
                scrollbar = self.log_text.verticalScrollBar()
                scrollbar.setValue(scrollbar.maximum())

        # 记录到系统日志
        log_level = level_map.get(level, "INFO")
        getattr(logger, log_level.lower())(message)

    def _should_show_log(self, level):
        """判断是否应该显示指定级别的日志"""
        if not hasattr(self, 'current_log_level'):
            return True

        # 定义级别优先级
        level_priority = {
            "全部": 0,
            "调试": 10,
            "信息": 20,
            "警告": 30,
            "错误": 40,
            "严重": 50
        }

        current_priority = level_priority.get(self.current_log_level, 20)
        message_priority = level_priority.get(level, 20)

        # 如果当前级别是"全部"，显示所有日志
        if self.current_log_level == "全部":
            return True

        # 否则只显示等于或高于当前级别的日志
        return message_priority >= current_priority

    def _on_log_level_changed(self, level):
        """日志级别改变时的处理"""
        self.current_log_level = level
        self._refresh_log_display()

    def _refresh_log_display(self):
        """刷新日志显示"""
        if not hasattr(self, 'log_text') or not hasattr(self, 'log_messages'):
            return

        # 清空当前显示
        self.log_text.clear()

        # 定义日志级别颜色
        level_colors = {
            "调试": "#888888",    # 灰色
            "信息": "#000000",    # 黑色
            "警告": "#FF8C00",    # 橙色
            "错误": "#FF0000",    # 红色
            "严重": "#8B0000"     # 深红色
        }

        # 重新显示符合条件的日志
        for log_entry in self.log_messages:
            if self._should_show_log(log_entry['level']):
                color = level_colors.get(log_entry['level'], "#000000")
                html_message = f'<span style="color: {color};">{log_entry["formatted"]}</span>'
                self.log_text.append(html_message)

        # 滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def _clear_logs(self):
        """清空日志"""
        if hasattr(self, 'log_text'):
            self.log_text.clear()
        if hasattr(self, 'log_messages'):
            self.log_messages.clear()

    def _has_pose_changed(self, new_pose_data):
        """
        检测位姿是否发生了显著变化

        Args:
            new_pose_data: 新的位姿数据字典

        Returns:
            bool: True表示位姿发生了显著变化，需要更新UI
        """
        if not self.pose_update_enabled:
            return False

        if self.last_pose_data is None:
            # 第一次接收数据，直接更新
            return True

        # 检查每个轴的变化是否超过阈值
        for key in ['x', 'y', 'z', 'rx', 'ry', 'rz']:
            old_value = self.last_pose_data.get(key, 0.0)
            new_value = new_pose_data.get(key, 0.0)

            # 计算变化量的绝对值
            change = abs(new_value - old_value)

            # 如果任何一个轴的变化超过阈值，认为位姿发生了变化
            if change > self.pose_change_threshold:
                return True

        # 所有轴的变化都在阈值内，认为位姿没有变化
        return False

    def _update_pose_cache(self, pose_data):
        """
        更新位姿缓存数据

        Args:
            pose_data: 位姿数据字典
        """
        self.last_pose_data = pose_data.copy() if pose_data else None

    def _set_pose_update_threshold(self, threshold):
        """
        设置位姿变化检测阈值

        Args:
            threshold: 阈值，单位为mm或度
        """
        self.pose_change_threshold = threshold
        self._log_message(f"🔧 位姿变化检测阈值已设置为: {threshold:.6f}", "调试")

    def _toggle_pose_cache(self):
        """切换位姿缓存机制的开关状态"""
        self.pose_update_enabled = not self.pose_update_enabled

        if self.pose_update_enabled:
            # 开启缓存机制
            self.pose_cache_btn.setText("智能缓存: 开")
            self.pose_cache_btn.setStyleSheet("""
                QPushButton {
                    background-color: #27ae60;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #2ecc71;
                }
            """)
            self._log_message("🔧 位姿智能缓存已开启 - 只在位姿发生显著变化时更新显示", "信息")
            self._log_message(f"   变化检测阈值: {self.pose_change_threshold:.6f} mm/°", "调试")
            # 更新状态栏
            if hasattr(self, 'cache_status_label'):
                self.cache_status_label.setText("智能缓存: 开启")
                self.cache_status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            # 关闭缓存机制
            self.pose_cache_btn.setText("智能缓存: 关")
            self.pose_cache_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border: none;
                    padding: 5px 10px;
                    border-radius: 3px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            self._log_message("🔧 位姿智能缓存已关闭 - 每次接收数据都会更新显示", "信息")
            # 清空缓存，确保下次数据会被更新
            self.last_pose_data = None
            # 更新状态栏
            if hasattr(self, 'cache_status_label'):
                self.cache_status_label.setText("智能缓存: 关闭")
                self.cache_status_label.setStyleSheet("color: red; font-weight: bold;")

    def _add_zernike_history_record(self, zfr_data):
        """
        添加Zernike数据历史记录

        Args:
            zfr_data: ZFR数据对象，包含PV、RMS、Power、Coma_X、Coma_Y等数值
        """
        try:
            # 获取当前时间
            current_time = time.strftime("%H:%M:%S")

            # 在表格末尾插入新行
            row_count = self.zernike_history_table.rowCount()
            self.zernike_history_table.insertRow(row_count)

            # 填充数据
            data = [
                current_time,
                f"{zfr_data.pv:.6f}",
                f"{zfr_data.rms:.6f}",
                f"{zfr_data.power:.6f}",
                f"{zfr_data.coma_x:.6f}",
                f"{zfr_data.coma_y:.6f}"
            ]

            for col, value in enumerate(data):
                item = QTableWidgetItem(value)
                # 设置文本居中对齐
                item.setTextAlignment(Qt.AlignCenter)
                self.zernike_history_table.setItem(row_count, col, item)

            # 自动滚动到最新添加的行
            self.zernike_history_table.scrollToBottom()

            # 选中新添加的行
            self.zernike_history_table.selectRow(row_count)

            # 记录日志
            self._log_message(f"📊 Zernike数据已添加到历史记录 (第{row_count + 1}条)", "调试")

        except Exception as e:
            self._log_message(f"❌ 添加历史记录失败: {str(e)}", "错误")

    def _clear_zernike_history(self):
        """清空Zernike数据历史记录"""
        try:
            reply = QMessageBox.question(
                self,
                "确认清空",
                "确定要清空所有Zernike数据历史记录吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.zernike_history_table.setRowCount(0)
                self._log_message("🗑️ Zernike数据历史记录已清空", "信息")

        except Exception as e:
            self._log_message(f"❌ 清空历史记录失败: {str(e)}", "错误")

    def _export_zernike_history(self):
        """导出Zernike数据历史记录到CSV文件"""
        try:
            if self.zernike_history_table.rowCount() == 0:
                QMessageBox.information(self, "提示", "没有历史记录可以导出")
                return

            # 选择保存文件
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出Zernike历史记录",
                f"zernike_history_{time.strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV文件 (*.csv)"
            )

            if file_path:
                import csv
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # 写入表头
                    headers = ["时间", "PV", "RMS", "Power", "Coma_X", "Coma_Y"]
                    writer.writerow(headers)

                    # 写入数据
                    for row in range(self.zernike_history_table.rowCount()):
                        row_data = []
                        for col in range(self.zernike_history_table.columnCount()):
                            item = self.zernike_history_table.item(row, col)
                            row_data.append(item.text() if item else "")
                        writer.writerow(row_data)

                self._log_message(f"📁 Zernike历史记录已导出到: {file_path}", "信息")
                QMessageBox.information(self, "导出成功", f"历史记录已成功导出到:\n{file_path}")

        except Exception as e:
            self._log_message(f"❌ 导出历史记录失败: {str(e)}", "错误")
            QMessageBox.critical(self, "导出失败", f"导出历史记录时发生错误:\n{str(e)}")

    def _clear_motion_history(self):
        """清空运动控制历史记录"""
        try:
            reply = QMessageBox.question(
                self, "确认清空", "确定要清空所有运动控制历史记录吗？\n此操作不可撤销。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.motion_history_table.setRowCount(0)
                self._log_message("🗑️ 运动控制历史记录已清空", "信息")

        except Exception as e:
            self._log_message(f"❌ 清空运动控制历史记录失败: {str(e)}", "错误")

    def _export_motion_history(self):
        """导出运动控制历史记录到JSON文件"""
        try:
            if self.motion_history_table.rowCount() == 0:
                QMessageBox.information(self, "提示", "没有历史记录可以导出")
                return

            # 选择保存文件
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出运动控制历史记录",
                f"motion_history_{time.strftime('%Y%m%d_%H%M%S')}.json",
                "JSON文件 (*.json)"
            )

            if file_path:
                import json

                # 收集数据
                data = {
                    "export_info": {
                        "export_time": time.strftime('%Y-%m-%d %H:%M:%S'),
                        "total_records": self.motion_history_table.rowCount(),
                        "description": "运动控制历史记录数据"
                    },
                    "records": []
                }

                # 获取表头
                headers = ["时间", "设备", "X(mm)", "Y(mm)", "Z(mm)", "A(°)", "B(°)", "C(°)",
                          "运动X", "运动Y", "运动Z", "运动A", "运动B", "运动C"]

                # 收集每行数据
                for row in range(self.motion_history_table.rowCount()):
                    record = {}
                    for col in range(self.motion_history_table.columnCount()):
                        item = self.motion_history_table.item(row, col)
                        record[headers[col]] = item.text() if item else ""
                    data["records"].append(record)

                # 写入JSON文件
                with open(file_path, 'w', encoding='utf-8') as jsonfile:
                    json.dump(data, jsonfile, ensure_ascii=False, indent=2)

                self._log_message(f"📁 运动控制历史记录已导出到: {file_path}", "信息")
                QMessageBox.information(self, "导出成功", f"历史记录已成功导出到:\n{file_path}")

        except Exception as e:
            self._log_message(f"❌ 导出运动控制历史记录失败: {str(e)}", "错误")
            QMessageBox.critical(self, "导出失败", f"导出历史记录时发生错误:\n{str(e)}")

    def _add_motion_history_record(self):
        """添加运动控制历史记录"""
        try:
            # 获取当前时间
            current_time = time.strftime('%Y-%m-%d %H:%M:%S')

            # 获取当前选中的设备
            selected_device = "未选择"
            device_number = "0"
            for device_type, radio in self.device_buttons.items():
                if radio.isChecked():
                    selected_device = device_type.display_name
                    if device_type.display_name == "主镜":
                        device_number = "1"
                    elif device_type.display_name == "次镜":
                        device_number = "2"
                    elif device_type.display_name == "三镜":
                        device_number = "3"
                    break

            # 获取当前设备位姿
            pos_x = self.pos_labels[0].text() if hasattr(self, 'pos_labels') else "--"
            pos_y = self.pos_labels[1].text() if hasattr(self, 'pos_labels') else "--"
            pos_z = self.pos_labels[2].text() if hasattr(self, 'pos_labels') else "--"
            rot_a = self.rot_labels[0].text() if hasattr(self, 'rot_labels') else "--"
            rot_b = self.rot_labels[1].text() if hasattr(self, 'rot_labels') else "--"
            rot_c = self.rot_labels[2].text() if hasattr(self, 'rot_labels') else "--"

            # 获取6轴运动控制变量
            motion_x = f"{self.motion_inputs['x'].value():.4f}" if 'x' in self.motion_inputs else "0.0000"
            motion_y = f"{self.motion_inputs['y'].value():.4f}" if 'y' in self.motion_inputs else "0.0000"
            motion_z = f"{self.motion_inputs['z'].value():.4f}" if 'z' in self.motion_inputs else "0.0000"
            motion_a = f"{self.motion_inputs['a'].value():.4f}" if 'a' in self.motion_inputs else "0.0000"
            motion_b = f"{self.motion_inputs['b'].value():.4f}" if 'b' in self.motion_inputs else "0.0000"
            motion_c = f"{self.motion_inputs['c'].value():.4f}" if 'c' in self.motion_inputs else "0.0000"

            # 添加到表格
            row_position = self.motion_history_table.rowCount()
            self.motion_history_table.insertRow(row_position)

            # 设置数据
            data = [current_time, device_number, pos_x, pos_y, pos_z, rot_a, rot_b, rot_c,
                   motion_x, motion_y, motion_z, motion_a, motion_b, motion_c]

            for col, value in enumerate(data):
                item = QTableWidgetItem(str(value))
                self.motion_history_table.setItem(row_position, col, item)

            # 滚动到最新记录
            self.motion_history_table.scrollToBottom()

            self._log_message(f"📝 运动控制记录已添加: 设备{device_number} 时间{current_time}", "信息")

        except Exception as e:
            self._log_message(f"❌ 添加运动控制记录失败: {str(e)}", "错误")





    def _create_layer3_params_and_control(self, parent_layout):
        """创建第三层：运动参数选择 + 自动控制"""
        layer3_widget = QWidget()
        layer3_layout = QHBoxLayout(layer3_widget)
        layer3_layout.setContentsMargins(0, 0, 0, 0)
        layer3_layout.setSpacing(15)

        # 左侧：运动参数选择
        params_group = QGroupBox("运动参数选择")
        params_layout = QGridLayout(params_group)

        # 运动等级
        level_label = QLabel("运动等级:")
        level_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        params_layout.addWidget(level_label, 0, 0)
        self.level_combo = QComboBox()
        self.level_combo.addItems([
            "精细 (0.0006)",
            "标准 (0.001)",
            "粗调 (0.01)"
        ])
        self.level_combo.setCurrentIndex(1)  # 默认选择标准
        self.level_combo.setStyleSheet("font-size: 13px;")
        self.level_combo.currentIndexChanged.connect(self._on_level_changed)
        params_layout.addWidget(self.level_combo, 0, 1)

        # 平移旋转比例因子
        ratio_label = QLabel("平移旋转比例因子:")
        ratio_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        params_layout.addWidget(ratio_label, 1, 0)
        self.ratio_spinbox = QSpinBox()
        self.ratio_spinbox.setRange(1, 10)
        self.ratio_spinbox.setValue(5)  # 默认5
        self.ratio_spinbox.setStyleSheet("font-size: 13px;")
        self.ratio_spinbox.setToolTip("平移与旋转的比例因子，范围1-10的整数")
        self.ratio_spinbox.valueChanged.connect(self._on_ratio_changed)
        params_layout.addWidget(self.ratio_spinbox, 1, 1)

        layer3_layout.addWidget(params_group)

        # 右侧：自动控制
        self._create_auto_control_panel(layer3_layout)

        parent_layout.addWidget(layer3_widget)

    def _create_auto_control_panel(self, parent_layout):
        """创建自动控制面板"""
        control_group = QGroupBox("自动控制")
        control_layout = QVBoxLayout(control_group)

        # 6轴运动输入
        axes_layout = QGridLayout()

        # 位移轴 (X, Y, Z)
        translation_axes = [('X', 'mm'), ('Y', 'mm'), ('Z', 'mm')]
        rotation_axes = [('A', '°'), ('B', '°'), ('C', '°')]

        # 位移轴
        for i, (axis, unit) in enumerate(translation_axes):
            axis_label = QLabel(f"{axis}轴:")
            axis_label.setStyleSheet("font-size: 14px; font-weight: bold;")
            axes_layout.addWidget(axis_label, 0, i*2)

            pos_input = QDoubleSpinBox()
            pos_input.setRange(-100.0, 100.0)
            pos_input.setValue(0.0)
            pos_input.setSingleStep(0.001)
            pos_input.setDecimals(6)
            pos_input.setSuffix(f" {unit}")
            pos_input.setStyleSheet("font-size: 13px;")
            pos_input.setReadOnly(False)  # 改为可编辑，允许用户手动输入
            axes_layout.addWidget(pos_input, 0, i*2+1)

            self.motion_inputs[axis.lower()] = pos_input

        # 旋转轴
        for i, (axis, unit) in enumerate(rotation_axes):
            axis_label = QLabel(f"{axis}轴:")
            axis_label.setStyleSheet("font-size: 14px; font-weight: bold;")
            axes_layout.addWidget(axis_label, 1, i*2)

            rot_input = QDoubleSpinBox()
            rot_input.setRange(-360.0, 360.0)
            rot_input.setValue(0.0)
            rot_input.setSingleStep(0.001)
            rot_input.setDecimals(6)
            rot_input.setSuffix(f" {unit}")
            rot_input.setStyleSheet("font-size: 13px;")
            rot_input.setReadOnly(False)  # 改为可编辑，允许用户手动输入
            axes_layout.addWidget(rot_input, 1, i*2+1)

            self.motion_inputs[axis.lower()] = rot_input

        control_layout.addLayout(axes_layout)

        # 控制按钮
        button_layout = QHBoxLayout()

        self.execute_btn = QPushButton("执行运动")
        self.execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover { background-color: #1976D2; }
            QPushButton:disabled { background-color: #cccccc; color: #666666; }
        """)
        self.execute_btn.clicked.connect(self._execute_motion)
        self.execute_btn.setEnabled(False)

        self.stop_btn = QPushButton("紧急停止")
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover { background-color: #da190b; }
        """)
        self.stop_btn.clicked.connect(self._emergency_stop)

        button_layout.addWidget(self.execute_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addStretch()

        control_layout.addLayout(button_layout)
        parent_layout.addWidget(control_group)

    def _create_layer4_system_log(self, parent_layout):
        """创建第四层：系统日志"""
        log_group = QGroupBox("系统日志")
        log_layout = QVBoxLayout(log_group)

        self.log_text = QTextEdit()
        # 移除高度限制，让日志区域自适应
        self.log_text.setMinimumHeight(250)
        log_layout.addWidget(self.log_text)

        parent_layout.addWidget(log_group)

    def _connect_signals(self):
        """连接信号"""
        # TCP控制器信号
        self.tcp_controller.connection_changed.connect(self._on_connection_changed)
        self.tcp_controller.command_sent.connect(self._on_command_sent)
        self.tcp_controller.error_occurred.connect(self._on_error)
        self.tcp_controller.pose_data_received.connect(self._on_pose_data_received)
        self.tcp_controller.device_status_updated.connect(self._on_device_status_updated)
        self.tcp_controller.query_result_received.connect(self._on_query_result_received)



    def _on_device_selected(self, device_type, checked):
        """设备选择变化处理"""
        if checked:
            self.tcp_controller.select_device(device_type)
            self.device_selected = True
            self._log_message(f"📱 已选择设备: {device_type.display_name} ({device_type.ip_address})")

            if self.is_connected:
                self._log_message("💡 提示: 现在可以点击'刷新位姿'获取当前设备状态")

    def _refresh_pose(self):
        """刷新设备位姿 - 发送特殊指令触发客户端开始实时数据发送"""
        if not self.is_connected:
            QMessageBox.warning(self, "警告", "请先连接到运动控制计算机")
            return

        if not self.device_selected:
            QMessageBox.warning(self, "警告", "请先选择当前控制设备")
            return

        # 发送位姿刷新指令 ($$$$设备IP+MOV X0)
        self._log_message("📡 正在启动实时位姿数据接收...")
        success = self.tcp_controller.refresh_pose_data()
        if success:
            self._log_message("✅ 位姿刷新指令已发送")
            self._log_message("🔄 客户端将开始以50ms频率发送实时位姿数据")
            self._log_message(f"📊 当前监控设备: {self.tcp_controller.current_device.display_name}")
        else:
            self._log_message("❌ 位姿刷新指令发送失败")









    def _clear_all_motion_inputs(self):
        """清零所有6个运动输入控件"""
        self.motion_inputs['x'].setValue(0.0)
        self.motion_inputs['y'].setValue(0.0)
        self.motion_inputs['z'].setValue(0.0)
        self.motion_inputs['a'].setValue(0.0)
        self.motion_inputs['b'].setValue(0.0)
        self.motion_inputs['c'].setValue(0.0)
        self._log_message("🔄 已清零所有运动轴数值")

    def _power_motion_control(self):
        """Power独立运动控制"""
        if not self.zfr_data_loaded:
            QMessageBox.warning(self, "警告", "请先加载ZFR数据")
            return

        self._log_message("🔍 开始Power运动控制分析...")

        # 先清零所有6个输入控件
        self._clear_all_motion_inputs()

        # 获取Power数据
        power = self.current_zfr_data.power
        self._log_message(f"📊 Power数据: {power:.6f}")

        # 显示运动参数
        level_names = ["精细 (0.0006)", "标准 (0.001)", "粗调 (0.01)"]
        current_level = level_names[self.level_combo.currentIndex()]
        self._log_message(f"⚙️ 运动参数: {current_level}, 比例因子: {self.translation_rotation_ratio}")

        # 计算Z轴运动（基于power值）
        if power > 0:
            dz = self.motion_level * self.translation_rotation_ratio
        else:
            dz = -self.motion_level * self.translation_rotation_ratio

        # 更新Z轴运动量，其他轴保持当前值
        self.motion_inputs['z'].setValue(dz)

        self._log_message(f"� Power控制计算结果:")
        self._log_message(f"   Z轴: {dz:.6f} mm")

        # 启用执行运动按钮
        self.execute_btn.setEnabled(True)
        self._log_message("✅ Power运动控制分析完成")

    def _coma_x_motion_control(self):
        """Coma_X独立运动控制"""
        if not self.zfr_data_loaded:
            QMessageBox.warning(self, "警告", "请先加载ZFR数据")
            return

        self._log_message("🔍 开始Coma_X运动控制分析...")

        # 先清零所有6个输入控件
        self._clear_all_motion_inputs()

        # 获取Coma_X数据
        coma_x = self.current_zfr_data.coma_x
        self._log_message(f"📊 Coma_X数据: {coma_x:.6f}")

        # 显示运动参数
        level_names = ["精细 (0.0006)", "标准 (0.001)", "粗调 (0.01)"]
        current_level = level_names[self.level_combo.currentIndex()]
        self._log_message(f"⚙️ 运动参数: {current_level}, 比例因子: {self.translation_rotation_ratio}")

        # 计算X轴运动和B轴旋转（基于coma_x值）
        if coma_x > 0:
            dx = self.motion_level * self.translation_rotation_ratio  # X轴正向
            db = -self.motion_level  # B轴负向旋转
        else:
            dx = -self.motion_level * self.translation_rotation_ratio  # X轴负向
            db = self.motion_level  # B轴正向旋转

        # 更新X轴和B轴运动量，其他轴保持当前值
        self.motion_inputs['x'].setValue(dx)
        self.motion_inputs['b'].setValue(db)

        self._log_message(f"📐 Coma_X控制计算结果:")
        self._log_message(f"   X轴: {dx:.6f} mm")
        self._log_message(f"   B轴: {db:.6f} °")

        # 启用执行运动按钮
        self.execute_btn.setEnabled(True)
        self._log_message("✅ Coma_X运动控制分析完成")

    def _coma_y_motion_control(self):
        """Coma_Y独立运动控制"""
        if not self.zfr_data_loaded:
            QMessageBox.warning(self, "警告", "请先加载ZFR数据")
            return

        self._log_message("� 开始Coma_Y运动控制分析...")

        # 先清零所有6个输入控件
        self._clear_all_motion_inputs()

        # 获取Coma_Y数据
        coma_y = self.current_zfr_data.coma_y
        self._log_message(f"📊 Coma_Y数据: {coma_y:.6f}")

        # 显示运动参数
        level_names = ["精细 (0.0006)", "标准 (0.001)", "粗调 (0.01)"]
        current_level = level_names[self.level_combo.currentIndex()]
        self._log_message(f"⚙️ 运动参数: {current_level}, 比例因子: {self.translation_rotation_ratio}")

        # 计算Y轴运动和A轴旋转（基于coma_y值）
        if coma_y > 0:
            dy = -self.motion_level * self.translation_rotation_ratio  # Y轴负向
            da = -self.motion_level  # A轴负向旋转
        else:
            dy = self.motion_level * self.translation_rotation_ratio  # Y轴正向
            da = self.motion_level  # A轴正向旋转

        # 更新Y轴和A轴运动量，其他轴保持当前值
        self.motion_inputs['y'].setValue(dy)
        self.motion_inputs['a'].setValue(da)

        self._log_message(f"📐 Coma_Y控制计算结果:")
        self._log_message(f"   Y轴: {dy:.6f} mm")
        self._log_message(f"   A轴: {da:.6f} °")

        # 启用执行运动按钮
        self.execute_btn.setEnabled(True)
        self._log_message("✅ Coma_Y运动控制分析完成")

    def _calculate_motion_values(self, power, coma_x, coma_y):
        """
        根据您指定的运动规则计算各轴运动量

        运动规则：
        1. power值为正，则z轴值为运动等级*平移旋转比例，反之为负值
        2. coma_x值为正，x轴正向移动(运动等级*平移旋转比例)并配合B轴负向旋转(运动等级)，反之相反
        3. coma_y值为正，Y轴负向移动(运动等级*平移旋转比例)并配合A轴负向旋转(运动等级)，反之相反
        """
        # Z轴运动（基于power值）
        if power > 0:
            dz = self.motion_level * self.translation_rotation_ratio
        else:
            dz = -self.motion_level * self.translation_rotation_ratio

        # X轴运动和B轴旋转（基于coma_x值）
        if coma_x > 0:
            dx = self.motion_level * self.translation_rotation_ratio  # X轴正向
            db = -self.motion_level  # B轴负向旋转
        else:
            dx = -self.motion_level * self.translation_rotation_ratio  # X轴负向
            db = self.motion_level  # B轴正向旋转

        # Y轴运动和A轴旋转（基于coma_y值）
        if coma_y > 0:
            dy = -self.motion_level * self.translation_rotation_ratio  # Y轴负向
            da = -self.motion_level  # A轴负向旋转
        else:
            dy = self.motion_level * self.translation_rotation_ratio  # Y轴正向
            da = self.motion_level  # A轴正向旋转

        # C轴暂时设为0（您未指定规则）
        dc = 0.0

        return dx, dy, dz, da, db, dc

    def _execute_motion(self):
        """执行运动"""
        if not self.is_connected:
            QMessageBox.warning(self, "警告", "请先连接到运动控制计算机")
            return

        if not self.device_selected:
            QMessageBox.warning(self, "警告", "请先选择当前控制设备")
            return

        # 获取运动量（直接使用显示的值）
        dx = self.motion_inputs['x'].value()
        dy = self.motion_inputs['y'].value()
        dz = self.motion_inputs['z'].value()
        drx = self.motion_inputs['a'].value()
        dry = self.motion_inputs['b'].value()
        drz = self.motion_inputs['c'].value()

        # 确认执行
        reply = QMessageBox.question(
            self,
            "确认执行",
            f"确定要执行以下运动吗？\n\n"
            f"平移: X={dx:.6f}mm, Y={dy:.6f}mm, Z={dz:.6f}mm\n"
            f"旋转: A={drx:.6f}°, B={dry:.6f}°, C={drz:.6f}°",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self._log_message("🚀 正在执行运动指令...")

            # 发送6轴运动指令
            success = self.tcp_controller.send_motion_command(
                target_device=self.tcp_controller.current_device,
                dx=dx, dy=dy, dz=dz, drx=drx, dry=dry, drz=drz
            )

            if success:
                self._log_message(f"✅ 运动指令已发送:")
                self._log_message(f"   平移: X={dx:.6f}mm, Y={dy:.6f}mm, Z={dz:.6f}mm")
                self._log_message(f"   旋转: A={drx:.6f}°, B={dry:.6f}°, C={drz:.6f}°")
                self._log_message("💡 提示: 运动完成后可刷新位姿查看结果")

                # 添加运动控制历史记录
                self._add_motion_history_record()
            else:
                self._log_message("❌ 运动指令发送失败")
        else:
            self._log_message("❌ 用户取消运动执行")

    def _emergency_stop(self):
        """紧急停止"""
        success = self.tcp_controller.stop_all_devices()
        if success:
            self._log_message("🛑 紧急停止指令已发送")
        else:
            self._log_message("❌ 紧急停止指令发送失败")

    def _on_level_changed(self, index):
        """运动等级变化"""
        levels = [0.0006, 0.001, 0.01]
        self.motion_level = levels[index]
        level_names = ["精细", "标准", "粗调"]
        self._log_message(f"🔧 运动等级设置为: {level_names[index]} ({self.motion_level})")

    def _on_ratio_changed(self, value):
        """平移旋转比例因子变化"""
        self.translation_rotation_ratio = value
        self._log_message(f"⚖️ 平移旋转比例因子设置为: {value}")

    def _on_connection_changed(self, connected):
        """客户端连接状态变化"""
        self.is_connected = connected

        if connected:
            from src.config.device_config import device_config
            server_ip, _ = device_config.get_motion_control_server()
            self.refresh_pose_btn.setEnabled(True)
            self._log_message(f"✅ 运动控制计算机已连接 ({server_ip})", "信息")
            # 更新状态栏
            if hasattr(self, 'connection_status_label'):
                self.connection_status_label.setText("已连接")
                self.connection_status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            self.refresh_pose_btn.setEnabled(False)
            self._log_message("⚠️ 运动控制计算机连接断开", "警告")
            self.execute_btn.setEnabled(False)
            # 更新状态栏
            if hasattr(self, 'connection_status_label'):
                self.connection_status_label.setText("未连接")
                self.connection_status_label.setStyleSheet("color: red; font-weight: bold;")

    def _on_command_sent(self, command):
        """命令发送成功"""
        self._log_message(f"📤 指令已发送: {command}", "信息")

    def _on_error(self, error_msg):
        """错误处理"""
        self._log_message(f"❌ 错误: {error_msg}", "错误")

    def _on_pose_data_received(self, pose_data):
        """处理接收到的位姿数据 - 带缓存机制避免无意义刷新"""
        try:
            devices = pose_data.get('devices', [])

            # 根据当前选择的设备更新界面
            current_device_id = self._get_current_device_id()

            for device in devices:
                if device.get('id') == current_device_id:
                    # 构建位姿数据字典用于变化检测
                    position_data = {
                        'x': device.get('X', 0),  # X位移
                        'y': device.get('Y', 0),  # Y位移
                        'z': device.get('Z', 0),  # Z位移
                        'rx': device.get('A', 0), # A角度
                        'ry': device.get('B', 0), # B角度
                        'rz': device.get('C', 0)  # C角度
                    }

                    # 检查位姿是否发生了显著变化
                    if self._has_pose_changed(position_data):
                        # 位姿发生变化，更新位姿显示
                        # 第一行显示角度信息（A, B, C -> 位置标签）
                        self.pos_x_label.setText(f"{device.get('A', 0):.6f}")
                        self.pos_y_label.setText(f"{device.get('B', 0):.6f}")
                        self.pos_z_label.setText(f"{device.get('C', 0):.6f}")
                        # 第二行显示位置信息（X, Y, Z -> 角度标签）
                        self.rot_a_label.setText(f"{device.get('X', 0):.6f}")
                        self.rot_b_label.setText(f"{device.get('Y', 0):.6f}")
                        self.rot_c_label.setText(f"{device.get('Z', 0):.6f}")

                        # 更新当前位姿数据 - 保持数据语义正确
                        self.current_pose = {
                            'x': device.get('X', 0),  # X仍然是X位移
                            'y': device.get('Y', 0),  # Y仍然是Y位移
                            'z': device.get('Z', 0),  # Z仍然是Z位移
                            'a': device.get('A', 0),  # A仍然是A角度
                            'b': device.get('B', 0),  # B仍然是B角度
                            'c': device.get('C', 0)   # C仍然是C角度
                        }

                        # 更新缓存
                        self._update_pose_cache(position_data)
                    else:
                        # 位姿没有显著变化，跳过UI更新，但仍然更新缓存
                        self._update_pose_cache(position_data)
                    break

        except Exception as e:
            self._log_message(f"❌ 位姿数据处理失败: {str(e)}", "错误")

    def _on_device_status_updated(self, device_ip, status_data):
        """处理设备状态更新 - 带缓存机制避免无意义刷新"""
        try:
            position = status_data.get('position', {})
            connected = status_data.get('connected', False)

            # 如果是当前选择的设备，更新界面
            current_device_ip = self.tcp_controller.current_device.ip_address
            if device_ip == current_device_ip:
                if connected:
                    # 检查位姿是否发生了显著变化
                    if self._has_pose_changed(position):
                        # 位姿发生变化，更新GUI显示标签
                        self.pos_x_label.setText(f"{position.get('x', 0):.6f}")
                        self.pos_y_label.setText(f"{position.get('y', 0):.6f}")
                        self.pos_z_label.setText(f"{position.get('z', 0):.6f}")
                        self.rot_a_label.setText(f"{position.get('rx', 0):.6f}")
                        self.rot_b_label.setText(f"{position.get('ry', 0):.6f}")
                        self.rot_c_label.setText(f"{position.get('rz', 0):.6f}")

                        # 更新当前位姿数据
                        self.current_pose = {
                            'x': position.get('x', 0),
                            'y': position.get('y', 0),
                            'z': position.get('z', 0),
                            'a': position.get('rx', 0),
                            'b': position.get('ry', 0),
                            'c': position.get('rz', 0)
                        }

                        # 更新缓存
                        self._update_pose_cache(position)

                        # 记录位姿变化（使用调试级别，避免日志过多）
                        self._log_message(f"📍 设备 {device_ip} 位姿已更新: X={position.get('x', 0):.3f}, Y={position.get('y', 0):.3f}, Z={position.get('z', 0):.3f}, A={position.get('rx', 0):.3f}, B={position.get('ry', 0):.3f}, C={position.get('rz', 0):.3f}", "调试")
                    else:
                        # 位姿没有显著变化，跳过UI更新，但仍然更新缓存
                        self._update_pose_cache(position)
                else:
                    self._log_message(f"⚠️ 设备 {device_ip} 连接断开", "警告")

        except Exception as e:
            self._log_message(f"❌ 设备状态处理失败: {str(e)}", "错误")

    def _get_current_device_id(self):
        """获取当前设备ID（从配置文件读取）"""
        from src.config.device_config import device_config
        current_device = self.tcp_controller.current_device
        device_id = device_config.get_device_id_by_type(current_device.type_name)
        return device_id or "device1"

    # ==================== 文件传输相关方法 ====================

    def _load_file_transfer_config(self):
        """加载文件传输配置"""
        try:
            from src.config.file_transfer_config import file_transfer_config
            self.file_transfer_config = file_transfer_config
            self.client_config = self.file_transfer_config.get_client_config()
        except Exception as e:
            print(f"加载文件传输配置失败: {e}")
            self.client_config = {
                "server_ip": "127.0.0.1",
                "server_port": "8888",
                "storage_path": ""
            }

    def _save_client_config(self):
        """保存客户端配置"""
        try:
            server_ip = self.server_ip_combo.currentText().strip()
            server_port = self.server_port_edit.text().strip()
            storage_path = self.storage_path_edit.text().strip()

            if hasattr(self, 'file_transfer_config'):
                self.file_transfer_config.save_client_config(server_ip, server_port, storage_path)
        except Exception as e:
            print(f"保存客户端配置失败: {e}")

    def _get_local_ips(self):
        """获取本机IP地址列表"""
        ips = ["127.0.0.1"]
        try:
            import socket
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            if local_ip not in ips:
                ips.append(local_ip)
        except:
            pass
        return ips

    def _connect_to_server(self):
        """连接到文件传输服务器"""
        try:
            server_ip = self.server_ip_combo.currentText().strip()
            server_port = int(self.server_port_edit.text().strip())

            if not server_ip or server_port <= 0:
                QMessageBox.warning(self, "错误", "请输入有效的服务器IP和端口")
                return

            # 创建socket连接
            import socket
            self.file_transfer_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.file_transfer_socket.connect((server_ip, server_port))

            self.is_file_transfer_connected = True
            self.connect_btn.setEnabled(False)
            self.disconnect_btn.setEnabled(True)
            self.receive_btn.setEnabled(True)

            self._log_message(f"✅ 已连接到文件传输服务器: {server_ip}:{server_port}")

            # 保存连接配置
            self._save_client_config()

            # 保存到持久化设置
            self._save_persistent_settings()

        except Exception as e:
            QMessageBox.critical(self, "连接失败", f"无法连接到服务器: {str(e)}")
            self._log_message(f"❌ 文件传输连接失败: {str(e)}")

    def _disconnect_from_server(self):
        """断开与文件传输服务器的连接"""
        try:
            if self.file_transfer_socket:
                self.file_transfer_socket.close()
                self.file_transfer_socket = None

            self.is_file_transfer_connected = False
            self.connect_btn.setEnabled(True)
            self.disconnect_btn.setEnabled(False)
            self.receive_btn.setEnabled(False)

            self._log_message("🔌 已断开文件传输服务器连接")

        except Exception as e:
            self._log_message(f"❌ 文件传输断开连接错误: {str(e)}")

    def _browse_storage_path(self):
        """浏览选择存储路径"""
        folder = QFileDialog.getExistingDirectory(self, "选择文件存储目录")
        if folder:
            self.storage_path = folder
            self.storage_path_edit.setText(folder)
            self._log_message(f"📁 已设置文件存储路径: {folder}")

            # 保存存储路径配置
            self._save_client_config()

            # 保存到持久化设置
            self._save_persistent_settings()

    def _receive_file(self):
        """从服务器接收文件"""
        if not self.is_file_transfer_connected:
            QMessageBox.warning(self, "错误", "请先连接到服务器")
            return

        if not self.storage_path:
            QMessageBox.warning(self, "错误", "请先设置文件存储路径")
            return

        try:
            # 接收文件长度（4字节）
            length_bytes = self._recv_all(4)
            if not length_bytes:
                raise Exception("服务器连接已断开")

            message_length = int.from_bytes(length_bytes, byteorder='big')

            # 接收文件数据
            message_bytes = self._recv_all(message_length)
            if not message_bytes:
                raise Exception("接收文件数据失败")

            # 解析JSON数据
            import json
            file_info = json.loads(message_bytes.decode('utf-8'))

            # 保存文件
            import os
            file_path = os.path.join(self.storage_path, file_info['filename'])
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(file_info['content'])

            self._log_message(f"✅ 文件接收成功: {file_info['filename']} ({file_info['size']} 字节)")
            self._log_message(f"📁 保存路径: {file_path}")

            # 解析ZFR数据
            zfr_data = self._parse_received_zfr_file(file_path)
            if zfr_data:
                self._update_transfer_zfr_display(zfr_data)
                self._log_message("📊 ZFR数据解析完成")

                # 添加到历史记录
                self._add_zernike_history_record(zfr_data)
                self._log_message("📋 ZFR数据已添加到历史记录", "信息")

            QMessageBox.information(self, "成功", f"文件接收成功！\n保存路径: {file_path}")

        except Exception as e:
            QMessageBox.critical(self, "接收失败", f"文件接收失败: {str(e)}")
            self._log_message(f"❌ 文件接收失败: {str(e)}")

    def _recv_all(self, length):
        """接收指定长度的数据"""
        data = b''
        while len(data) < length:
            packet = self.file_transfer_socket.recv(length - len(data))
            if not packet:
                return None
            data += packet
        return data

    def _parse_received_zfr_file(self, file_path):
        """解析接收到的ZFR文件"""
        try:
            import re

            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 提取PV值
            pv_pattern = r'PV:\s*([-+]?\d*\.?\d+(?:[eE][-+]?\d+)?)'
            pv_match = re.search(pv_pattern, content)
            pv_value = float(pv_match.group(1)) if pv_match else 0.0

            # 提取RMS值
            rms_pattern = r'RMS:\s*([-+]?\d*\.?\d+(?:[eE][-+]?\d+)?)'
            rms_match = re.search(rms_pattern, content)
            rms_value = float(rms_match.group(1)) if rms_match else 0.0

            # 提取ZFR数据
            zfr_pattern = r'ZFR\s+(\d+)\s+([-+]?\d*\.?\d+(?:[eE][-+]?\d+)?)'
            matches = re.findall(zfr_pattern, content)

            zfr_data = {}
            for match in matches:
                zfr_index = int(match[0])
                zfr_value = float(match[1])
                zfr_data[zfr_index] = zfr_value

            # 提取目标ZFR值
            power = zfr_data.get(3, 0.0)      # ZFR 3 -> Power
            coma_x = zfr_data.get(6, 0.0)     # ZFR 6 -> Coma_X
            coma_y = zfr_data.get(7, 0.0)     # ZFR 7 -> Coma_Y

            # 创建ZFR数据对象
            from src.data_models.zfr_data import ZFRData
            return ZFRData(
                power=power,
                coma_x=coma_x,
                coma_y=coma_y,
                pv=pv_value,
                rms=rms_value
            )

        except Exception as e:
            self._log_message(f"❌ ZFR文件解析失败: {str(e)}")
            return None

    def _update_transfer_zfr_display(self, zfr_data):
        """更新文件传输ZFR数据显示"""
        self.transfer_pv_edit.setText(f"{zfr_data.pv:.6f}")
        self.transfer_rms_edit.setText(f"{zfr_data.rms:.6f}")
        self.transfer_power_edit.setText(f"{zfr_data.power:.6f}")
        self.transfer_coma_x_edit.setText(f"{zfr_data.coma_x:.6f}")
        self.transfer_coma_y_edit.setText(f"{zfr_data.coma_y:.6f}")

        # 激活控制按钮
        self.transfer_power_control_btn.setEnabled(True)
        self.transfer_coma_x_control_btn.setEnabled(True)
        self.transfer_coma_y_control_btn.setEnabled(True)

        # 保存当前ZFR数据
        self.current_transfer_zfr_data = zfr_data

    def _transfer_power_motion_control(self):
        """文件传输Power运动控制 - 计算并填充到6轴控制区域"""
        if not hasattr(self, 'current_transfer_zfr_data') or not self.current_transfer_zfr_data:
            QMessageBox.warning(self, "警告", "没有可用的ZFR数据")
            return

        try:
            power_value = self.current_transfer_zfr_data.power

            # 计算Z轴运动量 - 仅根据正负判断方向
            if power_value > 0:
                z_motion = self.motion_level * self.translation_rotation_ratio
            else:
                z_motion = -self.motion_level * self.translation_rotation_ratio

            # 先清零所有6轴控制输入
            self._clear_all_motion_inputs()

            # 将计算结果填充到6轴运动控制区域
            self.motion_inputs['z'].setValue(z_motion)

            # 记录计算结果
            self._log_message(f"🎯 Power控制计算完成，已填充到6轴控制区域:", "信息")
            self._log_message(f"   Power值: {power_value:.6f} ({'正' if power_value > 0 else '负'})", "调试")
            self._log_message(f"   Z轴: {z_motion:.6f} mm", "信息")
            self._log_message("💡 请检查数值后点击'执行运动'按钮", "信息")

            # 启用执行运动按钮
            self.execute_btn.setEnabled(True)

        except Exception as e:
            self._log_message(f"❌ Power控制计算失败: {str(e)}")

    def _transfer_coma_x_motion_control(self):
        """文件传输Coma_X运动控制 - 计算并填充到6轴控制区域"""
        if not hasattr(self, 'current_transfer_zfr_data') or not self.current_transfer_zfr_data:
            QMessageBox.warning(self, "警告", "没有可用的ZFR数据")
            return

        try:
            coma_x_value = self.current_transfer_zfr_data.coma_x

            # 计算运动量 - 仅根据正负判断方向
            # coma_x值为正：X轴正向移动 + B轴负向旋转，反之相反
            if coma_x_value > 0:
                x_motion = self.motion_level * self.translation_rotation_ratio  # X轴正向
                b_motion = -self.motion_level  # B轴负向旋转
            else:
                x_motion = -self.motion_level * self.translation_rotation_ratio  # X轴负向
                b_motion = self.motion_level  # B轴正向旋转

            # 先清零所有6轴控制输入
            self._clear_all_motion_inputs()

            # 将计算结果填充到6轴运动控制区域
            self.motion_inputs['x'].setValue(x_motion)
            self.motion_inputs['b'].setValue(b_motion)

            # 记录计算结果
            self._log_message(f"🎯 Coma_X控制计算完成，已填充到6轴控制区域:", "信息")
            self._log_message(f"   Coma_X值: {coma_x_value:.6f} ({'正' if coma_x_value > 0 else '负'})", "调试")
            self._log_message(f"   X轴: {x_motion:.6f} mm", "信息")
            self._log_message(f"   B轴: {b_motion:.6f} °", "信息")
            self._log_message("💡 请检查数值后点击'执行运动'按钮", "信息")

            # 启用执行运动按钮
            self.execute_btn.setEnabled(True)

        except Exception as e:
            self._log_message(f"❌ Coma_X控制计算失败: {str(e)}")

    def _transfer_coma_y_motion_control(self):
        """文件传输Coma_Y运动控制 - 计算并填充到6轴控制区域"""
        if not hasattr(self, 'current_transfer_zfr_data') or not self.current_transfer_zfr_data:
            QMessageBox.warning(self, "警告", "没有可用的ZFR数据")
            return

        try:
            coma_y_value = self.current_transfer_zfr_data.coma_y

            # 计算运动量 - 仅根据正负判断方向
            # coma_y值为正：Y轴负向移动 + A轴负向旋转，反之相反
            if coma_y_value > 0:
                y_motion = -self.motion_level * self.translation_rotation_ratio  # Y轴负向
                a_motion = -self.motion_level  # A轴负向旋转
            else:
                y_motion = self.motion_level * self.translation_rotation_ratio  # Y轴正向
                a_motion = self.motion_level  # A轴正向旋转

            # 先清零所有6轴控制输入
            self._clear_all_motion_inputs()

            # 将计算结果填充到6轴运动控制区域
            self.motion_inputs['y'].setValue(y_motion)
            self.motion_inputs['a'].setValue(a_motion)

            # 记录计算结果
            self._log_message(f"🎯 Coma_Y控制计算完成，已填充到6轴控制区域:", "信息")
            self._log_message(f"   Coma_Y值: {coma_y_value:.6f} ({'正' if coma_y_value > 0 else '负'})", "调试")
            self._log_message(f"   Y轴: {y_motion:.6f} mm", "信息")
            self._log_message(f"   A轴: {a_motion:.6f} °", "信息")
            self._log_message("💡 请检查数值后点击'执行运动'按钮", "信息")

            # 启用执行运动按钮
            self.execute_btn.setEnabled(True)

        except Exception as e:
            self._log_message(f"❌ Coma_Y控制计算失败: {str(e)}")

    # ==================== TCP服务器控制方法 ====================

    def _start_tcp_server(self):
        """启动TCP服务器"""
        port = self.port_spinbox.value()

        self._log_message(f"正在启动TCP服务器，监听端口 {port}...")
        success = self.tcp_controller.start_server(self.tcp_controller.host, port)

        if success:
            self.start_server_btn.setEnabled(False)
            self.stop_server_btn.setEnabled(True)
            self.query_btn.setEnabled(True)
            self.port_spinbox.setEnabled(False)

            self._log_message("✅ TCP服务器启动成功")
            from src.config.device_config import device_config
            server_ip, server_port = device_config.get_motion_control_server()
            self._log_message(f"📡 等待运动控制计算机({server_ip})连接...")
            self._log_message("📋 请按照以下步骤操作:")
            self._log_message("   1. 确保运动控制计算机连接到本机")
            self._log_message("   2. 选择当前控制设备")
            self._log_message("   3. 选择偏心倾斜数据路径")
            self._log_message("   4. 刷新数据并进行运动控制分析")

            # 更新状态栏
            if hasattr(self, 'server_status_label'):
                self.server_status_label.setText("TCP服务器: 已启动")
                self.server_status_label.setStyleSheet("color: green; font-weight: bold;")
        else:
            self._log_message("❌ TCP服务器启动失败")
            # 更新状态栏
            if hasattr(self, 'server_status_label'):
                self.server_status_label.setText("TCP服务器: 启动失败")
                self.server_status_label.setStyleSheet("color: red; font-weight: bold;")

    def _stop_tcp_server(self):
        """停止TCP服务器"""
        self.tcp_controller.stop_server()

        self.start_server_btn.setEnabled(True)
        self.stop_server_btn.setEnabled(False)
        self.query_btn.setEnabled(False)
        self.port_spinbox.setEnabled(True)

        self._log_message("🔌 TCP服务器已停止")

        # 更新状态栏
        if hasattr(self, 'server_status_label'):
            self.server_status_label.setText("TCP服务器: 未启动")
            self.server_status_label.setStyleSheet("color: red; font-weight: bold;")

    def _query_devices(self):
        """查询设备状态"""
        if not self.tcp_controller.server_running:
            QMessageBox.warning(self, "警告", "请先启动TCP服务器")
            return

        if not self.tcp_controller.connected:
            QMessageBox.warning(self, "警告", "没有客户端连接")
            return

        self._log_message("🔍 正在查询设备状态...")
        success = self.tcp_controller.query_devices()

        if success:
            self._log_message("✅ 查询指令已发送到 192.168.0.5")
        else:
            self._log_message("❌ 查询指令发送失败")

    def _on_query_result_received(self, m1_values):
        """处理查询结果"""
        try:
            from src.config.settings import config
            from src.data_models.zfr_data import QueryDeviceData, QueryResultData

            # 从配置中读取处理参数
            base_value = config.get('query_data_processing.base_value', 72902701)
            division_factor = config.get('query_data_processing.division_factor', 10000)

            self._log_message(f"📊 查询结果接收 (原始M1值):")

            # 创建设备数据列表
            device_data_list = []

            for i, m1_value in enumerate(m1_values, 1):
                device_id = f"device{i}"

                # 进行数据处理：(base_value - m1_value) / division_factor
                processed_value = (base_value - m1_value) / division_factor

                # 创建设备数据对象
                device_data = QueryDeviceData(
                    device_id=device_id,
                    raw_m1_value=m1_value,
                    processed_value=processed_value
                )
                device_data_list.append(device_data)

                # 记录原始值和处理后的值
                self._log_message(f"   {device_id} - 原始M1: {m1_value}, 处理后: {processed_value:.6f}")

            # 创建查询结果数据对象
            query_result = QueryResultData(
                devices=device_data_list,
                base_value=base_value,
                division_factor=division_factor
            )

            # 存储查询结果供后续装配仿真使用
            self.latest_query_result = query_result

            # 显示处理后的汇总信息
            if len(device_data_list) >= 3:
                processed_values = [device.processed_value for device in device_data_list[:3]]
                self._log_message(f"🔍 处理后数据: device1={processed_values[0]:.6f}, device2={processed_values[1]:.6f}, device3={processed_values[2]:.6f}")
                self._log_message(f"📋 数据已存储，可用于装配仿真可视化")

        except Exception as e:
            self._log_message(f"❌ 处理查询结果失败: {str(e)}", "错误")

    def _open_urdf_file(self):
        """打开URDF文件进行装配仿真"""
        try:
            # 打开文件对话框选择URDF文件
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择URDF文件",
                "",
                "URDF文件 (*.urdf);;所有文件 (*.*)"
            )

            if file_path:
                self._log_message(f"📁 选择URDF文件: {file_path}")

                # {{ AURA: Modify - 使用安全的QLabel获取方式 }}
                # 更新显示标签为加载中状态
                import os
                file_name = os.path.basename(file_path)

                # 获取有效的QLabel引用
                assembly_label = self._get_valid_assembly_label()
                if assembly_label:
                    assembly_label.setText(f"正在加载URDF文件:\n{file_name}\n\n请稍候...")
                else:
                    print("⚠️ [URDF加载] 无法获取有效的装配显示标签")

                # 存储URDF文件路径
                self.current_urdf_path = file_path

                # 开始URDF解析和3D可视化
                success = self._load_and_visualize_urdf(file_path)

                if success:
                    self._log_message("✅ URDF文件解析和3D可视化成功")
                    # 获取有效的QLabel引用并更新文本
                    assembly_label = self._get_valid_assembly_label()
                    if assembly_label:
                        assembly_label.setText(f"URDF模型加载成功:\n{file_name}\n\n3D可视化已启用")
                    # 启用运动控制按钮
                    self.motion_control_btn.setEnabled(True)
                else:
                    self._log_message("❌ URDF文件解析或3D可视化失败")
                    # 获取有效的QLabel引用并更新文本
                    assembly_label = self._get_valid_assembly_label()
                    if assembly_label:
                        assembly_label.setText(f"URDF文件加载失败:\n{file_name}\n\n请检查文件格式和依赖库")
                    # 禁用运动控制按钮
                    self.motion_control_btn.setEnabled(False)

            else:
                self._log_message("❌ 未选择URDF文件")

        except Exception as e:
            self._log_message(f"❌ 打开几何模型文件失败: {str(e)}", "错误")
            QMessageBox.critical(self, "错误", f"打开几何模型文件时发生错误:\n{str(e)}")
            # 获取有效的QLabel引用并更新错误信息
            assembly_label = self._get_valid_assembly_label()
            if assembly_label:
                assembly_label.setText("URDF文件加载出错\n\n请检查文件和环境配置")
            else:
                print("⚠️ [URDF加载] 无法获取有效的装配显示标签来显示错误信息")

    def _load_and_visualize_urdf(self, urdf_path: str) -> bool:
        """
        加载和可视化URDF文件

        Args:
            urdf_path: URDF文件路径

        Returns:
            bool: 成功返回True，失败返回False
        """
        try:
            self._log_message("🔧 开始URDF解析...")

            # 创建URDF解析器 - 使用新的基于standalone版本的解析器
            from src.assembly_simulation.urdf_parser import URDFParser
            urdf_parser = URDFParser()

            # 加载URDF文件
            if not urdf_parser.load_urdf(urdf_path):
                self._log_message("❌ URDF文件解析失败")
                return False

            # 获取机器人信息
            robot_info = urdf_parser.get_robot_info()
            self._log_message(f"📊 机器人信息: {robot_info['name']}, DOF: {robot_info['dof']}, 连杆: {robot_info['num_links']}")

            # 初始化VTK 3D可视化
            success = self._initialize_vtk_visualization(urdf_parser)
            if not success:
                self._log_message("❌ VTK 3D可视化初始化失败")
                return False

            # 存储解析器供后续使用（用于运动控制）
            self.urdf_parser = urdf_parser

            # {{ AURA: Add - 关闭现有运动控制窗口以确保使用新的URDF数据 }}
            if self.motion_control_window:
                self.motion_control_window.close()
                self.motion_control_window = None
                self._log_message("🔄 已关闭旧的运动控制窗口，下次打开将使用新的URDF数据")

            return True

        except ImportError as e:
            self._log_message(f"❌ 缺少必要的依赖库: {str(e)}", "错误")
            QMessageBox.critical(self, "依赖库错误",
                               f"缺少必要的依赖库:\n{str(e)}\n\n"
                               f"请安装以下库:\n"
                               f"pip install vtk\n"
                               f"pip install PyQt5")
            return False
        except Exception as e:
            self._log_message(f"❌ URDF加载和可视化失败: {str(e)}", "错误")
            return False

    def _initialize_vtk_visualization(self, urdf_parser) -> bool:
        """
        初始化VTK 3D可视化 - 基于standalone版本的VTK逻辑

        Args:
            urdf_parser: URDF解析器实例

        Returns:
            bool: 成功返回True，失败返回False
        """
        try:
            self._log_message("🎨 初始化VTK 3D可视化...")

            # {{ AURA: Add - 在创建新VTK实例前清理现有实例 }}
            # 清理现有的VTK实例，避免多个OpenGL上下文冲突
            if hasattr(self, 'vtk_visualizer') and self.vtk_visualizer:
                print("🧹 [VTK初始化] 清理现有VTK实例...")
                try:
                    self.vtk_visualizer.cleanup()
                    self.vtk_visualizer = None
                    print("✅ [VTK初始化] 现有VTK实例已清理")
                except Exception as e:
                    print(f"⚠️ [VTK初始化] 清理现有VTK实例时出错: {e}")

            # 创建VTK可视化器
            from src.assembly_simulation.vtk_visualizer import VTKVisualizer

            # 检查VTK可用性
            vtk_visualizer = VTKVisualizer()
            if not vtk_visualizer.is_available():
                self._log_message("❌ VTK组件不可用")
                self._log_message("💡 解决方案:")
                self._log_message("   1. 确保已安装VTK: pip install vtk")
                self._log_message("   2. 或使用conda: conda install vtk")
                self._log_message("   3. 确保VTK版本>=9.0.0且<10.0.0")
                self._log_message("   4. 重新打包程序以包含VTK依赖")
                return False

            # 初始化VTK组件
            if not vtk_visualizer.initialize_vtk_widget():
                self._log_message("❌ VTK组件初始化失败")
                return False

            # 加载URDF模型
            if not vtk_visualizer.load_urdf_model(urdf_parser):
                self._log_message("❌ URDF模型加载失败")
                return False

            # 将VTK组件嵌入到显示区域
            self._embed_vtk_visualization(vtk_visualizer)

            # 存储可视化器供后续使用
            self.vtk_visualizer = vtk_visualizer

            # 设置MultiTouchCamera交互样式 - CAD风格的多点触控交互
            vtk_visualizer.set_interaction_style("MultiTouchCamera")

            self._log_message("✅ VTK 3D可视化初始化成功")
            return True

        except Exception as e:
            self._log_message(f"❌ VTK可视化初始化失败: {str(e)}", "错误")
            return False

    def _embed_vtk_visualization(self, vtk_visualizer):
        """
        将VTK 3D可视化组件嵌入到显示区域

        Args:
            vtk_visualizer: VTK可视化器实例
        """
        try:
            # 获取VTK组件
            vtk_widget = vtk_visualizer.get_vtk_widget()
            if not vtk_widget:
                self._log_message("❌ 无法获取VTK组件")
                return

            # {{ AURA: Modify - 使用动态查找替代悬空引用，提高布局切换的健壮性 }}
            # 动态查找当前有效的装配显示区域
            assembly_display_widget, assembly_layout, assembly_label = self._find_assembly_display_area()

            if not assembly_display_widget or not assembly_layout or not assembly_label:
                self._log_message("❌ 无法找到装配显示区域")
                return

            # 清空当前显示区域的内容
            self._clear_display_area_content(assembly_layout)

            # {{ AURA: Modify - 智能内容管理：只移除VTK组件，保留或创建QLabel }}
            # 查找现有的QLabel，如果没有则创建一个
            existing_label = None
            if assembly_label:
                existing_label = assembly_label
                print("✅ [VTK嵌入] 找到现有QLabel，将保留用于状态显示")
            else:
                # 创建一个新的状态QLabel
                existing_label = QLabel("装配仿真显示区域")
                existing_label.setAlignment(Qt.AlignCenter)
                existing_label.setStyleSheet("""
                    QLabel {
                        background-color: #f0f0f0;
                        color: #666;
                        padding: 20px;
                        border: 2px dashed #ccc;
                        border-radius: 8px;
                        font-size: 12px;
                    }
                """)
                assembly_layout.addWidget(existing_label)
                print("✅ [VTK嵌入] 创建新的状态QLabel")

            # {{ AURA: Add - 在VTK组件上添加可视化器引用，便于后续清理 }}
            # 在VTK组件上添加可视化器引用，便于清理时访问
            vtk_widget.visualizer = vtk_visualizer

            # 将VTK组件添加到显示区域
            vtk_widget.setParent(assembly_display_widget)
            assembly_layout.addWidget(vtk_widget)

            # 更新引用
            self.assembly_display_layout = assembly_layout
            self.assembly_display_label = existing_label



            self._log_message("✅ VTK组件已嵌入到显示区域")

        except Exception as e:
            self._log_message(f"❌ 嵌入VTK组件失败: {str(e)}", "错误")

    def _find_assembly_display_area(self):
        """
        动态查找当前有效的装配显示区域

        Returns:
            tuple: (display_widget, display_layout, label_widget) 或 (None, None, None)
        """
        try:
            print("🔍 [显示区域查找] 开始查找装配显示区域...")

            # 验证右栏组件
            if not hasattr(self, 'right_column_widget') or not self.right_column_widget:
                print("❌ [显示区域查找] 右栏组件不存在")
                return None, None, None

            # 获取右栏布局
            right_layout = self.right_column_widget.layout()
            if not right_layout:
                print("❌ [显示区域查找] 右栏布局不存在")
                return None, None, None

            # 固定使用标签页布局查找装配显示区域
            return self._find_assembly_area_in_tabs(right_layout)

        except Exception as e:
            print(f"❌ [显示区域查找] 查找装配显示区域失败: {e}")
            return None, None, None



    def _find_assembly_area_in_tabs(self, right_layout):
        """在标签布局中查找装配显示区域"""
        try:
            print("🔍 [标签查找] 在标签布局中查找装配显示区域...")

            # 遍历右栏布局中的所有项目
            for i in range(right_layout.count()):
                item = right_layout.itemAt(i)
                if not item or not item.widget():
                    continue

                # 检查是否是标签页控件
                widget = item.widget()
                if widget.__class__.__name__ != 'QTabWidget':
                    continue

                # 获取第一个标签页（装配仿真显示）
                if widget.count() > 0:
                    first_tab = widget.widget(0)
                    if first_tab:
                        return self._extract_display_area_from_group(first_tab)

            print("❌ [标签查找] 未找到装配显示区域")
            return None, None, None

        except Exception as e:
            print(f"❌ [标签查找] 标签布局查找失败: {e}")
            return None, None, None

    def _extract_display_area_from_group(self, group_box):
        """从组框中提取显示区域组件和布局"""
        try:
            print(f"🔍 [区域提取] 从组框提取显示区域: {group_box.__class__.__name__}")

            # 获取组框的布局
            group_layout = group_box.layout()
            if not group_layout:
                print("❌ [区域提取] 组框没有布局")
                return None, None, None

            # 查找显示区域widget（通常是第二个项目，第一个是按钮布局）
            for i in range(group_layout.count()):
                item = group_layout.itemAt(i)
                if not item or not item.widget():
                    continue

                widget = item.widget()
                # 查找具有特定样式的显示区域widget
                if hasattr(widget, 'styleSheet') and '#E3F2FD' in widget.styleSheet():
                    # 找到显示区域widget，获取其布局
                    display_layout = widget.layout()
                    if display_layout:
                        # {{ AURA: Add - 查找布局中的QLabel组件 }}
                        # 在显示区域布局中查找QLabel组件
                        label_widget = self._find_label_in_layout(display_layout)
                        if label_widget:
                            print(f"✅ [区域提取] 找到装配显示区域和标签: {widget.__class__.__name__}, {label_widget.__class__.__name__}")
                            return widget, display_layout, label_widget
                        else:
                            print("⚠️ [区域提取] 找到显示区域但未找到QLabel")
                            return widget, display_layout, None

            print("❌ [区域提取] 未找到显示区域widget")
            return None, None, None

        except Exception as e:
            print(f"❌ [区域提取] 提取显示区域失败: {e}")
            return None, None, None

    def _find_label_in_layout(self, layout):
        """在布局中查找QLabel组件"""
        try:
            for i in range(layout.count()):
                item = layout.itemAt(i)
                if not item:
                    continue

                widget = item.widget()
                if widget and widget.__class__.__name__ == 'QLabel':
                    print(f"✅ [标签查找] 找到QLabel: {widget.text()[:50]}...")
                    return widget

            print("❌ [标签查找] 未找到QLabel组件")
            return None

        except Exception as e:
            print(f"❌ [标签查找] 查找QLabel失败: {e}")
            return None









    def _get_valid_assembly_label(self):
        """获取有效的装配显示区域QLabel引用

        Returns:
            QLabel: 有效的QLabel对象，如果找不到则返回None
        """
        try:
            # 首先检查现有引用是否有效
            if (hasattr(self, 'assembly_display_label') and
                self.assembly_display_label is not None):
                try:
                    # 尝试访问QLabel的属性来检查是否有效
                    _ = self.assembly_display_label.text()
                    print("✅ [QLabel检查] 现有QLabel引用有效")
                    return self.assembly_display_label
                except RuntimeError:
                    print("⚠️ [QLabel检查] 现有QLabel引用已失效，重新查找...")

            # 动态查找当前有效的装配显示区域
            display_widget, display_layout, label_widget = self._find_assembly_display_area()

            if label_widget:
                print("✅ [QLabel检查] 找到有效的QLabel")
                # 更新引用
                self.assembly_display_label = label_widget
                return label_widget
            else:
                print("❌ [QLabel检查] 未找到有效的QLabel")
                return None

        except Exception as e:
            print(f"❌ [QLabel检查] 获取有效QLabel失败: {e}")
            return None

    def _clear_display_area_content(self, display_layout):
        """智能清理显示区域内容：只移除VTK组件，保留QLabel"""
        try:
            print("🧹 [显示区域清理] 开始智能清理显示区域内容...")

            # 检查布局有效性
            if not display_layout:
                print("⚠️ [显示区域清理] 布局为空，跳过清理")
                return

            # {{ AURA: Modify - 使用更安全的Qt对象有效性检查方法 }}
            # 检查Qt对象有效性
            try:
                # 尝试访问布局的count方法来检查对象是否有效
                display_layout.count()
            except RuntimeError as e:
                if "wrapped C/C++ object" in str(e) or "has been deleted" in str(e):
                    print("⚠️ [显示区域清理] 布局已被删除，跳过清理")
                    return
                raise
            except Exception as e:
                print(f"⚠️ [显示区域清理] 布局状态检查失败: {e}")
                return

            # {{ AURA: Modify - 智能清理：只移除VTK组件，保留QLabel }}
            # 智能清理：只移除VTK组件，保留QLabel等其他组件
            cleared_count = 0
            items_to_remove = []

            # 收集需要移除的VTK组件
            for i in range(display_layout.count()):
                item = display_layout.itemAt(i)
                if item and item.widget():
                    widget = item.widget()
                    # 检查是否是VTK组件
                    if (hasattr(widget, 'GetRenderWindow') or
                        'vtk' in widget.__class__.__name__.lower() or
                        'VTK' in widget.__class__.__name__):
                        items_to_remove.append((i, widget))
                        print(f"🎯 [显示区域清理] 标记VTK组件待移除: {widget.__class__.__name__}")

            # 移除VTK组件（从后往前移除以避免索引问题）
            for i, widget in reversed(items_to_remove):
                # {{ AURA: Add - 在删除VTK组件前先清理其VTK资源 }}
                # 清理VTK资源，避免OpenGL上下文错误
                try:
                    if hasattr(widget, 'visualizer') and widget.visualizer:
                        print(f"🧹 [显示区域清理] 清理VTK组件的可视化器资源: {widget.__class__.__name__}")
                        widget.visualizer.cleanup()
                        widget.visualizer = None
                    elif hasattr(self, 'vtk_visualizer') and self.vtk_visualizer:
                        # 检查是否是当前主VTK组件
                        if hasattr(self.vtk_visualizer, 'vtk_widget') and widget is self.vtk_visualizer.vtk_widget:
                            print(f"🧹 [显示区域清理] 清理主VTK可视化器资源: {widget.__class__.__name__}")
                            self.vtk_visualizer.cleanup()
                            self.vtk_visualizer = None
                except Exception as e:
                    print(f"⚠️ [显示区域清理] 清理VTK资源时出错（通常无害）: {e}")

                # 移除Qt组件
                display_layout.removeWidget(widget)
                widget.deleteLater()
                cleared_count += 1
                print(f"🗑️ [显示区域清理] 已移除VTK组件: {widget.__class__.__name__}")

            print(f"✅ [显示区域清理] 智能清理完成，移除了 {cleared_count} 个VTK组件，保留了QLabel")

        except Exception as e:
            print(f"❌ [显示区域清理] 智能清理显示区域内容失败: {e}")

    def _load_persistent_settings(self):
        """加载持久化设置"""
        try:
            # 加载Zernike存储路径
            storage_path = self.settings.value("zernike/storage_path", "")
            if storage_path and hasattr(self, 'storage_path_edit'):
                self.storage_path = storage_path
                self.storage_path_edit.setText(storage_path)
                self._log_message(f"📁 已加载存储路径: {storage_path}", "调试")

            # 加载Zernike连接IP和端口
            zernike_ip = self.settings.value("zernike/server_ip", "")
            zernike_port = self.settings.value("zernike/server_port", 8899, type=int)
            if zernike_ip and hasattr(self, 'ip_edit'):
                self.ip_edit.setText(zernike_ip)
                self._log_message(f"🌐 已加载Zernike服务器IP: {zernike_ip}", "调试")
            if hasattr(self, 'port_edit'):
                self.port_edit.setValue(zernike_port)
                self._log_message(f"🔌 已加载Zernike服务器端口: {zernike_port}", "调试")

            # 加载TCP服务器IP和端口
            tcp_ip = self.settings.value("tcp_server/ip", "")
            tcp_port = self.settings.value("tcp_server/port", 5050, type=int)
            if tcp_ip and hasattr(self, 'ip_combo'):
                # 查找并设置IP
                index = self.ip_combo.findText(tcp_ip)
                if index >= 0:
                    self.ip_combo.setCurrentIndex(index)
                    self._log_message(f"🌐 已加载TCP服务器IP: {tcp_ip}", "调试")
            if hasattr(self, 'port_spinbox'):
                self.port_spinbox.setValue(tcp_port)
                self._log_message(f"🔌 已加载TCP服务器端口: {tcp_port}", "调试")

            self._log_message("⚙️ 持久化设置加载完成", "信息")

        except Exception as e:
            self._log_message(f"❌ 加载持久化设置失败: {str(e)}", "错误")

    def _save_persistent_settings(self):
        """保存持久化设置"""
        try:
            # 保存Zernike存储路径
            if hasattr(self, 'storage_path') and self.storage_path:
                self.settings.setValue("zernike/storage_path", self.storage_path)
                self._log_message(f"💾 已保存存储路径: {self.storage_path}", "调试")

            # 保存Zernike连接IP和端口
            if hasattr(self, 'ip_edit'):
                zernike_ip = self.ip_edit.text()
                if zernike_ip:
                    self.settings.setValue("zernike/server_ip", zernike_ip)
                    self._log_message(f"💾 已保存Zernike服务器IP: {zernike_ip}", "调试")

            if hasattr(self, 'port_edit'):
                zernike_port = self.port_edit.value()
                self.settings.setValue("zernike/server_port", zernike_port)
                self._log_message(f"💾 已保存Zernike服务器端口: {zernike_port}", "调试")

            # 保存TCP服务器IP和端口
            if hasattr(self, 'ip_combo'):
                tcp_ip = self.ip_combo.currentText()
                if tcp_ip:
                    self.settings.setValue("tcp_server/ip", tcp_ip)
                    self._log_message(f"💾 已保存TCP服务器IP: {tcp_ip}", "调试")

            if hasattr(self, 'port_spinbox'):
                tcp_port = self.port_spinbox.value()
                self.settings.setValue("tcp_server/port", tcp_port)
                self._log_message(f"💾 已保存TCP服务器端口: {tcp_port}", "调试")

            # 同步设置到文件
            self.settings.sync()
            self._log_message("💾 持久化设置保存完成", "信息")

        except Exception as e:
            self._log_message(f"❌ 保存持久化设置失败: {str(e)}", "错误")

    def closeEvent(self, event):
        """窗口关闭事件 - 清理VTK资源并保存设置"""
        try:
            print("🚪 [主界面] 开始关闭程序...")
            self._log_message("👋 DigitalTwinAssemblyPlatform 正在关闭...", "信息")

            # 1. 首先清理VTK资源（避免Windows平台OpenGL上下文错误）
            if hasattr(self, 'vtk_visualizer') and self.vtk_visualizer:
                print("🧹 [主界面] 清理VTK可视化器...")
                try:
                    self.vtk_visualizer.cleanup()
                    self.vtk_visualizer = None
                    print("✅ [主界面] VTK资源清理完成")
                except Exception as e:
                    print(f"⚠️ [主界面] VTK清理过程中出现错误（通常无害）: {e}")

            # 2. 关闭关节控制窗口
            if hasattr(self, 'motion_control_window') and self.motion_control_window:
                print("🧹 [主界面] 关闭关节控制窗口...")
                try:
                    self.motion_control_window.close()
                    self.motion_control_window = None
                except Exception as e:
                    print(f"⚠️ [主界面] 关节控制窗口关闭错误: {e}")

            # 3. 保存设置
            print("💾 [主界面] 保存设置...")
            self._save_persistent_settings()

            print("✅ [主界面] 程序关闭完成")
            event.accept()

        except Exception as e:
            print(f"❌ [主界面] 关闭时出现错误: {str(e)}")
            self._log_message(f"❌ 关闭时出现错误: {str(e)}", "错误")
            # 即使出错也要接受关闭事件
            event.accept()

    def _open_motion_control(self):
        """打开运动控制窗口"""
        try:
            if not self.urdf_parser:
                QMessageBox.warning(self, "警告", "请先加载URDF文件")
                return

            # 如果窗口已存在，直接显示
            if self.motion_control_window:
                self.motion_control_window.show()
                self.motion_control_window.raise_()
                self.motion_control_window.activateWindow()
                return

            # 创建运动控制窗口
            from src.gui.joint_control_window import JointControlWindow
            self.motion_control_window = JointControlWindow(self.urdf_parser, parent=self)

            # 连接关节值变化信号
            self.motion_control_window.joint_value_changed.connect(self._on_joint_value_changed)

            # 连接相机重置请求信号
            self.motion_control_window.camera_reset_requested.connect(self._on_camera_reset_requested)

            # 显示组件
            self.motion_control_window.show()

            self._log_message("🎮 运动控制窗口已打开")

        except Exception as e:
            self._log_message(f"❌ 打开运动控制窗口失败: {str(e)}", "错误")
            QMessageBox.critical(self, "错误", f"打开运动控制窗口失败:\n{str(e)}")

    def _on_joint_value_changed(self, joint_name, joint_value):
        """关节值变化处理"""
        try:
            print(f"🎮 [主界面] 接收到关节值变化: {joint_name} = {joint_value:.3f}")

            # 更新3D模型
            self._update_robot_pose_from_joint_values()

            # 记录日志
            self._log_message(f"🔧 关节 {joint_name} 更新为: {joint_value:.3f}")

        except Exception as e:
            print(f"❌ [主界面] 更新机器人姿态失败: {str(e)}")
            self._log_message(f"❌ 更新机器人姿态失败: {str(e)}", "错误")

    def _on_camera_reset_requested(self):
        """处理相机重置请求信号"""
        try:
            print("🎥 [主界面] 接收到相机重置请求")

            # 检查VTK可视化器是否存在
            if not hasattr(self, 'vtk_visualizer') or not self.vtk_visualizer:
                self._log_message("⚠️ VTK可视化器未初始化，无法重置相机", "警告")
                print("⚠️ [主界面] VTK可视化器未初始化")
                return

            # 调用VTK可视化器的重置相机方法
            self.vtk_visualizer.reset_camera()
            self._log_message("🎥 相机视角已重置")
            print("✅ [主界面] 相机重置完成")

        except Exception as e:
            error_msg = f"相机重置失败: {str(e)}"
            print(f"❌ [主界面] {error_msg}")
            self._log_message(f"❌ {error_msg}", "错误")

    def _update_robot_pose_from_joint_values(self):
        """根据关节值更新机器人姿态"""
        print(f"🎮 [主界面] 开始更新机器人姿态")

        if not self.vtk_visualizer:
            print("❌ [主界面] VTK可视化器不可用")
            return

        if not self.motion_control_window:
            print("❌ [主界面] 运动控制窗口不可用")
            return

        try:
            # 获取当前所有关节值
            joint_values = self.motion_control_window.joint_values
            print(f"🎮 [主界面] 获取到关节值: {joint_values}")

            # 调用VTK可视化器更新机器人姿态
            self._update_vtk_robot_pose(joint_values)

        except Exception as e:
            print(f"❌ [主界面] 更新VTK机器人姿态异常: {str(e)}")
            self._log_message(f"❌ 更新VTK机器人姿态失败: {str(e)}", "错误")

    def _update_vtk_robot_pose(self, joint_values):
        """更新VTK中的机器人姿态"""
        try:
            print(f"🎮 [主界面] 调用VTK可视化器更新姿态，关节值: {joint_values}")

            # 调用VTK可视化器的更新方法
            success = self.vtk_visualizer.update_robot_pose(joint_values)

            if success:
                print("✅ [主界面] VTK机器人姿态更新成功")
                self._log_message(f"🔧 机器人姿态已更新", "调试")
            else:
                print("❌ [主界面] VTK机器人姿态更新失败")
                self._log_message("❌ 机器人姿态更新失败", "警告")

        except Exception as e:
            print(f"❌ [主界面] VTK姿态更新异常: {str(e)}")
            self._log_message(f"❌ VTK姿态更新异常: {str(e)}", "错误")
            import traceback
            traceback.print_exc()






    # ==================== 光学测量数据相关方法 ====================

    def _connect_zemax(self):
        """连接Zemax - 使用异步连接器"""
        try:
            if self.zemax_connector.is_connected():
                # 如果已连接，则断开
                self._log_message("🔗 正在断开Zemax连接...")
                self.zemax_connector.disconnect()
            else:
                # 开始异步连接
                self._log_message("🔗 正在启动Zemax连接...")
                self.zemax_connector.connect_async()

        except Exception as e:
            self._log_message(f"❌ Zemax连接操作失败: {str(e)}")
            self.zemax_status_label.setText("○连接失败")
            self.zemax_status_label.setStyleSheet("color: #dc3545;")

    def _open_zemax_file(self):
        """打开Zemax文件"""
        try:
            # 检查Zemax连接状态
            if not self.zemax_connector.is_connected():
                self._log_message("❌ 请先连接Zemax再打开文件")
                QMessageBox.warning(self, "警告", "请先连接Zemax再打开文件")
                return

            # 打开文件选择对话框
            file_path, _ = QFileDialog.getOpenFileName(
                self,
                "选择Zemax文件",
                "",
                "Zemax文件 (*.zmx);;所有文件 (*)"
            )

            if file_path:
                self._log_message(f"📁 正在加载文件: {file_path}")

                # 获取Zemax应用程序对象
                zemax_app = self.zemax_connector.get_zemax_app()
                if not zemax_app:
                    self._log_message("❌ 无法获取Zemax应用程序对象")
                    QMessageBox.critical(self, "错误", "无法获取Zemax应用程序对象")
                    return

                # 加载文件到Zemax
                try:
                    zemax_app.open_file(file_path, save_if_needed=False)

                    # 更新UI状态
                    self.zemax_file_path_edit.setText(file_path)
                    self._log_message(f"✅ 文件加载成功: {file_path}")

                    # {{ AURA: Fix - 文件加载成功后启用更新按钮 }}
                    # 启用更新按钮
                    if hasattr(self, 'update_analysis_btn'):
                        self.update_analysis_btn.setEnabled(True)
                        self._log_message("🔧 更新按钮已启用")

                    # {{ AURA: Add - 文件加载成功后更新光学参数下拉框 }}
                    # 更新光学参数下拉框（波长和视场选项）
                    self._populate_optical_parameters()
                    self._log_message("🔧 光学参数下拉框已更新")

                    # 显示成功消息
                    QMessageBox.information(self, "成功", f"文件已成功加载到Zemax:\n{file_path}\n\n点击'更新'按钮执行光学分析")

                    # 注意：根据需求，这里不自动进行光学分析
                    # 用户需要点击"更新"按钮才会进行分析

                except Exception as load_error:
                    error_msg = f"加载文件到Zemax失败: {str(load_error)}"
                    self._log_message(f"❌ {error_msg}")

                    # {{ AURA: Fix - 文件加载失败时禁用更新按钮 }}
                    # 禁用更新按钮
                    if hasattr(self, 'update_analysis_btn'):
                        self.update_analysis_btn.setEnabled(False)

                    QMessageBox.critical(self, "加载失败", error_msg)

        except Exception as e:
            error_msg = f"打开文件操作失败: {str(e)}"
            self._log_message(f"❌ {error_msg}")

            # {{ AURA: Fix - 文件打开操作失败时禁用更新按钮 }}
            # 禁用更新按钮
            if hasattr(self, 'update_analysis_btn'):
                self.update_analysis_btn.setEnabled(False)

            QMessageBox.critical(self, "错误", error_msg)

    def _browse_zemax_file(self):
        """浏览选择Zemax文件"""
        self._open_zemax_file()

    def _update_optical_analysis(self):
        """
        更新光学分析

        当用户点击"更新"按钮时，执行各种光学分析（点列图、波前图等）。
        这实现了按需分析的功能，避免文件打开时的自动分析。
        """
        try:
            # 检查Zemax连接状态
            if not self.zemax_connector.is_connected():
                self._log_message("❌ Zemax未连接，无法执行分析")
                QMessageBox.warning(self, "警告", "请先连接Zemax")
                return

            # 检查是否已加载文件
            if not self.zemax_file_path_edit.text():
                self._log_message("❌ 未加载文件，无法执行分析")
                QMessageBox.warning(self, "警告", "请先打开Zemax文件")
                return

            self._log_message("🔄 开始执行光学分析...")

            # 禁用更新按钮，防止重复点击
            self.update_analysis_btn.setEnabled(False)
            self.update_analysis_btn.setText("分析中...")

            # {{ AURA: Add - 集成SpotDiagramAnalyzer实现点列图分析 }}
            # 执行点列图分析
            self._log_message("📊 正在执行点列图分析...")
            try:
                spot_analysis_success = self._run_spot_diagram_analysis()
                if spot_analysis_success:
                    self._log_message("✅ 点列图分析完成")
                else:
                    self._log_message("⚠️ 点列图分析部分完成")
            except Exception as spot_error:
                self._log_message(f"❌ 点列图分析失败: {str(spot_error)}")

            # TODO: 其他分析功能将在后续开发中实现
            # self._log_message("📊 正在执行波前图分析...")
            # self._log_message("📊 正在执行MTF分析...")
            # self._log_message("📊 正在执行PSF分析...")

            # 恢复按钮状态
            self.update_analysis_btn.setEnabled(True)
            self.update_analysis_btn.setText("更新")

            self._log_message("✅ 光学分析完成")
            QMessageBox.information(self, "完成", "光学分析已完成！")

        except Exception as e:
            # 恢复按钮状态
            self.update_analysis_btn.setEnabled(True)
            self.update_analysis_btn.setText("更新")

            error_msg = f"光学分析失败: {str(e)}"
            self._log_message(f"❌ {error_msg}")
            QMessageBox.critical(self, "分析失败", error_msg)

    def _run_spot_diagram_analysis(self) -> bool:
        """
        执行点列图分析

        Returns:
            bool: 分析是否成功
        """
        try:
            # 获取Zemax应用程序对象
            zemax_app = self.zemax_connector.get_zemax_app()
            if not zemax_app:
                self._log_message("❌ 无法获取Zemax应用程序对象")
                return False

            # 创建点列图分析器
            spot_analyzer = SpotDiagramAnalyzer(zemax_app)

            # {{ AURA: Fix - 从UI控件获取分析参数，严格参考OpticalRealSim的参数设置 }}
            # 设置分析参数（从UI控件获取，如果存在的话）
            ray_density = 30  # 默认值，参考OpticalRealSim
            wavelength_index = 0  # 默认所有波长
            field_index = 0  # 默认所有视场
            refer_to = 1  # 默认Centroid

            # 如果UI控件存在，使用UI设置的参数
            if hasattr(self, 'ray_density_spinbox'):
                ray_density = self.ray_density_spinbox.value()
                self._log_message(f"🔧 光线密度: {ray_density}")

            if hasattr(self, 'wavelength_combo'):
                # {{ AURA: Fix - 波长参数处理：索引0="All"，索引1="1"，索引2="2"... }}
                # {{ AURA: Fix - 修复参数传递错误，确保currentIndex()不返回-1 }}
                raw_wavelength_index = self.wavelength_combo.currentIndex()
                wavelength_index = max(0, raw_wavelength_index)  # 确保不小于0，-1时使用默认值0
                wavelength_text = self.wavelength_combo.currentText()

                if raw_wavelength_index == -1:
                    self._log_message(f"⚠️ 波长组合框未选中，使用默认值: All (索引: 0)")
                    wavelength_index = 0
                else:
                    self._log_message(f"🔧 波长: {wavelength_text} (索引: {wavelength_index})")

            if hasattr(self, 'field_combo'):
                # {{ AURA: Fix - 视场编号从1开始，所以需要将索引+1 }}
                field_index = self.field_combo.currentIndex() + 1  # 视场编号从1开始
                field_text = self.field_combo.currentText()
                self._log_message(f"🔧 视场: {field_text} (编号: {field_index})")

            if hasattr(self, 'refer_to_combo'):
                refer_to = self.refer_to_combo.currentIndex()
                refer_to_text = self.refer_to_combo.currentText()
                self._log_message(f"🔧 参考类型: {refer_to_text} (索引: {refer_to})")

            spot_analyzer.set_analysis_parameters(
                ray_density=ray_density,
                wavelength_index=wavelength_index,
                field_index=field_index,
                refer_to=refer_to
            )

            # 执行分析
            self._log_message("🔄 开始执行点列图计算...")
            analysis_results = spot_analyzer.run_analysis()

            # 更新UI显示
            self._update_spot_diagram_display(analysis_results)



            # 清理资源
            spot_analyzer.cleanup()

            return True

        except Exception as e:
            self._log_message(f"❌ 点列图分析执行失败: {str(e)}")
            logger.error(f"点列图分析失败: {e}")
            return False

    def _update_spot_diagram_display(self, analysis_results: dict):
        """
        更新点列图显示（严格参考OpticalRealSim的实现）

        Args:
            analysis_results: 分析结果数据
        """
        try:
            self._log_message("📊 开始更新点列图显示...")

            # {{ AURA: Add - 实现具体的UI更新逻辑，参考OpticalRealSim }}
            # 1. 更新数据表格（RMS和GEO值）
            self._update_spot_data_table(analysis_results)

            # 2. 更新散点图显示
            self._update_spot_scatter_plot(analysis_results)

            # 3. 更新统计信息
            self._update_spot_statistics(analysis_results)

            self._log_message("✅ 点列图数据已更新到界面")

        except Exception as e:
            self._log_message(f"❌ 更新点列图显示失败: {str(e)}")
            logger.error(f"更新点列图显示失败: {e}")

    def _update_spot_data_table(self, analysis_results: dict):
        """
        更新点列图数据表格（参考OpticalRealSim的表格更新逻辑）

        Args:
            analysis_results: 分析结果数据
        """
        try:
            rms_data = analysis_results.get('rms_data', [])
            geo_data = analysis_results.get('geo_data', [])
            num_fields = analysis_results.get('num_fields', 0)

            # 查找点列图数据表格
            if hasattr(self, 'spot_data_table'):
                table = self.spot_data_table

                # 清空现有行
                table.setRowCount(0)

                # 设置表格列数和表头（参考OpticalRealSim）
                table.setColumnCount(2)
                table.setHorizontalHeaderLabels(['RMS', 'GEO'])

                # 添加数据行
                for field_idx in range(num_fields):
                    table.insertRow(field_idx)

                    # 设置行表头
                    row_header_item = QTableWidgetItem(f'Field {field_idx + 1}')
                    table.setVerticalHeaderItem(field_idx, row_header_item)

                    # RMS值
                    if field_idx < len(rms_data) and rms_data[field_idx]:
                        rms_value = rms_data[field_idx][0] if isinstance(rms_data[field_idx], list) else rms_data[field_idx]
                        rms_item = QTableWidgetItem(f"{rms_value:.3f}")
                    else:
                        rms_item = QTableWidgetItem("--")
                    table.setItem(field_idx, 0, rms_item)

                    # GEO值
                    if field_idx < len(geo_data) and geo_data[field_idx]:
                        geo_value = geo_data[field_idx][0] if isinstance(geo_data[field_idx], list) else geo_data[field_idx]
                        geo_item = QTableWidgetItem(f"{geo_value:.3f}")
                    else:
                        geo_item = QTableWidgetItem("--")
                    table.setItem(field_idx, 1, geo_item)

                self._log_message(f"📊 数据表格已更新: {num_fields}个视场")
            else:
                self._log_message("⚠️ 未找到点列图数据表格控件")

        except Exception as e:
            self._log_message(f"❌ 更新数据表格失败: {str(e)}")
            logger.error(f"更新数据表格失败: {e}")

    def _update_spot_scatter_plot(self, analysis_results: dict):
        """
        更新点列图散点图显示（参考ref/zpy_raytrace.py的多波长绘制逻辑）

        Args:
            analysis_results: 分析结果数据
        """
        try:
            scatter_data = analysis_results.get('scatter_data', {})

            # 查找点列图绘制组件
            if hasattr(self, 'spot_plot_widget'):
                plot_widget = self.spot_plot_widget

                # 获取散点数据
                x_data = scatter_data.get('x_data')
                y_data = scatter_data.get('y_data')
                wavelength_numbers = scatter_data.get('wavelength_numbers', [1])
                field_numbers = scatter_data.get('field_numbers', [1])

                if x_data is not None and y_data is not None:
                    # {{ AURA: Fix - 根据用户选择的视场和波长参数决定显示方式 }}
                    # 获取用户选择的参数
                    selected_field_index = 0  # 默认第一个视场（数组索引）
                    selected_wavelength_text = "All"  # 默认所有波长

                    # 从UI控件获取用户选择
                    if hasattr(self, 'field_combo'):
                        # {{ AURA: Fix - 修复视场索引映射问题 }}
                        # field_combo的选项是"1", "2", "3"...，currentIndex()返回0,1,2...
                        # 但数据数组索引也是0,1,2...，所以直接使用currentIndex()即可
                        selected_field_index = self.field_combo.currentIndex()  # 视场索引（从0开始）
                        selected_field_text = self.field_combo.currentText()
                        self._log_message(f"[DEBUG] UI选择: 视场{selected_field_text}, 数组索引: {selected_field_index}")

                    if hasattr(self, 'wavelength_combo'):
                        selected_wavelength_text = self.wavelength_combo.currentText()

                    # 获取数据维度
                    if len(x_data.shape) >= 3 and len(y_data.shape) >= 3:
                        if selected_wavelength_text == "All":
                            # {{ AURA: Fix - 多波长显示：使用新的多波长绘制方法 }}
                            # 显示选定视场的所有波长
                            plot_widget.plot_multi_wavelength_data(
                                scatter_data,
                                selected_field_index,
                                wavelength_numbers
                            )
                            self._log_message(f"📈 多波长散点图已更新: 视场{selected_field_index + 1}, {len(wavelength_numbers)}个波长")
                        else:
                            # {{ AURA: Fix - 单波长显示：使用传统的单色绘制方法 }}
                            # 显示选定视场的单个波长
                            try:
                                # {{ AURA: Debug - 添加单波长显示的详细调试输出 }}
                                print(f"[DEBUG] 单波长显示模式")
                                print(f"[DEBUG] 用户选择波长: '{selected_wavelength_text}'")
                                print(f"[DEBUG] 可用波长编号: {wavelength_numbers}")

                                wave_index = int(selected_wavelength_text) - 1  # 波长索引（从0开始）
                                print(f"[DEBUG] 计算的波长索引: {wave_index}")

                                # {{ AURA: Fix - 修正索引范围检查，使用实际数据数组维度而不是wavelength_numbers长度 }}
                                actual_wavelength_count = x_data.shape[1]  # 数据数组的实际波长维度
                                print(f"[DEBUG] 数据数组波长维度: {actual_wavelength_count}")
                                print(f"[DEBUG] 索引范围检查: 0 <= {wave_index} < {actual_wavelength_count} = {0 <= wave_index < actual_wavelength_count}")

                                if 0 <= wave_index < actual_wavelength_count:
                                    print(f"[DEBUG] 访问数据: x_data[{selected_field_index}, {wave_index}, :]")
                                    x_plot_data = np.squeeze(x_data[selected_field_index, wave_index, :])
                                    y_plot_data = np.squeeze(y_data[selected_field_index, wave_index, :])

                                    # {{ AURA: Debug - 显示原始数据信息 }}
                                    print(f"[DEBUG] 原始数据形状: x={x_plot_data.shape}, y={y_plot_data.shape}")
                                    print(f"[DEBUG] 原始数据范围: X=[{np.min(x_plot_data):.6f}, {np.max(x_plot_data):.6f}], Y=[{np.min(y_plot_data):.6f}, {np.max(y_plot_data):.6f}]")
                                    print(f"[DEBUG] 原始数据点数: {len(x_plot_data)}")

                                    # 过滤有效数据点（非零值）
                                    valid_mask = (x_plot_data != 0) | (y_plot_data != 0)
                                    x_plot_data = x_plot_data[valid_mask]
                                    y_plot_data = y_plot_data[valid_mask]

                                    # {{ AURA: Debug - 显示过滤后数据信息 }}
                                    print(f"[DEBUG] 过滤后有效点数: {len(x_plot_data)}")
                                    if len(x_plot_data) > 0:
                                        print(f"[DEBUG] 有效数据范围: X=[{np.min(x_plot_data):.6f}, {np.max(x_plot_data):.6f}], Y=[{np.min(y_plot_data):.6f}, {np.max(y_plot_data):.6f}]")

                                    # 初始化散点图并绘制
                                    print(f"[DEBUG] 调用 plot_widget.init_scatter()")
                                    plot_widget.init_scatter()

                                    if len(x_plot_data) > 0:
                                        print(f"[DEBUG] 调用 plot_widget.plot_scatter_data() 绘制 {len(x_plot_data)} 个点")
                                        plot_widget.plot_scatter_data(
                                            x_plot_data, y_plot_data,
                                            color='b',
                                            label=f"波长 {selected_wavelength_text}"
                                        )
                                        plot_widget.ax.set_title(f'Spot Diagram - Field {selected_field_index + 1}, Wavelength {selected_wavelength_text}')
                                        print(f"[DEBUG] 单波长散点图绘制完成")
                                    else:
                                        print(f"[WARNING] 没有有效数据点可绘制")

                                    self._log_message(f"📈 单波长散点图已更新: 视场{selected_field_index + 1}, 波长{selected_wavelength_text}, {len(x_plot_data)}个数据点")
                                else:
                                    print(f"[WARNING] 波长索引超出范围: {wave_index}")
                                    self._log_message(f"⚠️ 波长索引超出范围: {wave_index}")
                            except ValueError:
                                print(f"[WARNING] 无效的波长选择: {selected_wavelength_text}")
                                self._log_message(f"⚠️ 无效的波长选择: {selected_wavelength_text}")
                    else:
                        self._log_message("⚠️ 散点数据维度不正确")
                else:
                    self._log_message("⚠️ 未找到有效的散点数据")
            else:
                self._log_message("⚠️ 未找到点列图绘制组件")

        except Exception as e:
            self._log_message(f"❌ 更新散点图失败: {str(e)}")
            logger.error(f"更新散点图失败: {e}")

    def _update_spot_statistics(self, analysis_results: dict):
        """
        更新点列图统计信息

        Args:
            analysis_results: 分析结果数据
        """
        try:
            rms_data = analysis_results.get('rms_data', [])
            geo_data = analysis_results.get('geo_data', [])
            num_fields = analysis_results.get('num_fields', 0)

            # 计算统计信息
            if rms_data and len(rms_data) > 0:
                # 计算平均RMS
                valid_rms = []
                for field_rms in rms_data:
                    if isinstance(field_rms, list) and len(field_rms) > 0:
                        valid_rms.extend(field_rms)
                    elif isinstance(field_rms, (int, float)):
                        valid_rms.append(field_rms)

                if valid_rms:
                    avg_rms = np.mean(valid_rms)
                    max_rms = np.max(valid_rms)
                    min_rms = np.min(valid_rms)

                    self._log_message(f"📊 RMS统计: 平均={avg_rms:.6f}, 最大={max_rms:.6f}, 最小={min_rms:.6f}")

            # {{ AURA: Delete - 删除数据信息标签更新代码，因为相关控件已被移除 }}

        except Exception as e:
            self._log_message(f"❌ 更新统计信息失败: {str(e)}")
            logger.error(f"更新统计信息失败: {e}")

    def _update_spot_analysis(self):
        """更新点列图分析（从Spot Diagram标签页触发）"""
        try:
            self._log_message("🔄 从Spot Diagram标签页触发分析更新...")
            success = self._run_spot_diagram_analysis()
            if success:
                self._log_message("✅ Spot Diagram分析更新完成")
            else:
                self._log_message("❌ Spot Diagram分析更新失败")
        except Exception as e:
            self._log_message(f"❌ Spot Diagram分析更新出错: {str(e)}")

    def _export_spot_data(self):
        """导出点列图数据"""
        try:
            self._log_message("📤 导出点列图数据功能暂未实现")
            QMessageBox.information(self, "提示", "导出点列图数据功能将在后续版本中实现")
        except Exception as e:
            self._log_message(f"❌ 导出点列图数据出错: {str(e)}")

    def _populate_optical_parameters(self):
        """填充光学参数下拉框（从Zemax系统中读取实际参数）"""
        try:
            # {{ AURA: Fix - 从Zemax系统中读取实际的波长和视场参数 }}
            zemax_app = self.zemax_connector.get_zemax_app()
            if not zemax_app:
                self._log_message("⚠️ Zemax未连接，使用默认参数")
                # 使用默认参数
                self._populate_default_parameters()
                return

            # 获取Zemax系统对象
            TheSystem = zemax_app.TheSystem

            # {{ AURA: Fix - 清空所有相关下拉框的现有选项，避免重复 }}
            # 清空点列图下拉框
            self.wavelength_combo.clear()
            self.field_combo.clear()

            # 清空波前图下拉框
            if hasattr(self, 'wavefront_wavelength_combo'):
                self.wavefront_wavelength_combo.clear()
            if hasattr(self, 'wavefront_field_combo'):
                self.wavefront_field_combo.clear()

            # 重新添加"All"选项
            self.wavelength_combo.addItem('All')

            # 获取波长数量并填充
            num_wavelengths = TheSystem.SystemData.Wavelengths.NumberOfWavelengths
            for i in range(1, num_wavelengths + 1):
                self.wavelength_combo.addItem(f"{i}")
                if hasattr(self, 'wavefront_wavelength_combo'):
                    self.wavefront_wavelength_combo.addItem(f"{i}")

            # 获取视场数量并填充
            num_fields = TheSystem.SystemData.Fields.NumberOfFields
            for i in range(1, num_fields + 1):
                self.field_combo.addItem(f"{i}")
                if hasattr(self, 'wavefront_field_combo'):
                    self.wavefront_field_combo.addItem(f"{i}")

            self._log_message(f"✅ 光学参数已更新: {num_wavelengths}个波长, {num_fields}个视场")

            # {{ AURA: Add - 参数更新完成后触发波前图初始分析 }}
            # 触发波前图的初始更新（因为addItem不会触发信号槽）
            if hasattr(self, 'wavefront_wavelength_combo') and hasattr(self, 'wavefront_field_combo'):
                self._log_message("🔄 触发波前图初始分析...")
                self._update_wavefront_map()

        except Exception as e:
            self._log_message(f"❌ 参数更新失败: {str(e)}")
            # 出错时使用默认参数
            self._populate_default_parameters()

    def _populate_default_parameters(self):
        """填充默认光学参数"""
        try:
            # {{ AURA: Fix - 清空所有相关下拉框，避免重复 }}
            # 清空并填充点列图下拉框
            if hasattr(self, 'wavelength_combo'):
                self.wavelength_combo.clear()
                self.wavelength_combo.addItem('All')
                # 添加默认波长选项
                for i in range(1, 4):  # 默认3个波长
                    self.wavelength_combo.addItem(f"{i}")

            if hasattr(self, 'field_combo'):
                self.field_combo.clear()
                # 添加默认视场选项
                for i in range(1, 6):  # 默认5个视场
                    self.field_combo.addItem(f"{i}")

            # 清空并填充波前图下拉框
            if hasattr(self, 'wavefront_wavelength_combo'):
                self.wavefront_wavelength_combo.clear()
                # 添加默认波长选项
                for i in range(1, 4):  # 默认3个波长
                    self.wavefront_wavelength_combo.addItem(f"{i}")

            if hasattr(self, 'wavefront_field_combo'):
                self.wavefront_field_combo.clear()
                # 添加默认视场选项
                for i in range(1, 6):  # 默认5个视场
                    self.wavefront_field_combo.addItem(f"{i}")

            self._log_message("✅ 已加载默认光学参数")

        except Exception as e:
            self._log_message(f"❌ 加载默认参数失败: {str(e)}")



    def _export_spot_data(self):
        """导出Spot Diagram数据"""
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出Spot Diagram数据",
                "spot_diagram_data.csv",
                "CSV文件 (*.csv);;所有文件 (*)"
            )

            if file_path:
                # TODO: 实现实际的数据导出逻辑
                self._log_message(f"📤 数据已导出到: {file_path}")

        except Exception as e:
            self._log_message(f"❌ 数据导出失败: {str(e)}")

    # {{ AURA: Delete - 删除_load_optical_data方法，因为对应的按钮已被移除 }}

    # ==================== Zemax连接器信号处理方法 ====================

    def _on_zemax_status_changed(self, is_connected, message):
        """处理Zemax连接状态变化"""
        try:
            if is_connected:
                # {{ AURA: Add - Zemax连接成功后填充光学参数 }}
                # 连接成功
                self.zemax_status_label.setText("●已连接")
                self.zemax_status_label.setStyleSheet("color: #28a745;")
                self.connect_zemax_btn.setText("断开连接")
                self.open_zemax_file_btn.setEnabled(True)

                # 启用连接相关的UI组件
                self.connect_zemax_btn.setEnabled(True)

                # 填充光学参数下拉框
                self._populate_optical_parameters()

                # {{ AURA: Add - 初始化光学分析器 }}
                # Zemax连接成功后初始化光学分析器
                zemax_app = self.zemax_connector.get_zemax_app()
                if zemax_app:
                    self.spot_analyzer = SpotDiagramAnalyzer(zemax_app)
                    self.wavefront_analyzer.set_zemax_application(zemax_app)
                    logger.info("光学分析器初始化完成")

            else:
                # 连接断开或失败
                self.zemax_status_label.setText("○未连接")
                self.zemax_status_label.setStyleSheet("color: #666;")
                self.connect_zemax_btn.setText("连接Zemax")
                self.open_zemax_file_btn.setEnabled(False)

                # 清空文件路径
                self.zemax_file_path_edit.setText("")

                # 启用连接按钮
                self.connect_zemax_btn.setEnabled(True)

            # 记录状态变化消息
            self._log_message(message)

        except Exception as e:
            logger.error(f"处理Zemax状态变化时出错: {e}")
            self._log_message(f"❌ 状态更新失败: {str(e)}")

    def _on_zemax_progress(self, progress_text):
        """处理Zemax连接进度更新"""
        try:
            self._log_message(f"🔄 {progress_text}")
        except Exception as e:
            logger.error(f"处理Zemax进度更新时出错: {e}")

    def _on_zemax_connection_started(self):
        """处理Zemax连接开始"""
        try:
            # 禁用连接按钮，防止重复点击
            self.connect_zemax_btn.setEnabled(False)
            self.connect_zemax_btn.setText("连接中...")

            # 更新状态显示
            self.zemax_status_label.setText("○连接中...")
            self.zemax_status_label.setStyleSheet("color: #ffc107;")

        except Exception as e:
            logger.error(f"处理Zemax连接开始时出错: {e}")

    def _on_zemax_connection_finished(self):
        """处理Zemax连接结束"""
        try:
            # 重新启用连接按钮
            self.connect_zemax_btn.setEnabled(True)

            # 根据连接状态更新按钮文本
            if self.zemax_connector.is_connected():
                self.connect_zemax_btn.setText("断开连接")
            else:
                self.connect_zemax_btn.setText("连接Zemax")

        except Exception as e:
            logger.error(f"处理Zemax连接结束时出错: {e}")

    def _sync_zemax_ui_state(self):
        """
        同步ZemaxConnector的当前状态到UI组件

        在UI组件重新创建后调用此方法，确保UI状态与ZemaxConnector的实际状态保持一致。
        这解决了布局切换时UI状态不一致的问题。
        """
        try:
            # 检查必要的UI组件是否存在
            if not hasattr(self, 'zemax_connector') or not self.zemax_connector:
                logger.warning("ZemaxConnector未初始化，跳过状态同步")
                return

            if not hasattr(self, 'zemax_status_label') or not hasattr(self, 'connect_zemax_btn'):
                logger.warning("Zemax UI组件未创建，跳过状态同步")
                return

            # 获取当前连接状态
            is_connected = self.zemax_connector.is_connected()
            logger.info(f"🔄 [状态同步] 同步Zemax UI状态，当前连接状态: {is_connected}")

            if is_connected:
                # 连接状态：更新UI为已连接状态
                self.zemax_status_label.setText("●已连接")
                self.zemax_status_label.setStyleSheet("color: #28a745;")
                self.connect_zemax_btn.setText("断开连接")

                # 启用相关按钮
                if hasattr(self, 'open_zemax_file_btn'):
                    self.open_zemax_file_btn.setEnabled(True)
                self.connect_zemax_btn.setEnabled(True)

                # {{ AURA: Add - 根据文件加载状态决定更新按钮的启用状态 }}
                # 更新按钮的状态取决于是否已加载文件
                if hasattr(self, 'update_analysis_btn'):
                    file_loaded = hasattr(self, 'zemax_file_path_edit') and bool(self.zemax_file_path_edit.text().strip())
                    self.update_analysis_btn.setEnabled(file_loaded)

                logger.info("✅ [状态同步] UI已更新为连接状态")
            else:
                # 未连接状态：更新UI为未连接状态
                self.zemax_status_label.setText("○未连接")
                self.zemax_status_label.setStyleSheet("color: #666;")
                self.connect_zemax_btn.setText("连接Zemax")

                # 禁用相关按钮和清空文件路径
                if hasattr(self, 'open_zemax_file_btn'):
                    self.open_zemax_file_btn.setEnabled(False)
                if hasattr(self, 'zemax_file_path_edit'):
                    self.zemax_file_path_edit.setText("")
                # {{ AURA: Add - 未连接时禁用更新按钮 }}
                if hasattr(self, 'update_analysis_btn'):
                    self.update_analysis_btn.setEnabled(False)
                self.connect_zemax_btn.setEnabled(True)

                logger.info("✅ [状态同步] UI已更新为未连接状态")

        except Exception as e:
            logger.error(f"❌ [状态同步] 同步Zemax UI状态失败: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    print("🚀 启动25AutoAssembly运动控制界面")

    app = QApplication(sys.argv)
    app.setStyle('Fusion')

    try:
        window = MotionControlMainWindow()
        window.show()

        print("✅ 运动控制界面已启动")

        sys.exit(app.exec_())

    except Exception as e:
        print(f"❌ 界面启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
