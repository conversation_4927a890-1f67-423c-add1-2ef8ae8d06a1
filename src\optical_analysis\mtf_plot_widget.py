#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MTF绘图控件模块

基于ref/zpy_fftmtf.py的MTF绘图功能，适配现有项目架构。
提供完整的MTF图表显示功能，包括：
- MTF曲线绘制
- 控制面板（Sampling、Maximum Frequency、Wavelength、Field）
- 多视场MTF比较分析
- 切向和弧矢向MTF曲线显示

主要类：
    - MtfPlotCanvas: MTF绘图画布
    - MtfPlotWidget: MTF绘图控件（包含控制面板和绘图区域）

作者: AI Assistant
版本: 1.0.0
兼容性: Python 3.6+, PyQt5
"""

import logging
import numpy as np
from typing import Dict, List, Tuple, Union, Any, Optional

from PyQt5.QtWidgets import QWidget, QVBoxLayout
from PyQt5.QtCore import Qt

# 条件导入matplotlib，避免启动时错误
try:
    import matplotlib
    matplotlib.use("Qt5Agg")
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
    from matplotlib.figure import Figure
    import matplotlib.pyplot as plt

    # 设置中文字体，解决中文乱码问题
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为 SimHei
    plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示问题

    MATPLOTLIB_AVAILABLE = True
except ImportError:
    # 如果matplotlib不可用，定义占位符
    FigureCanvas = None
    NavigationToolbar = None
    Figure = None
    MATPLOTLIB_AVAILABLE = False

# 配置日志
logger = logging.getLogger(__name__)


class MtfPlotCanvas(FigureCanvas if MATPLOTLIB_AVAILABLE else object):
    """MTF绘图画布类，继承自FigureCanvasQTAgg"""
    
    def __init__(self, parent=None, width=10, height=6, dpi=100):
        """
        初始化MTF绘图画布。
        
        Args:
            parent: 父控件
            width (float): 图形宽度（英寸）
            height (float): 图形高度（英寸）
            dpi (int): 分辨率
        """
        if not MATPLOTLIB_AVAILABLE:
            raise ImportError("matplotlib不可用，请安装matplotlib和PyQt5")
            
        # 创建matplotlib图形对象
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        super().__init__(self.fig)
        self.setParent(parent)
        
        # 创建子图
        self.axes = self.fig.add_subplot(111)
        
        # 设置中文字体
        matplotlib.rcParams['font.sans-serif'] = ['SimHei']
        matplotlib.rcParams['axes.unicode_minus'] = False
    
    def plot_mtf_series(self, x_data, y_data, series_labels=None, title="FFT MTF曲线", 
                       x_label="频率 (cycles/mm)", y_label="MTF"):
        """
        绘制MTF系列数据。
        
        Args:
            x_data: X轴数据（频率）
            y_data: Y轴数据（MTF值）
            series_labels: 系列标签列表
            title: 图表标题
            x_label: X轴标签
            y_label: Y轴标签
        """
        # 清除之前的绘图
        self.axes.clear()
        
        # 绘制每个系列
        if y_data.ndim == 2:
            for i in range(y_data.shape[1]):
                label = series_labels[i] if series_labels and i < len(series_labels) else f"系列 {i+1}"
                self.axes.plot(x_data, y_data[:, i], label=label)
        else:
            self.axes.plot(x_data, y_data, label="MTF")
        
        # 设置标题和标签
        self.axes.set_title(title)
        self.axes.set_xlabel(x_label)
        self.axes.set_ylabel(y_label)
        self.axes.legend()
        self.axes.grid(True)
        self.axes.set_ylim(0, 1.05)
        
        # 刷新画布
        self.draw()
    
    def plot_mtf_comparison(self, mtf_data, series_indices=None, direction="切向", 
                           title="不同视场MTF曲线比较"):
        """
        绘制MTF比较图。
        
        Args:
            mtf_data: MTF数据字典
            series_indices: 要绘制的系列索引列表
            direction: 绘制方向（"切向"或"弧矢向"）
            title: 图表标题
        """
        # 清除之前的绘图
        self.axes.clear()
        
        if series_indices is None:
            series_indices = list(range(len(mtf_data["数据系列"])))
        
        # 确定方向索引
        dir_idx = 0 if direction == "切向" else 1
        
        # 绘制每个系列
        for idx in series_indices:
            series = mtf_data["数据系列"][idx]
            
            if "X数据" not in series or "Y数据" not in series:
                continue
            
            x_data = series["X数据"]
            y_data = np.array(series["Y数据"])
            
            if y_data.shape[0] == len(x_data) and y_data.shape[1] > dir_idx:
                y_values = y_data[:, dir_idx]
            else:
                continue
            
            # 获取标签
            if "系列标签" in series and len(series["系列标签"]) > dir_idx:
                label = series["系列标签"][dir_idx]
                if "描述" in series:
                    label = f"{series['描述']} - {label}"
            else:
                label = f"系列 {idx+1} - {direction}"
            
            self.axes.plot(x_data, y_values, label=label)
        
        # 设置图表属性
        self.axes.set_title(title)
        self.axes.set_xlabel("频率 (cycles/mm)")
        self.axes.set_ylabel("MTF")
        self.axes.legend()
        self.axes.grid(True)
        self.axes.set_ylim(0, 1.05)
        
        # 刷新画布
        self.draw()


class MtfPlotWidget(QWidget):
    """MTF绘图控件，纯绘图功能，不包含控制面板"""
    
    def __init__(self, parent=None):
        """
        初始化MTF绘图控件。
        
        Args:
            parent: 父控件
        """
        super().__init__(parent)
        
        if not MATPLOTLIB_AVAILABLE:
            logger.error("matplotlib不可用，MTF绘图功能将不可用")
            return
            
        self.init_ui()
        self.mtf_data = None
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建主布局
        main_layout = QVBoxLayout()

        # 创建绘图区域（移除控制面板，只保留绘图功能）
        plot_area = self.create_plot_area()
        main_layout.addWidget(plot_area)

        self.setLayout(main_layout)
    

    
    def create_plot_area(self) -> QWidget:
        """创建绘图区域"""
        plot_widget = QWidget()
        layout = QVBoxLayout()
        
        # 创建绘图画布
        self.canvas = MtfPlotCanvas(self, width=10, height=6, dpi=100)
        
        # 创建导航工具栏
        self.toolbar = NavigationToolbar(self.canvas, self)
        
        # 添加到布局
        layout.addWidget(self.toolbar)
        layout.addWidget(self.canvas)
        
        plot_widget.setLayout(layout)
        return plot_widget
    


    def plot_mtf_data(self, mtf_data, series_index=0):
        """
        绘制指定系列的MTF数据。

        Args:
            mtf_data: MTF数据字典
            series_index: 系列索引
        """
        if not MATPLOTLIB_AVAILABLE:
            logger.error("matplotlib不可用，无法绘制MTF数据")
            return

        if series_index >= len(mtf_data["数据系列"]):
            logger.warning(f"系列索引{series_index}超出范围")
            return

        self.mtf_data = mtf_data
        series = mtf_data["数据系列"][series_index]
        x_data = series["X数据"]
        y_data = np.array(series["Y数据"])
        series_labels = series.get("系列标签", None)
        title = f"FFT MTF曲线 - {series.get('描述', f'系列 {series_index+1}')}"

        self.canvas.plot_mtf_series(x_data, y_data, series_labels, title)

    def plot_mtf_comparison(self, mtf_data, direction="切向"):
        """
        绘制MTF比较图。

        Args:
            mtf_data: MTF数据字典
            direction: 绘制方向
        """
        if not MATPLOTLIB_AVAILABLE:
            logger.error("matplotlib不可用，无法绘制MTF比较图")
            return

        self.mtf_data = mtf_data
        self.canvas.plot_mtf_comparison(mtf_data, direction=direction)

    def clear_plot(self):
        """清除绘图"""
        if MATPLOTLIB_AVAILABLE and hasattr(self, 'canvas'):
            self.canvas.axes.clear()
            self.canvas.draw()

    def set_analysis_parameters(self, params: dict):
        """
        设置分析参数到控制面板

        Args:
            params: 参数字典
        """
        if 'sample_size' in params:
            sample_size = params['sample_size']
            sampling_text = f"{sample_size} x {sample_size}"
            index = self.sampling_combo.findText(sampling_text)
            if index >= 0:
                self.sampling_combo.setCurrentIndex(index)

        if 'maximum_frequency' in params:
            self.max_freq_edit.setText(str(params['maximum_frequency']))

        if 'wavelength_number' in params:
            wavelength_number = params['wavelength_number']
            wavelength_text = "All" if wavelength_number == 0 else str(wavelength_number)
            index = self.wavelength_combo.findText(wavelength_text)
            if index >= 0:
                self.wavelength_combo.setCurrentIndex(index)

        if 'field_number' in params:
            field_number = params['field_number']
            field_text = "All" if field_number == 0 else str(field_number)
            index = self.field_combo.findText(field_text)
            if index >= 0:
                self.field_combo.setCurrentIndex(index)

    def get_current_mtf_data(self):
        """获取当前MTF数据"""
        return self.mtf_data

    def set_enabled(self, enabled: bool):
        """设置控件启用状态"""
        self.sampling_combo.setEnabled(enabled)
        self.max_freq_edit.setEnabled(enabled)
        self.wavelength_combo.setEnabled(enabled)
        self.field_combo.setEnabled(enabled)
        self.analyze_button.setEnabled(enabled)

        # 更新按钮文本
        if enabled:
            self.analyze_button.setText("运行MTF分析")
        else:
            self.analyze_button.setText("分析中...")

    def validate_parameters(self):
        """
        验证分析参数的有效性

        Returns:
            tuple: (是否有效, 错误信息)
        """
        # 验证最大频率
        try:
            max_freq = float(self.max_freq_edit.text())
            if max_freq <= 0:
                return False, "最大频率必须大于0"
            if max_freq > 1000:
                return False, "最大频率不能超过1000 cycles/mm"
        except ValueError:
            return False, "最大频率必须是有效的数字"

        # 验证采样大小
        sampling_text = self.sampling_combo.currentText()
        if not sampling_text or 'x' not in sampling_text:
            return False, "采样大小格式无效"

        return True, ""

    def reset_to_defaults(self):
        """重置控件到默认值"""
        self.sampling_combo.setCurrentText("64 x 64")
        self.max_freq_edit.setText("60")
        self.wavelength_combo.setCurrentText("All")
        self.field_combo.setCurrentText("All")

        logger.info("MTF参数已重置为默认值")

    def update_wavelength_options(self, wavelength_count: int):
        """
        根据光学系统更新波长选项

        Args:
            wavelength_count: 波长数量
        """
        self.wavelength_combo.clear()
        self.wavelength_combo.addItem("All")

        for i in range(1, wavelength_count + 1):
            self.wavelength_combo.addItem(str(i))

        logger.info(f"波长选项已更新，共{wavelength_count}个波长")

    def update_field_options(self, field_count: int):
        """
        根据光学系统更新视场选项

        Args:
            field_count: 视场数量
        """
        self.field_combo.clear()
        self.field_combo.addItem("All")

        for i in range(1, field_count + 1):
            self.field_combo.addItem(str(i))

        logger.info(f"视场选项已更新，共{field_count}个视场")
