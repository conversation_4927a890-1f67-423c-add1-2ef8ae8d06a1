#!/usr/bin/env python3
"""
TCP文件传输服务器
用于局域网文件传输的简单GUI服务器程序
"""

import sys
import os
import socket
import threading
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QLineEdit, QPushButton,
                             QComboBox, QFileDialog, QTextEdit, QGroupBox,
                             QMessageBox, QProgressBar)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap
from PIL import Image, ImageQt
import json

# 添加项目根目录到路径，以便导入配置模块
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.file_transfer_config import file_transfer_config


class FileTransferServer(QMainWindow):
    """文件传输服务器主窗口"""
    
    def __init__(self):
        super().__init__()
        self.server_socket = None
        self.client_socket = None
        self.is_running = False
        self.selected_file_path = ""
        
        # 图片传输相关属性
        self.selected_image_path = ""
        self.image_preview_label = None
        self.image_info_labels = {}
        self.image_progress_bar = None

        # 加载服务器配置
        self.server_config = file_transfer_config.get_server_config()

        self.init_ui()
        self.setup_style()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("ZernikeServer")
        self.setMinimumSize(700, 650)  # 增加最小尺寸
        self.resize(800, 700)  # 增加默认尺寸
        
        # 居中显示
        self.center_window()
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(12)  # 减少间距
        main_layout.setContentsMargins(15, 15, 15, 15)  # 减少边距
        
        # 网络配置区域
        self.create_network_config(main_layout)
        
        # 文件选择区域
        self.create_file_selection(main_layout)
        
        # 图片传输区域
        self.create_image_transfer(main_layout)
        
        # 传输控制区域
        self.create_transfer_control(main_layout)
        
        # 日志显示区域
        self.create_log_area(main_layout)
        
    def center_window(self):
        """窗口居中"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move(
            (screen.width() - size.width()) // 2,
            (screen.height() - size.height()) // 2
        )
        
    def create_network_config(self, parent_layout):
        """创建网络配置区域"""
        group = QGroupBox("网络配置")
        group.setMinimumHeight(120)  # 设置最小高度
        layout = QVBoxLayout(group)
        
        # IP配置
        ip_layout = QHBoxLayout()
        ip_layout.addWidget(QLabel("服务器IP:"))
        
        self.ip_combo = QComboBox()
        self.ip_combo.setEditable(True)
        self.ip_combo.setMinimumHeight(30)  # 设置下拉框最小高度
        self.ip_combo.addItems(self.get_local_ips())
        # 设置保存的IP值
        self.ip_combo.setCurrentText(self.server_config.get("server_ip", "127.0.0.1"))
        ip_layout.addWidget(self.ip_combo)

        layout.addLayout(ip_layout)

        # 端口配置
        port_layout = QHBoxLayout()
        port_layout.addWidget(QLabel("端口:"))

        self.port_edit = QLineEdit()
        self.port_edit.setMinimumHeight(30)  # 设置输入框最小高度
        # 设置保存的端口值
        self.port_edit.setText(self.server_config.get("server_port", "8888"))
        self.port_edit.setMaximumWidth(100)
        port_layout.addWidget(self.port_edit)
        port_layout.addStretch()
        
        layout.addLayout(port_layout)
        
        # 服务器控制按钮
        button_layout = QHBoxLayout()
        self.start_btn = QPushButton("启动服务器")
        self.start_btn.setMinimumHeight(35)  # 设置按钮最小高度
        self.start_btn.clicked.connect(self.start_server)

        self.stop_btn = QPushButton("停止服务器")
        self.stop_btn.setMinimumHeight(35)  # 设置按钮最小高度
        self.stop_btn.clicked.connect(self.stop_server)
        self.stop_btn.setEnabled(False)

        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)
        button_layout.addStretch()

        layout.addLayout(button_layout)
        
        parent_layout.addWidget(group)
        
    def create_file_selection(self, parent_layout):
        """创建文件选择区域"""
        group = QGroupBox("文件选择")
        group.setMinimumHeight(80)  # 设置最小高度
        layout = QVBoxLayout(group)
        
        # 文件路径显示
        path_layout = QHBoxLayout()
        path_layout.addWidget(QLabel("选择文件:"))
        
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setReadOnly(True)
        self.file_path_edit.setMinimumHeight(30)  # 设置输入框最小高度
        self.file_path_edit.setPlaceholderText("请选择要传输的文件...")
        # 设置保存的文件路径
        saved_file_path = self.server_config.get("file_path", "")
        if saved_file_path and os.path.exists(saved_file_path):
            self.file_path_edit.setText(saved_file_path)
            self.selected_file_path = saved_file_path
        path_layout.addWidget(self.file_path_edit)

        self.browse_btn = QPushButton("浏览...")
        self.browse_btn.setMinimumHeight(30)  # 设置按钮最小高度
        self.browse_btn.clicked.connect(self.browse_file)
        path_layout.addWidget(self.browse_btn)
        
        layout.addLayout(path_layout)
        
        parent_layout.addWidget(group)
        
    def create_image_transfer(self, parent_layout):
        """创建图片传输区域"""
        group = QGroupBox("图片传输")
        group.setMinimumHeight(200)  # 设置最小高度
        main_layout = QHBoxLayout(group)
        
        # 左侧区域：图片选择和信息
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 10, 0)
        
        # 图片选择按钮
        self.browse_image_btn = QPushButton("选择图片...")
        self.browse_image_btn.setMinimumHeight(35)
        self.browse_image_btn.clicked.connect(self.browse_image)
        left_layout.addWidget(self.browse_image_btn)
        
        # 图片信息显示
        info_widget = QWidget()
        info_layout = QVBoxLayout(info_widget)
        info_layout.setContentsMargins(0, 10, 0, 0)
        
        # 文件名
        filename_layout = QHBoxLayout()
        filename_layout.addWidget(QLabel("📄 文件名:"))
        self.image_info_labels['filename'] = QLabel("未选择")
        self.image_info_labels['filename'].setStyleSheet("color: #666; margin-left: 5px;")
        filename_layout.addWidget(self.image_info_labels['filename'])
        filename_layout.addStretch()
        info_layout.addLayout(filename_layout)
        
        # 尺寸
        size_layout = QHBoxLayout()
        size_layout.addWidget(QLabel("📏 尺寸:"))
        self.image_info_labels['dimensions'] = QLabel("未知")
        self.image_info_labels['dimensions'].setStyleSheet("color: #666; margin-left: 5px;")
        size_layout.addWidget(self.image_info_labels['dimensions'])
        size_layout.addStretch()
        info_layout.addLayout(size_layout)
        
        # 文件大小
        filesize_layout = QHBoxLayout()
        filesize_layout.addWidget(QLabel("💾 大小:"))
        self.image_info_labels['filesize'] = QLabel("未知")
        self.image_info_labels['filesize'].setStyleSheet("color: #666; margin-left: 5px;")
        filesize_layout.addWidget(self.image_info_labels['filesize'])
        filesize_layout.addStretch()
        info_layout.addLayout(filesize_layout)
        
        # 格式
        format_layout = QHBoxLayout()
        format_layout.addWidget(QLabel("🎨 格式:"))
        self.image_info_labels['format'] = QLabel("未知")
        self.image_info_labels['format'].setStyleSheet("color: #666; margin-left: 5px;")
        format_layout.addWidget(self.image_info_labels['format'])
        format_layout.addStretch()
        info_layout.addLayout(format_layout)
        
        left_layout.addWidget(info_widget)
        
        # 图片传输按钮和进度条
        self.send_image_btn = QPushButton("发送图片")
        self.send_image_btn.setMinimumHeight(35)
        self.send_image_btn.clicked.connect(self.send_image)
        self.send_image_btn.setEnabled(False)
        left_layout.addWidget(self.send_image_btn)
        
        self.image_progress_bar = QProgressBar()
        self.image_progress_bar.setVisible(False)
        left_layout.addWidget(self.image_progress_bar)
        
        left_layout.addStretch()
        
        # 右侧区域：图片预览
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(10, 0, 0, 0)
        
        preview_label = QLabel("图片预览")
        preview_label.setAlignment(Qt.AlignCenter)
        preview_label.setStyleSheet("font-weight: bold; margin-bottom: 5px;")
        right_layout.addWidget(preview_label)
        
        self.image_preview_label = QLabel()
        self.image_preview_label.setFixedSize(150, 150)
        self.image_preview_label.setAlignment(Qt.AlignCenter)
        self.image_preview_label.setStyleSheet("""
            border: 2px dashed #ccc;
            border-radius: 5px;
            background-color: #f9f9f9;
            color: #999;
        """)
        self.image_preview_label.setText("无图片")
        right_layout.addWidget(self.image_preview_label)
        
        right_layout.addStretch()
        
        # 设置布局比例
        main_layout.addWidget(left_widget, 3)  # 60%
        main_layout.addWidget(right_widget, 2)  # 40%
        
        parent_layout.addWidget(group)
        
    def create_transfer_control(self, parent_layout):
        """创建传输控制区域"""
        group = QGroupBox("传输控制")
        group.setMinimumHeight(80)  # 设置最小高度
        layout = QVBoxLayout(group)
        
        # 发送按钮
        button_layout = QHBoxLayout()
        self.send_btn = QPushButton("发送文件")
        self.send_btn.setMinimumHeight(35)  # 设置按钮最小高度
        self.send_btn.clicked.connect(self.send_file)
        self.send_btn.setEnabled(False)

        button_layout.addWidget(self.send_btn)
        button_layout.addStretch()

        layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        parent_layout.addWidget(group)
        
    def create_log_area(self, parent_layout):
        """创建日志显示区域"""
        group = QGroupBox("服务器日志")
        group.setMinimumHeight(250)  # 设置最小高度
        layout = QVBoxLayout(group)

        self.log_text = QTextEdit()
        self.log_text.setMinimumHeight(200)  # 设置日志文本框最小高度
        self.log_text.setMaximumHeight(400)  # 设置最大高度，避免过度拉伸
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)

        parent_layout.addWidget(group)
        
    def setup_style(self):
        """设置界面样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
            }
            QGroupBox {
                font-weight: bold;
                border: 2px solid #cccccc;
                border-radius: 5px;
                margin-top: 1ex;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
            QPushButton {
                background-color: #4CAF50;
                border: none;
                color: white;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
            QLineEdit, QComboBox {
                padding: 5px;
                border: 1px solid #ddd;
                border-radius: 3px;
            }
        """)
        
    def get_local_ips(self):
        """获取本机IP地址列表"""
        ips = ["127.0.0.1"]
        try:
            import socket
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            if local_ip not in ips:
                ips.append(local_ip)
        except:
            pass
        return ips
        
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.append(f"[{self.get_timestamp()}] {message}")
        
    def get_timestamp(self):
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")

    def save_server_config(self):
        """保存服务器配置"""
        try:
            server_ip = self.ip_combo.currentText().strip()
            server_port = self.port_edit.text().strip()
            file_path = self.selected_file_path

            file_transfer_config.save_server_config(server_ip, server_port, file_path)
        except Exception as e:
            self.log_message(f"保存配置失败: {str(e)}")
        
    def browse_file(self):
        """浏览选择文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, 
            "选择要传输的文件", 
            "", 
            "文本文件 (*.txt);;所有文件 (*.*)"
        )
        
        if file_path:
            self.selected_file_path = file_path
            self.file_path_edit.setText(file_path)
            self.log_message(f"已选择文件: {os.path.basename(file_path)}")

            # 保存文件路径配置
            self.save_server_config()
            
    def start_server(self):
        """启动TCP服务器"""
        try:
            ip = self.ip_combo.currentText().strip()
            port = int(self.port_edit.text().strip())
            
            if not ip or port <= 0:
                QMessageBox.warning(self, "错误", "请输入有效的IP地址和端口号")
                return
                
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((ip, port))
            self.server_socket.listen(1)
            
            self.is_running = True
            self.start_btn.setEnabled(False)
            self.stop_btn.setEnabled(True)
            self.send_btn.setEnabled(True)
            
            self.log_message(f"服务器已启动: {ip}:{port}")
            self.log_message("等待客户端连接...")

            # 保存服务器配置
            self.save_server_config()

            # 启动监听线程
            self.listen_thread = threading.Thread(target=self.listen_for_clients, daemon=True)
            self.listen_thread.start()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"启动服务器失败: {str(e)}")
            self.log_message(f"启动失败: {str(e)}")
            
    def listen_for_clients(self):
        """监听客户端连接"""
        try:
            while self.is_running:
                if self.server_socket:
                    self.client_socket, addr = self.server_socket.accept()
                    self.log_message(f"客户端已连接: {addr[0]}:{addr[1]}")
        except Exception as e:
            if self.is_running:
                self.log_message(f"监听错误: {str(e)}")
                
    def send_file(self):
        """发送文件到客户端"""
        if not self.selected_file_path:
            QMessageBox.warning(self, "错误", "请先选择要发送的文件")
            return
            
        if not self.client_socket:
            QMessageBox.warning(self, "错误", "没有客户端连接")
            return
            
        try:
            # 读取文件内容
            with open(self.selected_file_path, 'r', encoding='utf-8') as f:
                file_content = f.read()
                
            # 准备文件信息
            file_info = {
                'filename': os.path.basename(self.selected_file_path),
                'size': len(file_content.encode('utf-8')),
                'content': file_content
            }
            
            # 发送文件信息（JSON格式）
            message = json.dumps(file_info, ensure_ascii=False)
            message_bytes = message.encode('utf-8')
            
            # 先发送消息长度，再发送消息内容
            length_bytes = len(message_bytes).to_bytes(4, byteorder='big')
            self.client_socket.send(length_bytes)
            self.client_socket.send(message_bytes)
            
            self.log_message(f"文件发送成功: {file_info['filename']} ({file_info['size']} 字节)")
            QMessageBox.information(self, "成功", "文件发送成功！")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"发送文件失败: {str(e)}")
            self.log_message(f"发送失败: {str(e)}")
            
    def browse_image(self):
        """选择图片文件对话框"""
        # 获取支持的图片格式
        image_config = self.server_config.get('image_transfer', {})
        supported_formats = image_config.get('supported_formats', ['png', 'jpg', 'jpeg', 'gif', 'bmp'])
        
        # 构建文件过滤器
        filter_parts = []
        for fmt in supported_formats:
            filter_parts.append(f"*.{fmt}")
        
        image_filter = f"图片文件 ({' '.join(filter_parts)})"
        all_files_filter = "所有文件 (*.*)"
        
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择要传输的图片",
            "",
            f"{image_filter};;{all_files_filter}"
        )
        
        if file_path:
            if self.validate_image_format(file_path):
                self.selected_image_path = file_path
                self.load_image_preview(file_path)
                self.get_image_info(file_path)
                self.send_image_btn.setEnabled(True)
                self.log_message(f"已选择图片: {os.path.basename(file_path)}")
                
                # 保存图片路径到配置
                self.save_image_config(file_path)
            else:
                QMessageBox.warning(self, "错误", "不支持的图片格式！")
                
    def validate_image_format(self, file_path):
        """验证图片格式"""
        try:
            with Image.open(file_path) as img:
                # 检查是否为支持的格式
                image_config = self.server_config.get('image_transfer', {})
                supported_formats = image_config.get('supported_formats', ['png', 'jpg', 'jpeg', 'gif', 'bmp'])
                
                file_format = img.format.lower() if img.format else ''
                if file_format == 'jpeg':
                    file_format = 'jpg'  # 统一为jpg
                    
                return file_format in [fmt.lower() for fmt in supported_formats]
        except Exception:
            return False
            
    def load_image_preview(self, image_path):
        """加载图片预览"""
        try:
            # 获取预览尺寸配置
            image_config = self.server_config.get('image_transfer', {})
            preview_size = image_config.get('preview_size', [150, 150])
            
            # 使用PIL打开和缩放图片
            with Image.open(image_path) as img:
                # 转换为RGB模式（处理透明图片）
                if img.mode in ('RGBA', 'LA'):
                    # 保持透明度
                    img = img.convert('RGBA')
                elif img.mode == 'P':
                    img = img.convert('RGBA')
                else:
                    img = img.convert('RGB')
                
                # 保持长宽比缩放
                img.thumbnail(preview_size, Image.Resampling.LANCZOS)
                
                # 转换为QPixmap
                qimg = ImageQt.ImageQt(img)
                pixmap = QPixmap.fromImage(qimg)
                
                # 设置到预览标签
                self.image_preview_label.setPixmap(pixmap)
                self.image_preview_label.setScaledContents(False)
                
        except Exception as e:
            self.log_message(f"加载图片预览失败: {str(e)}")
            self.image_preview_label.setText("预览失败")
            
    def get_image_info(self, image_path):
        """获取图片详细信息"""
        try:
            # 文件名
            filename = os.path.basename(image_path)
            self.image_info_labels['filename'].setText(filename)
            
            # 文件大小
            file_size = os.path.getsize(image_path)
            if file_size < 1024:
                size_str = f"{file_size} B"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            else:
                size_str = f"{file_size / (1024 * 1024):.1f} MB"
            self.image_info_labels['filesize'].setText(size_str)
            
            # 图片尺寸和格式
            with Image.open(image_path) as img:
                dimensions = f"{img.width}x{img.height} 像素"
                self.image_info_labels['dimensions'].setText(dimensions)
                
                format_name = img.format or '未知'
                self.image_info_labels['format'].setText(format_name)
                
        except Exception as e:
            self.log_message(f"获取图片信息失败: {str(e)}")
            # 设置默认值
            self.image_info_labels['filename'].setText(os.path.basename(image_path))
            self.image_info_labels['dimensions'].setText("未知")
            self.image_info_labels['filesize'].setText("未知")
            self.image_info_labels['format'].setText("未知")
            
    def save_image_config(self, image_path):
        """保存图片配置"""
        try:
            file_transfer_config.save_image_config(image_path)
        except Exception as e:
            self.log_message(f"保存图片配置失败: {str(e)}")
            
    def send_image(self):
        """发送图片到客户端"""
        if not self.selected_image_path:
            QMessageBox.warning(self, "错误", "请先选择要发送的图片")
            return
            
        if not self.client_socket:
            QMessageBox.warning(self, "错误", "没有客户端连接")
            return
            
        try:
            self.image_progress_bar.setVisible(True)
            self.image_progress_bar.setValue(0)
            self.send_image_btn.setEnabled(False)
            
            # 获取文件信息
            filename = os.path.basename(self.selected_image_path)
            file_size = os.path.getsize(self.selected_image_path)
            
            # 检查文件大小限制
            image_config = self.server_config.get('image_transfer', {})
            max_size_mb = image_config.get('max_file_size_mb', 100)
            if file_size > max_size_mb * 1024 * 1024:
                QMessageBox.warning(self, "错误", f"图片文件过大（超过{max_size_mb}MB）")
                return
            
            # 读取图片文件
            with open(self.selected_image_path, 'rb') as f:
                file_content = f.read()
            
            self.image_progress_bar.setValue(30)
            
            # 准备图片信息
            image_info = {
                'type': 'image',
                'filename': filename,
                'size': file_size,
                'content': file_content.hex()  # 转为十六进制字符串
            }
            
            self.image_progress_bar.setValue(60)
            
            # 发送图片信息（JSON格式）
            message = json.dumps(image_info, ensure_ascii=False)
            message_bytes = message.encode('utf-8')
            
            # 先发送消息长度，再发送消息内容
            length_bytes = len(message_bytes).to_bytes(4, byteorder='big')
            self.client_socket.send(length_bytes)
            self.client_socket.send(message_bytes)
            
            self.image_progress_bar.setValue(100)
            
            self.log_message(f"图片发送成功: {filename} ({file_size} 字节)")
            QMessageBox.information(self, "成功", "图片发送成功！")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"发送图片失败: {str(e)}")
            self.log_message(f"图片发送失败: {str(e)}")
        finally:
            self.image_progress_bar.setVisible(False)
            self.send_image_btn.setEnabled(True)
            
    def stop_server(self):
        """停止TCP服务器"""
        try:
            self.is_running = False
            
            if self.client_socket:
                self.client_socket.close()
                self.client_socket = None
                
            if self.server_socket:
                self.server_socket.close()
                self.server_socket = None
                
            self.start_btn.setEnabled(True)
            self.stop_btn.setEnabled(False)
            self.send_btn.setEnabled(False)
            
            self.log_message("服务器已停止")
            
        except Exception as e:
            self.log_message(f"停止服务器错误: {str(e)}")
            
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.is_running:
            self.stop_server()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    # 设置应用程序字体
    font = QFont("Microsoft YaHei", 10)
    app.setFont(font)
    
    window = FileTransferServer()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
