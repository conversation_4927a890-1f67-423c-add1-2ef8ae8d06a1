#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Zemax FFT MTF分析模块

该模块提供了完整的Zemax OpticStudio FFT MTF分析功能，包括：
- 创建和运行FFT MTF分析
- 提取和处理MTF数据
- 使用matplotlib pyplot或Qt界面进行可视化
- 支持切向和弧矢向MTF曲线绘制
- 支持多视场MTF比较分析

主要类：
    - ZemaxFftMtfAnalysis: 主要的MTF分析类
    - MtfPlotCanvas: Qt绘图画布
    - MtfPlotWidget: Qt绘图控件
    - MtfAnalysisWindow: Qt分析窗口

作者: Zemax Python工具开发团队
版本: 1.0.0
兼容性: Python 3.6+, Zemax OpticStudio 2022+
"""

__version__ = "1.0.0"
__author__ = "Zemax Python Tools Team"

import os
import sys
import numpy as np
import matplotlib
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple, Union, Any, Optional

# 设置Qt插件路径（根据实际环境调整）
try:
    # 尝试自动检测conda环境的Qt插件路径
    import sys
    conda_env_path = sys.prefix
    qt_plugin_path = os.path.join(conda_env_path, 'Library', 'plugins')
    if os.path.exists(qt_plugin_path):
        os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = qt_plugin_path
    else:
        # 备用路径
        backup_paths = [
            r'D:\anaconda3\envs\pyzemax\Library\plugins',
            r'C:\anaconda3\envs\pyzemax\Library\plugins',
            r'D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\plugins'
        ]
        for path in backup_paths:
            if os.path.exists(path):
                os.environ['QT_QPA_PLATFORM_PLUGIN_PATH'] = path
                break
except Exception:
    pass

# 导入基础连接模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
# 导入PythonStandaloneApplication类
from zpy_base.zpy_connection import PythonStandaloneApplication

# Qt相关导入（延迟导入，避免在不使用Qt时报错）
_QT_AVAILABLE = False
try:
    from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QHBoxLayout, QMessageBox
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.backends.backend_qt5agg import NavigationToolbar2QT as NavigationToolbar
    from matplotlib.figure import Figure
    _QT_AVAILABLE = True
except ImportError:
    print("警告: PyQt5未安装，Qt绘图功能不可用")

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为 SimHei
plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示问题

class MtfAnalysisError(Exception):
    """MTF分析时的一般错误。"""
    pass

class MtfCreationError(MtfAnalysisError):
    """创建MTF分析时的错误。"""
    pass

class MtfResultError(MtfAnalysisError):
    """获取MTF分析结果时的错误。"""
    pass

class MtfDataError(MtfAnalysisError):
    """提取MTF数据时的错误。"""
    pass

class MtfVisualizationError(MtfAnalysisError):
    """MTF可视化时的错误。"""
    pass


class MtfPlotCanvas(FigureCanvas):
    """MTF绘图画布类，继承自FigureCanvasQTAgg"""
    
    def __init__(self, parent=None, width=10, height=6, dpi=100):
        """
        初始化MTF绘图画布。
        
        Args:
            parent: 父控件
            width (float): 图形宽度（英寸）
            height (float): 图形高度（英寸）
            dpi (int): 分辨率
        """
        if not _QT_AVAILABLE:
            raise MtfVisualizationError("Qt绘图功能不可用，请安装PyQt5")
            
        # 创建matplotlib图形对象
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        super().__init__(self.fig)
        self.setParent(parent)
        
        # 创建子图
        self.axes = self.fig.add_subplot(111)
        
        # 设置中文字体
        matplotlib.rcParams['font.sans-serif'] = ['SimHei']
        matplotlib.rcParams['axes.unicode_minus'] = False
    
    def plot_mtf_series(self, x_data, y_data, series_labels=None, title="FFT MTF曲线", 
                       x_label="频率 (cycles/mm)", y_label="MTF"):
        """
        绘制MTF系列数据。
        
        Args:
            x_data: X轴数据（频率）
            y_data: Y轴数据（MTF值）
            series_labels: 系列标签列表
            title: 图表标题
            x_label: X轴标签
            y_label: Y轴标签
        """
        # 清除之前的绘图
        self.axes.clear()
        
        # 绘制每个系列
        if y_data.ndim == 2:
            for i in range(y_data.shape[1]):
                label = series_labels[i] if series_labels and i < len(series_labels) else f"系列 {i+1}"
                self.axes.plot(x_data, y_data[:, i], label=label)
        else:
            self.axes.plot(x_data, y_data, label="MTF")
        
        # 设置标题和标签
        self.axes.set_title(title)
        self.axes.set_xlabel(x_label)
        self.axes.set_ylabel(y_label)
        self.axes.legend()
        self.axes.grid(True)
        self.axes.set_ylim(0, 1.05)
        
        # 刷新画布
        self.draw()
    
    def plot_mtf_comparison(self, mtf_data, series_indices=None, direction="切向", 
                           title="不同视场MTF曲线比较"):
        """
        绘制MTF比较图。
        
        Args:
            mtf_data: MTF数据字典
            series_indices: 要绘制的系列索引列表
            direction: 绘制方向（"切向"或"弧矢向"）
            title: 图表标题
        """
        # 清除之前的绘图
        self.axes.clear()
        
        if series_indices is None:
            series_indices = list(range(len(mtf_data["数据系列"])))
        
        # 确定方向索引
        dir_idx = 0 if direction == "切向" else 1
        
        # 绘制每个系列
        for idx in series_indices:
            series = mtf_data["数据系列"][idx]
            
            if "X数据" not in series or "Y数据" not in series:
                continue
            
            x_data = series["X数据"]
            y_data = np.array(series["Y数据"])
            
            if y_data.shape[0] == len(x_data) and y_data.shape[1] > dir_idx:
                y_values = y_data[:, dir_idx]
            else:
                continue
            
            # 获取标签
            if "系列标签" in series and len(series["系列标签"]) > dir_idx:
                label = series["系列标签"][dir_idx]
                if "描述" in series:
                    label = f"{series['描述']} - {label}"
            else:
                label = f"系列 {idx+1} - {direction}"
            
            self.axes.plot(x_data, y_values, label=label)
        
        # 设置图表属性
        self.axes.set_title(title)
        self.axes.set_xlabel("频率 (cycles/mm)")
        self.axes.set_ylabel("MTF")
        self.axes.legend()
        self.axes.grid(True)
        self.axes.set_ylim(0, 1.05)
        
        # 刷新画布
        self.draw()


class MtfPlotWidget(QWidget):
    """MTF绘图控件，包含画布和工具栏"""
    
    def __init__(self, parent=None):
        """
        初始化MTF绘图控件。
        
        Args:
            parent: 父控件
        """
        if not _QT_AVAILABLE:
            raise MtfVisualizationError("Qt绘图功能不可用，请安装PyQt5")
            
        super().__init__(parent)
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建布局
        layout = QVBoxLayout()
        
        # 创建绘图画布
        self.canvas = MtfPlotCanvas(self, width=10, height=6, dpi=100)
        
        # 创建导航工具栏
        self.toolbar = NavigationToolbar(self.canvas, self)
        
        # 添加到布局
        layout.addWidget(self.toolbar)
        layout.addWidget(self.canvas)
        
        self.setLayout(layout)
    
    def plot_mtf_data(self, mtf_data, series_index=0):
        """
        绘制指定系列的MTF数据。
        
        Args:
            mtf_data: MTF数据字典
            series_index: 系列索引
        """
        if series_index >= len(mtf_data["数据系列"]):
            return
        
        series = mtf_data["数据系列"][series_index]
        x_data = series["X数据"]
        y_data = np.array(series["Y数据"])
        series_labels = series.get("系列标签", None)
        title = f"FFT MTF曲线 - {series.get('描述', f'系列 {series_index+1}')}"
        
        self.canvas.plot_mtf_series(x_data, y_data, series_labels, title)
    
    def plot_mtf_comparison(self, mtf_data, direction="切向"):
        """
        绘制MTF比较图。
        
        Args:
            mtf_data: MTF数据字典
            direction: 绘制方向
        """
        self.canvas.plot_mtf_comparison(mtf_data, direction=direction)


class MtfAnalysisWindow(QMainWindow):
    """MTF分析主窗口"""
    
    def __init__(self, mtf_analyzer):
        """
        初始化MTF分析窗口。
        
        Args:
            mtf_analyzer: MTF分析器实例
        """
        if not _QT_AVAILABLE:
            raise MtfVisualizationError("Qt绘图功能不可用，请安装PyQt5")
            
        super().__init__()
        self.mtf_analyzer = mtf_analyzer
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle('Zemax FFT MTF分析工具')
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央控件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        main_layout = QVBoxLayout()
        button_layout = QHBoxLayout()
        
        # 创建按钮
        self.plot_single_button = QPushButton('绘制单个系列MTF')
        self.plot_tangential_button = QPushButton('绘制切向MTF比较')
        self.plot_sagittal_button = QPushButton('绘制弧矢向MTF比较')
        self.close_button = QPushButton('关闭')
        
        # 连接信号
        self.plot_single_button.clicked.connect(self.plot_single_series)
        self.plot_tangential_button.clicked.connect(self.plot_tangential_comparison)
        self.plot_sagittal_button.clicked.connect(self.plot_sagittal_comparison)
        self.close_button.clicked.connect(self.close)
        
        # 添加按钮到布局
        button_layout.addWidget(self.plot_single_button)
        button_layout.addWidget(self.plot_tangential_button)
        button_layout.addWidget(self.plot_sagittal_button)
        button_layout.addWidget(self.close_button)
        
        # 创建绘图区域
        self.plot_widget = MtfPlotWidget()
        
        # 添加到主布局
        main_layout.addLayout(button_layout)
        main_layout.addWidget(self.plot_widget)
        
        central_widget.setLayout(main_layout)
        
        # 如果有数据，默认绘制第一个系列
        if (self.mtf_analyzer.mtf_data and 
            len(self.mtf_analyzer.mtf_data["数据系列"]) > 0):
            self.plot_widget.plot_mtf_data(self.mtf_analyzer.mtf_data, 0)
    
    def plot_single_series(self):
        """绘制单个系列MTF"""
        if self.mtf_analyzer.mtf_data:
            self.plot_widget.plot_mtf_data(self.mtf_analyzer.mtf_data, 0)
    
    def plot_tangential_comparison(self):
        """绘制切向MTF比较"""
        if self.mtf_analyzer.mtf_data:
            self.plot_widget.plot_mtf_comparison(self.mtf_analyzer.mtf_data, "切向")
    
    def plot_sagittal_comparison(self):
        """绘制弧矢向MTF比较"""
        if self.mtf_analyzer.mtf_data:
            self.plot_widget.plot_mtf_comparison(self.mtf_analyzer.mtf_data, "弧矢向")

class ZemaxFftMtfAnalysis:
    """
    用于进行Zemax光学系统FFT MTF分析的类。
    提供创建MTF分析、读取结果、提取信息和可视化绘制功能。
    """

    def __init__(self, zos: PythonStandaloneApplication):
        """
        初始化与Zemax的连接。

        Args:
            zos (PythonStandaloneApplication): Zemax连接实例。

        Raises:
            Exception: 如果初始化失败。
        """
        try:
            # 创建与Zemax的连接
            self.zos = zos
            self.ZOSAPI = self.zos.ZOSAPI
            self.TheApplication = self.zos.TheApplication
            self.TheSystem = self.zos.TheSystem
            
            # 初始化MTF分析相关变量
            self.mtf_analysis = None
            self.mtf_results = None
            self.mtf_data = None
        except Exception as e:
            raise Exception(f"Zemax API初始化失败: {str(e)}")

    def load_file(self, file_path: str, save_if_needed: bool = False) -> None:
        """
        加载Zemax文件。

        Args:
            file_path (str): Zemax文件路径。
            save_if_needed (bool, optional): 如果需要，是否保存当前文件。默认为False。

        Raises:
            FileNotFoundError: 如果文件不存在。
            Exception: 如果加载文件时出错。
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
                
            self.TheSystem.LoadFile(file_path, save_if_needed)
            print(f"文件加载成功: {file_path}")
        except FileNotFoundError as e:
            raise
        except Exception as e:
            raise Exception(f"加载文件时出错: {str(e)}")

    def create_fftmtf_analysis(self, 
                              field_number: int = 1, 
                              wavelength_number: int = 1,
                              surface_number: int = -1,  # -1表示像面
                              mtf_type: str = "Modulation",  # Modulation, Phase, SquareWave
                              sample_size: int = 64,  # 64, 128, 256, 512, 1024
                              show_diffraction_limit: bool = True,
                              use_dashes: bool = False,
                              use_polarization: bool = False,
                              maximum_frequency: float = 0.0) -> None:
        """
        创建FFT MTF分析。

        Args:
            field_number (int, optional): 视场编号，0表示所有视场。默认为1。
            wavelength_number (int, optional): 波长编号，0表示所有波长。默认为1。
            surface_number (int, optional): 表面编号，-1表示像面。默认为-1。
            mtf_type (str, optional): MTF类型，可选"Modulation"、"Phase"、"SquareWave"。默认为"Modulation"。
            sample_size (int, optional): 采样大小，支持64、128、256、512、1024。默认为64。
            show_diffraction_limit (bool, optional): 是否显示衍射极限。默认为True。
            use_dashes (bool, optional): 是否使用虚线。默认为False。
            use_polarization (bool, optional): 是否使用偏振。默认为False。
            maximum_frequency (float, optional): 最大频率，0表示自动。默认为0.0。

        Raises:
            MtfCreationError: 如果创建MTF分析时出错。
        """
        try:
            # 创建FFT MTF分析
            self.mtf_analysis = self.TheSystem.Analyses.New_Analysis(
                self.ZOSAPI.Analysis.AnalysisIDM.FftMtf)
            
            if self.mtf_analysis is None:
                raise MtfCreationError("无法创建FFT MTF分析")
            
            # 获取设置
            settings = self.mtf_analysis.GetSettings()
            
            # 设置视场
            if field_number == 0:
                settings.Field.UseAllFields()
            else:
                settings.Field.SetFieldNumber(field_number)
            
            # 设置波长
            if wavelength_number == 0:
                settings.Wavelength.UseAllWavelengths()
            else:
                settings.Wavelength.SetWavelengthNumber(wavelength_number)
            
            # 设置表面
            if surface_number == -1:
                settings.Surface.UseImageSurface()
            else:
                settings.Surface.SetSurfaceNumber(surface_number)
            
            # 设置MTF类型
            if mtf_type == "Modulation":
                settings.Type = self.ZOSAPI.Analysis.Settings.Mtf.MtfTypes.Modulation
            elif mtf_type == "Phase":
                settings.Type = self.ZOSAPI.Analysis.Settings.Mtf.MtfTypes.Phase
            elif mtf_type == "Real":
                settings.Type = self.ZOSAPI.Analysis.Settings.Mtf.MtfTypes.Real
            elif mtf_type == "Imaginary":
                settings.Type = self.ZOSAPI.Analysis.Settings.Mtf.MtfTypes.Imaginary
            elif mtf_type == "SquareWave":
                settings.Type = self.ZOSAPI.Analysis.Settings.Mtf.MtfTypes.SquareWave
            else:
                settings.Type = self.ZOSAPI.Analysis.Settings.Mtf.MtfTypes.Modulation
                print(f"警告: 不支持的MTF类型 {mtf_type}，已使用默认值Modulation")
            
            # 设置采样大小
            if sample_size == 64:
                settings.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_64x64
            elif sample_size == 128:
                settings.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_128x128
            elif sample_size == 256:
                settings.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_256x256
            elif sample_size == 512:
                settings.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_512x512
            elif sample_size == 1024:
                settings.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_1024x1024
            else:
                settings.SampleSize = self.ZOSAPI.Analysis.SampleSizes.S_64x64
                print(f"警告: 不支持的采样大小 {sample_size}，已使用默认值64x64")
            
            # 设置其他选项
            settings.ShowDiffractionLimit = show_diffraction_limit
            settings.UseDashes = use_dashes
            settings.UsePolarization = use_polarization
            
            # 设置最大频率（如果不为0）
            if maximum_frequency > 0:
                settings.MaximumFrequency = maximum_frequency
            
            print("FFT MTF分析创建成功")
        except Exception as e:
            raise MtfCreationError(f"创建FFT MTF分析时出错: {str(e)}")

    def run_analysis(self) -> None:
        """
        运行FFT MTF分析。

        Raises:
            MtfCreationError: 如果运行MTF分析时出错。
        """
        try:
            if self.mtf_analysis is None:
                raise MtfCreationError("MTF分析未创建，请先调用create_fftmtf_analysis方法")
            
            # 运行分析并等待完成
            self.mtf_analysis.ApplyAndWaitForCompletion()
            print("FFT MTF分析运行完成")
        except Exception as e:
            raise MtfCreationError(f"运行FFT MTF分析时出错: {str(e)}")

    def get_results(self) -> None:
        """
        获取FFT MTF分析结果。

        Raises:
            MtfResultError: 如果获取MTF分析结果时出错。
        """
        try:
            if self.mtf_analysis is None:
                raise MtfResultError("MTF分析未创建，请先调用create_fftmtf_analysis方法")
            
            # 获取分析结果
            self.mtf_results = self.mtf_analysis.GetResults()
            
            if self.mtf_results is None:
                raise MtfResultError("无法获取FFT MTF分析结果")
                
            print("获取FFT MTF分析结果成功")
        except Exception as e:
            raise MtfResultError(f"获取FFT MTF分析结果时出错: {str(e)}")

    def extract_mtf_data(self) -> Dict[str, Any]:
        """
        提取MTF数据并结构化输出。

        Returns:
            Dict[str, Any]: 包含MTF数据的字典。

        Raises:
            MtfDataError: 如果提取MTF数据时出错。
        """
        try:
            if self.mtf_results is None:
                raise MtfDataError("MTF分析结果未获取，请先调用get_results方法")
            
            # 存储MTF数据的字典
            mtf_data = {}
            
            # 获取数据系列数量
            series_count = self.mtf_results.NumberOfDataSeries
            if series_count <= 0:
                raise MtfDataError("没有可用的MTF数据系列")
            
            mtf_data["数据系列数量"] = series_count
            mtf_data["数据系列"] = []
            
            # 提取每个数据系列
            for i in range(series_count):
                series = self.mtf_results.GetDataSeries(i)
                if series is None:
                    continue
                
                # 创建数据系列字典
                series_data = {}
                series_data["索引"] = i
                series_data["描述"] = series.Description if hasattr(series, "Description") else f"数据系列 {i}"
                
                # 获取X轴标签
                x_label = series.XLabel if hasattr(series, "XLabel") else "频率 (cycles/mm)"
                series_data["X轴标签"] = x_label
                
                # 获取X数据 (IVectorData类型)
                if hasattr(series, "XData") and series.XData is not None:
                    try:
                        # 使用Data属性获取真实数据
                        x_data = list(series.XData.Data)
                        series_data["X数据"] = x_data
                    except Exception as ex:
                        print(f"获取X数据时出错: {ex}")
                
                # 获取Y数据 (IMatrixData类型)
                if hasattr(series, "YData") and series.YData is not None:
                    try:
                        # 获取行数和列数
                        rows = series.YData.Rows
                        cols = series.YData.Cols
                        
                        # 创建Y数据矩阵
                        y_data = []
                        for r in range(rows):
                            row_data = []
                            for c in range(cols):
                                row_data.append(series.YData.GetValueAt(r, c))
                            y_data.append(row_data)
                        
                        series_data["Y数据"] = y_data
                        series_data["行数"] = rows
                        series_data["列数"] = cols
                    except Exception as ex:
                        print(f"获取Y数据时出错: {ex}")
                
                # 获取系列标签
                if hasattr(series, "SeriesLabels") and series.SeriesLabels is not None:
                    try:
                        # 检查SeriesLabels是否有Data属性
                        if hasattr(series.SeriesLabels, "Data"):
                            series_labels = list(series.SeriesLabels.Data)
                        else:
                            # 如果没有Data属性，尝试直接转换
                            series_labels = list(series.SeriesLabels)
                        series_data["系列标签"] = series_labels
                    except Exception as ex:
                        print(f"获取系列标签时出错: {ex}")
                
                # 获取系列数量
                if hasattr(series, "NumSeries"):
                    num_series = series.NumSeries
                    series_data["系列数量"] = num_series
                
                # 添加到数据系列列表
                mtf_data["数据系列"].append(series_data)
            
            # 保存结果供后续使用
            self.mtf_data = mtf_data
            
            return mtf_data
        except Exception as e:
            raise MtfDataError(f"提取MTF数据时出错: {str(e)}")

    def print_mtf_info(self) -> None:
        """
        打印MTF分析信息。

        Raises:
            MtfDataError: 如果MTF数据未提取。
        """
        try:
            if self.mtf_data is None:
                raise MtfDataError("MTF数据未提取，请先调用extract_mtf_data方法")
            
            print("\n======= FFT MTF分析信息 =======")
            print(f"数据系列数量: {self.mtf_data['数据系列数量']}")
            
            # 打印每个数据系列的信息
            for i, series in enumerate(self.mtf_data["数据系列"]):
                print(f"\n数据系列 {i+1}:")
                print(f"  描述: {series['描述']}")
                print(f"  X轴标签: {series['X轴标签']}")
                
                if "系列标签" in series:
                    print(f"  系列标签: {', '.join(series['系列标签'])}")
                
                if "系列数量" in series:
                    print(f"  系列数量: {series['系列数量']}")
                
                if "行数" in series and "列数" in series:
                    print(f"  数据维度: {series['行数']}×{series['列数']}")
                
                # # 打印X数据和Y数据的范围
                # if "X数据" in series:
                #     x_min = min(series["X数据"])
                #     x_max = max(series["X数据"])
                #     print(f"  频率范围: {x_min:.4f} - {x_max:.4f} cycles/mm")
                
                # if "Y数据" in series and len(series["Y数据"]) > 0 and len(series["Y数据"][0]) > 0:
                #     for i, row in enumerate(series["Y数据"]):
                #         if len(row) > 0:
                #             y_min = min(row)
                #             y_max = max(row)
                #             if "系列标签" in series and i < len(series["系列标签"]):
                #                 print(f"  {series['系列标签'][i]}范围: {y_min:.4f} - {y_max:.4f}")
                #             else:
                #                 print(f"  系列{i+1}范围: {y_min:.4f} - {y_max:.4f}")
            
            print("==============================")
        except Exception as e:
            raise MtfDataError(f"打印MTF信息时出错: {str(e)}")

    def plot_mtf(self, 
                title: str = "FFT MTF曲线", 
                x_label: str = None,
                y_label: str = "MTF",
                legend_loc: str = "best",
                grid: bool = True,
                figsize: Tuple[int, int] = (10, 6),
                series_index: int = 0) -> None:
        """
        绘制MTF曲线。

        Args:
            title (str, optional): 图表标题。默认为"FFT MTF曲线"。
            x_label (str, optional): X轴标签。如果为None，则使用数据中的标签。
            y_label (str, optional): Y轴标签。默认为"MTF"。
            legend_loc (str, optional): 图例位置。默认为"best"。
            grid (bool, optional): 是否显示网格。默认为True。
            figsize (Tuple[int, int], optional): 图表大小。默认为(10, 6)。
            series_index (int, optional): 要绘制的数据系列索引。默认为0。

        Raises:
            MtfVisualizationError: 如果绘制MTF曲线时出错。
        """
        try:
            if self.mtf_data is None:
                raise MtfVisualizationError("MTF数据未提取，请先调用extract_mtf_data方法")
            
            # 检查数据系列索引是否有效
            if series_index < 0 or series_index >= len(self.mtf_data["数据系列"]):
                raise MtfVisualizationError(f"无效的数据系列索引: {series_index}")
            
            # 获取指定的数据系列
            series = self.mtf_data["数据系列"][series_index]
            
            # 检查数据系列中是否包含所需的数据
            if "X数据" not in series or "Y数据" not in series:
                raise MtfVisualizationError("数据系列中缺少X数据或Y数据")
            
            # 获取X数据和Y数据
            x_data = series["X数据"]
            y_data = np.array(series["Y数据"])

            # 打印数据维度以便于调试
            print(f"X数据维度: {np.array(x_data).shape}")
            print(f"Y数据维度: {y_data.shape}")
            
            # 创建一个新的图表
            plt.figure(figsize=figsize)
            
            # 设置X轴标签
            if x_label is None and "X轴标签" in series:
                x_label = series["X轴标签"]
            elif x_label is None:
                x_label = "频率 (cycles/mm)"
            
            # 修改后的绘图逻辑，正确处理MTF数据的结构
            # 对于维度为(300, 2)的y_data，需要按列提取数据绘制曲线
            if y_data.shape[0] == len(x_data) and y_data.shape[1] < len(x_data):
                # 绘制每个方向的曲线
                if "系列标签" in series and len(series["系列标签"]) > 0:
                    for i in range(y_data.shape[1]):  # 遍历每一列
                        if i < len(series["系列标签"]):
                            plt.plot(x_data, y_data[:, i], label=series["系列标签"][i])
                        else:
                            plt.plot(x_data, y_data[:, i], label=f"系列 {i+1}")
                else:
                    for i in range(y_data.shape[1]):  # 遍历每一列
                        plt.plot(x_data, y_data[:, i], label=f"系列 {i+1}")
            else:
                # 原有的逻辑，以防万一
                if "系列标签" in series and len(series["系列标签"]) > 0:
                    for i, row in enumerate(y_data):
                        if i < len(series["系列标签"]):
                            plt.plot(x_data, row, label=series["系列标签"][i])
                        else:
                            plt.plot(x_data, row, label=f"系列 {i+1}")
                else:
                    for i, row in enumerate(y_data):
                        plt.plot(x_data, row, label=f"系列 {i+1}")
            
            # 设置标题和轴标签
            plt.title(title)
            plt.xlabel(x_label)
            plt.ylabel(y_label)
            
            # 设置图例
            plt.legend(loc=legend_loc)
            
            # 设置网格
            plt.grid(grid)
            
            # 设置Y轴范围为0到1
            plt.ylim(0, 1.05)
            
            # 显示图表
            plt.tight_layout()
            plt.show()
        except Exception as e:
            raise MtfVisualizationError(f"绘制MTF曲线时出错: {str(e)}")

    def plot_mtf_comparison(self, 
                           title: str = "不同视场MTF曲线比较", 
                           x_label: str = "频率 (cycles/mm)",
                           y_label: str = "MTF",
                           legend_loc: str = "best",
                           grid: bool = True,
                           figsize: Tuple[int, int] = (12, 8),
                           series_indices: List[int] = None,
                           direction: str = "切向") -> None:
        """
        绘制不同数据系列的MTF曲线比较图。

        Args:
            title (str, optional): 图表标题。默认为"不同视场MTF曲线比较"。
            x_label (str, optional): X轴标签。默认为"频率 (cycles/mm)"。
            y_label (str, optional): Y轴标签。默认为"MTF"。
            legend_loc (str, optional): 图例位置。默认为"best"。
            grid (bool, optional): 是否显示网格。默认为True。
            figsize (Tuple[int, int], optional): 图表大小。默认为(12, 8)。
            series_indices (List[int], optional): 要绘制的数据系列索引列表。如果为None，则绘制所有系列。
            direction (str, optional): 要绘制的方向，可选"切向"或"弧矢向"。默认为"切向"。

        Raises:
            MtfVisualizationError: 如果绘制MTF曲线比较图时出错。
        """
        try:
            if self.mtf_data is None:
                raise MtfVisualizationError("MTF数据未提取，请先调用extract_mtf_data方法")
            
            # 如果未指定系列索引，则使用所有系列
            if series_indices is None:
                series_indices = list(range(len(self.mtf_data["数据系列"])))
            
            # 检查系列索引是否有效
            for idx in series_indices:
                if idx < 0 or idx >= len(self.mtf_data["数据系列"]):
                    raise MtfVisualizationError(f"无效的数据系列索引: {idx}")
            
            # 创建一个新的图表
            plt.figure(figsize=figsize)
            
            # 确定方向索引
            dir_idx = 0  # 默认为切向
            if direction.lower() in ["弧矢向", "sagittal", "s"]:
                dir_idx = 1
            
            # 绘制每个系列的指定方向曲线
            for idx in series_indices:
                series = self.mtf_data["数据系列"][idx]
                
                # 检查数据系列中是否包含所需的数据
                if "X数据" not in series or "Y数据" not in series:
                    continue
                
                # 获取X数据和Y数据
                x_data = series["X数据"]
                y_data = np.array(series["Y数据"])
                
                # 检查Y数据的维度以确定如何提取数据
                if y_data.shape[0] == len(x_data) and y_data.shape[1] > dir_idx:
                    # Y数据维度为(频率点数, 方向数)，直接提取对应列
                    y_values = y_data[:, dir_idx]
                elif y_data.shape[0] > dir_idx and y_data.shape[1] == len(x_data):
                    # Y数据维度为(方向数, 频率点数)，提取对应行
                    y_values = y_data[dir_idx, :]
                else:
                    # 无法确定维度或无法提取对应方向的数据
                    continue
                
                # 获取系列标签
                if "系列标签" in series and len(series["系列标签"]) > dir_idx:
                    label = series["系列标签"][dir_idx]
                    # 添加视场信息（假设描述中包含视场信息）
                    if "描述" in series:
                        label = f"{series['描述']} - {label}"
                else:
                    label = f"系列 {idx+1} - {direction}"
                
                # 绘制曲线
                plt.plot(x_data, y_values, label=label)
            
            # 设置标题和轴标签
            plt.title(title)
            plt.xlabel(x_label)
            plt.ylabel(y_label)
            
            # 设置图例
            plt.legend(loc=legend_loc)
            
            # 设置网格
            plt.grid(grid)
            
            # 设置Y轴范围为0到1
            plt.ylim(0, 1.05)
            
            # 显示图表
            plt.tight_layout()
            plt.show()
        except Exception as e:
            raise MtfVisualizationError(f"绘制MTF曲线比较图时出错: {str(e)}")

    def save_results(self, file_path: str) -> None:
        """
        保存MTF分析结果到文件。

        Args:
            file_path (str): 保存文件路径。

        Raises:
            MtfResultError: 如果保存MTF分析结果时出错。
        """
        try:
            if self.mtf_results is None:
                raise MtfResultError("MTF分析结果未获取，请先调用get_results方法")
            
            # 保存结果
            self.mtf_results.GetTextFile(file_path)
            print(f"MTF分析结果已保存到: {file_path}")
        except Exception as e:
            raise MtfResultError(f"保存MTF分析结果时出错: {str(e)}")

    def close(self) -> None:
        """
        关闭MTF分析并释放资源。
        """
        try:
            # 关闭分析
            if hasattr(self, 'mtf_analysis') and self.mtf_analysis is not None:
                self.mtf_analysis.Close()
                self.mtf_analysis = None
            
            # 释放结果
            if hasattr(self, 'mtf_results'):
                self.mtf_results = None
            
            # 释放数据
            if hasattr(self, 'mtf_data'):
                self.mtf_data = None
            
            print("MTF分析已关闭")
        except Exception as e:
            print(f"关闭MTF分析时出错: {str(e)}")

    def __del__(self):
        """
        析构函数，确保资源被正确释放。
        """
        self.close()

    def get_mtf_at_frequency(self, frequency: float) -> Dict[int, float]:
        """
        获取指定空间频率对应的MTF值。

        Args:
            frequency (float): 指定的空间频率。

        Returns:
            Dict[int, float]: MTF值，键为系列索引，值为对应的MTF值。

        Raises:
            MtfDataError: 如果数据未提取或频率无效。
        """
        try:
            if self.mtf_data is None:
                raise MtfDataError("MTF数据未提取")
            
            results = {}
            for series_idx, series in enumerate(self.mtf_data["数据系列"]):
                if "X数据" not in series or "Y数据" not in series:
                    print(f"警告: 数据系列 {series_idx} 缺少X或Y数据，已跳过")
                    continue
                
                x_data = series["X数据"]
                y_data = np.array(series["Y数据"])
                
                # 查找与指定频率最接近的索引
                closest_index = (np.abs(np.array(x_data) - frequency)).argmin()
                results[series_idx] = y_data[closest_index]  # 提取对应的MTF值
            
            return results
        except Exception as e:
            raise MtfDataError(f"获取MTF值时出错: {str(e)}")

    def plot_mtf_qt(self, series_index: int = 0) -> 'MtfPlotWidget':
        """
        在Qt控件中绘制MTF曲线。
        
        Args:
            series_index (int): 要绘制的数据系列索引
        
        Returns:
            MtfPlotWidget: MTF绘图控件
            
        Raises:
            MtfVisualizationError: 如果Qt绘图功能不可用或数据未提取
        """
        if not _QT_AVAILABLE:
            raise MtfVisualizationError("Qt绘图功能不可用，请安装PyQt5")
            
        if self.mtf_data is None:
            raise MtfVisualizationError("MTF数据未提取，请先调用extract_mtf_data方法")
        
        # 创建Qt绘图控件
        plot_widget = MtfPlotWidget()
        plot_widget.plot_mtf_data(self.mtf_data, series_index)
        
        return plot_widget

    def plot_mtf_comparison_qt(self, direction: str = "切向") -> 'MtfPlotWidget':
        """
        在Qt控件中绘制MTF比较图。
        
        Args:
            direction (str): 绘制方向（"切向"或"弧矢向"）
        
        Returns:
            MtfPlotWidget: MTF绘图控件
            
        Raises:
            MtfVisualizationError: 如果Qt绘图功能不可用或数据未提取
        """
        if not _QT_AVAILABLE:
            raise MtfVisualizationError("Qt绘图功能不可用，请安装PyQt5")
            
        if self.mtf_data is None:
            raise MtfVisualizationError("MTF数据未提取，请先调用extract_mtf_data方法")
        
        # 创建Qt绘图控件
        plot_widget = MtfPlotWidget()
        plot_widget.plot_mtf_comparison(self.mtf_data, direction)
        
        return plot_widget

    def show_mtf_analysis_window(self) -> 'MtfAnalysisWindow':
        """
        显示MTF分析窗口。
        
        Returns:
            MtfAnalysisWindow: MTF分析窗口实例
            
        Raises:
            MtfVisualizationError: 如果Qt绘图功能不可用或数据未提取
        """
        if not _QT_AVAILABLE:
            raise MtfVisualizationError("Qt绘图功能不可用，请安装PyQt5")
            
        if self.mtf_data is None:
            raise MtfVisualizationError("MTF数据未提取，请先调用extract_mtf_data方法")
        
        # 创建并显示MTF分析窗口
        window = MtfAnalysisWindow(self)
        window.show()
        
        return window


def choose_plot_backend() -> str:
    """
    让用户选择绘图后端。
    
    Returns:
        str: 选择的后端类型 ("plt" 或 "qt")
    """
    print("\n======= 绘图方式选择 =======")
    print("请选择绘图方式:")
    print("1. matplotlib pyplot (传统弹窗显示)")
    print("2. Qt界面 (集成化界面显示)")
    
    if not _QT_AVAILABLE:
        print("注意: Qt绘图功能不可用，将自动使用matplotlib pyplot")
        return "plt"
    
    while True:
        choice = input("请输入选择 (1 或 2): ").strip()
        if choice == "1":
            return "plt"
        elif choice == "2":
            return "qt"
        else:
            print("无效选择，请输入 1 或 2")


def run_mtf_analysis_with_plots(mtf_analyzer, plot_backend: str = "plt"):
    """
    运行MTF分析并根据选择的后端进行绘图。
    
    Args:
        mtf_analyzer: MTF分析器实例
        plot_backend (str): 绘图后端类型 ("plt" 或 "qt")
    """
    # 获取MTF数据
    mtf_data = mtf_analyzer.mtf_data
    
    # 获取指定频率的MTF值
    frequency = 50
    results = mtf_analyzer.get_mtf_at_frequency(frequency)
    print(f"在频率 {frequency} cycles/mm 处的MTF结果: {results}")
    
    if plot_backend == "qt":
        # 使用Qt界面显示
        print("\n使用Qt界面显示MTF分析结果...")
        
        # 设置Qt后端
        matplotlib.use('Qt5Agg')
        
        # 创建QApplication实例
        app = QApplication(sys.argv)
        
        # 显示MTF分析窗口
        window = mtf_analyzer.show_mtf_analysis_window()
        
        # 运行Qt事件循环
        sys.exit(app.exec_())
        
    else:
        # 使用matplotlib pyplot显示
        print("\n使用matplotlib pyplot显示MTF分析结果...")
        
        # 为每个数据系列绘制MTF曲线
        for i in range(len(mtf_data["数据系列"])):
            mtf_analyzer.plot_mtf(
                title=f"FFT MTF曲线 - 数据系列 {i+1}",
                series_index=i
            )
        
        # 绘制切向MTF曲线比较图
        mtf_analyzer.plot_mtf_comparison(
            title="不同视场切向MTF曲线比较",
            direction="切向"
        )
        
        # 绘制弧矢向MTF曲线比较图
        mtf_analyzer.plot_mtf_comparison(
            title="不同视场弧矢向MTF曲线比较",
            direction="弧矢向"
        )


if __name__ == "__main__":
    """
    主程序入口，演示ZemaxFftMtfAnalysis类的用法。
    支持用户选择使用matplotlib pyplot或Qt界面进行绘图。
    
    使用示例：
    1. 运行程序后，会自动加载示例文件并进行MTF分析
    2. 程序会提示选择绘图方式：
       - 选择 1: 使用matplotlib pyplot显示（传统弹窗方式）
       - 选择 2: 使用Qt界面显示（集成化界面，支持交互操作）
    3. 根据选择显示相应的MTF分析结果
    
    注意事项：
    - 确保Zemax OpticStudio已正确安装并授权
    - 确保示例文件路径正确
    - 如果选择Qt界面，需要安装PyQt5
    """
    # 示例文件路径
    sample_file = os.path.abspath(os.path.join("data", "19-zemax.ZMX"))
    output_file = os.path.abspath(os.path.join("output", "mtf_results.txt"))
    
    try:
        # 创建目录（如果不存在）
        os.makedirs(os.path.dirname(output_file), exist_ok=True)
        
        # 创建Zemax连接
        zos = PythonStandaloneApplication()
        
        # 创建ZemaxFftMtfAnalysis实例
        mtf_analyzer = ZemaxFftMtfAnalysis(zos)
        
        # 加载示例文件
        mtf_analyzer.load_file(sample_file)
        
        # 创建FFT MTF分析
        mtf_analyzer.create_fftmtf_analysis(
            field_number=0,  # 使用所有视场
            wavelength_number=0,  # 使用所有波长
            surface_number=-1,  # 像面
            mtf_type="Modulation",
            sample_size=64,
            show_diffraction_limit=True,
            use_dashes=False,
            use_polarization=False,
            maximum_frequency=100.0  # 设置最大频率为100 cycles/mm
        )
        
        # 运行分析
        mtf_analyzer.run_analysis()
        
        # 获取结果
        mtf_analyzer.get_results()
        
        # 提取MTF数据
        mtf_data = mtf_analyzer.extract_mtf_data()
        
        # 打印MTF信息
        mtf_analyzer.print_mtf_info()
        
        # 保存结果
        mtf_analyzer.save_results(output_file)
        
        # 让用户选择绘图方式
        plot_backend = choose_plot_backend()
        
        # 根据选择运行绘图
        run_mtf_analysis_with_plots(mtf_analyzer, plot_backend)
        
    except Exception as e:
        print(f"错误: {str(e)}")
    finally:
        # 确保资源被释放
        if 'mtf_analyzer' in locals():
            mtf_analyzer.close()
