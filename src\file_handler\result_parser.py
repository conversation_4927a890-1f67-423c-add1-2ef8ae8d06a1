"""
结果文件解析模块
负责读取和解析ZFR数据文件
"""

import os
import re
import logging
import threading
from typing import Dict, Optional, Callable
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

from config.settings import config


class ZFRData:
    """偏心倾斜数据类"""

    def __init__(self, power: float = 0.0, coma_x: float = 0.0, coma_y: float = 0.0,
                 pv: float = 0.0, rms: float = 0.0):
        """
        初始化偏心倾斜数据

        Args:
            power: Power值 (原ZFR 3)
            coma_x: Coma_X值 (原ZFR 6)
            coma_y: Coma_Y值 (原ZFR 7)
            pv: PV值
            rms: RMS值
        """
        self.power = power
        self.coma_x = coma_x
        self.coma_y = coma_y
        self.pv = pv
        self.rms = rms
        self.timestamp = datetime.now()

    def __str__(self) -> str:
        return f"偏心倾斜数据 - Power: {self.power}, Coma_X: {self.coma_x}, Coma_Y: {self.coma_y}, PV: {self.pv}, RMS: {self.rms} (时间: {self.timestamp})"


class FileWatcher(FileSystemEventHandler):
    """文件监控处理器"""
    
    def __init__(self, callback: Callable[[str], None]):
        """
        初始化文件监控器
        
        Args:
            callback: 文件变化时的回调函数
        """
        self.callback = callback
        self.last_modified = 0
    
    def on_modified(self, event):
        """文件修改事件处理"""
        if not event.is_directory:
            # 防止重复触发
            current_time = datetime.now().timestamp()
            if current_time - self.last_modified > 0.5:  # 0.5秒防抖
                self.last_modified = current_time
                self.callback(event.src_path)


class ResultParser:
    """结果文件解析器"""
    
    def __init__(self):
        """初始化解析器"""
        self.file_path = config.get('file_settings.result_file_path', 'resource/result.txt')
        self.auto_reload = config.get('file_settings.auto_reload', True)
        self.observer = None
        self.data_callback: Optional[Callable[[ZFRData], None]] = None
        self._lock = threading.Lock()
        
    def set_file_path(self, file_path: str) -> None:
        """
        设置文件路径
        
        Args:
            file_path: 新的文件路径
        """
        with self._lock:
            self.file_path = file_path
            config.set('file_settings.result_file_path', file_path)
            config.save_config()
            
            # 重启文件监控
            if self.observer and self.observer.is_alive():
                self.stop_watching()
                if self.auto_reload:
                    self.start_watching()
    
    def set_data_callback(self, callback: Callable[[ZFRData], None]) -> None:
        """
        设置数据更新回调函数
        
        Args:
            callback: 数据更新时的回调函数
        """
        self.data_callback = callback
    
    def parse_file(self, file_path: Optional[str] = None) -> Optional[ZFRData]:
        """
        解析结果文件

        Args:
            file_path: 文件路径，如果为None则使用默认路径

        Returns:
            解析得到的偏心倾斜数据，解析失败返回None
        """
        target_file = file_path or self.file_path

        try:
            if not os.path.exists(target_file):
                logging.error(f"文件不存在: {target_file}")
                return None

            zfr_data = {}
            pv_value = 0.0
            rms_value = 0.0

            with open(target_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # 提取PV值
            pv_pattern = r'PV:\s*([-+]?\d*\.?\d+(?:[eE][-+]?\d+)?)'
            pv_match = re.search(pv_pattern, content)
            if pv_match:
                pv_value = float(pv_match.group(1))

            # 提取RMS值
            rms_pattern = r'RMS:\s*([-+]?\d*\.?\d+(?:[eE][-+]?\d+)?)'
            rms_match = re.search(rms_pattern, content)
            if rms_match:
                rms_value = float(rms_match.group(1))

            # 使用正则表达式提取ZFR数据
            zfr_pattern = r'ZFR\s+(\d+)\s+([-+]?\d*\.?\d+(?:[eE][-+]?\d+)?)'
            matches = re.findall(zfr_pattern, content)

            for match in matches:
                zfr_index = int(match[0])
                zfr_value = float(match[1])
                zfr_data[zfr_index] = zfr_value

            # 提取目标ZFR值并重新命名
            power = zfr_data.get(3, 0.0)      # ZFR 3 -> Power
            coma_x = zfr_data.get(6, 0.0)     # ZFR 6 -> Coma_X
            coma_y = zfr_data.get(7, 0.0)     # ZFR 7 -> Coma_Y

            result = ZFRData(power, coma_x, coma_y, pv_value, rms_value)
            logging.info(f"文件解析成功: {result}")

            return result

        except Exception as e:
            logging.error(f"文件解析失败: {e}")
            return None

    def find_latest_txt_file(self, directory: str) -> Optional[str]:
        """
        在指定目录中查找最新的txt文件

        Args:
            directory: 目录路径

        Returns:
            最新txt文件的完整路径，如果没有找到返回None
        """
        try:
            if not os.path.exists(directory) or not os.path.isdir(directory):
                logging.warning(f"目录不存在或不是目录: {directory}")
                return None

            txt_files = []
            for file in os.listdir(directory):
                if file.lower().endswith('.txt'):
                    file_path = os.path.join(directory, file)
                    if os.path.isfile(file_path):
                        # 获取文件修改时间
                        mtime = os.path.getmtime(file_path)
                        txt_files.append((file_path, mtime))

            if not txt_files:
                logging.info(f"目录中没有找到txt文件: {directory}")
                return None

            # 按修改时间排序，获取最新的文件
            txt_files.sort(key=lambda x: x[1], reverse=True)
            latest_file = txt_files[0][0]

            logging.info(f"找到最新txt文件: {os.path.basename(latest_file)}")
            return latest_file

        except Exception as e:
            logging.error(f"查找最新txt文件失败: {e}")
            return None
    
    def start_watching(self) -> None:
        """开始监控文件变化"""
        if not self.auto_reload:
            return
            
        try:
            if self.observer and self.observer.is_alive():
                return
                
            watch_dir = os.path.dirname(os.path.abspath(self.file_path))
            if not os.path.exists(watch_dir):
                logging.warning(f"监控目录不存在: {watch_dir}")
                return
            
            self.observer = Observer()
            event_handler = FileWatcher(self._on_file_changed)
            self.observer.schedule(event_handler, watch_dir, recursive=False)
            self.observer.start()
            
            logging.info(f"开始监控文件变化: {watch_dir}")
            
        except Exception as e:
            logging.error(f"启动文件监控失败: {e}")
    
    def stop_watching(self) -> None:
        """停止监控文件变化"""
        if self.observer and self.observer.is_alive():
            self.observer.stop()
            self.observer.join()
            logging.info("文件监控已停止")
    
    def _on_file_changed(self, file_path: str) -> None:
        """文件变化回调处理"""
        if os.path.basename(file_path) == os.path.basename(self.file_path):
            logging.info(f"检测到文件变化: {file_path}")
            
            # 解析新数据
            new_data = self.parse_file()
            if new_data and self.data_callback:
                self.data_callback(new_data)
    
    def get_latest_data(self) -> Optional[ZFRData]:
        """获取最新的ZFR数据"""
        return self.parse_file()
