#!/usr/bin/env python3
"""
25AutoAssembly - 自动装配控制系统
主程序入口

功能特性:
1. TCP/IP通信控制6轴运动平台
2. 自动读取和解析ZFR数据文件
3. 根据ZFR值计算运动参数
4. 实时监控文件变化
5. 图形化用户界面

作者: AI Assistant
版本: 1.0.0
创建时间: 2024
"""

import sys
import os
import logging
import traceback
from pathlib import Path

# 添加项目根目录和src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(src_path))

from src.config.settings import config


def setup_logging():
    """设置日志系统"""
    try:
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 配置日志格式
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        log_level = getattr(logging, config.get('logging.level', 'INFO').upper())
        
        # 配置根日志记录器
        logging.basicConfig(
            level=log_level,
            format=log_format,
            handlers=[
                logging.FileHandler(
                    log_dir / "system.log", 
                    encoding='utf-8',
                    mode='a'
                ),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        # 设置第三方库的日志级别
        logging.getLogger('watchdog').setLevel(logging.WARNING)
        
        logging.info("=" * 60)
        logging.info("25AutoAssembly 自动装配控制系统启动")
        logging.info("=" * 60)
        
    except Exception as e:
        print(f"日志系统初始化失败: {e}")
        sys.exit(1)


def check_dependencies():
    """检查依赖包"""
    # 检查是否在PyInstaller打包环境中运行
    if getattr(sys, 'frozen', False):
        # 在打包环境中，跳过依赖检查，因为所有依赖都已经打包进去了
        logging.info("检测到PyInstaller打包环境，跳过依赖检查")
        return True

    # 在开发环境中进行正常的依赖检查
    required_packages = [
        ('PyQt5', 'GUI界面'),
        ('watchdog', '文件监控'),
        ('numpy', '数值计算')
    ]

    missing_packages = []

    for package, description in required_packages:
        try:
            if package == 'PyQt5':
                import PyQt5
            elif package == 'watchdog':
                import watchdog
            elif package == 'numpy':
                import numpy
        except ImportError:
            missing_packages.append((package, description))

    if missing_packages:
        logging.error("缺少必要的依赖包:")
        for package, description in missing_packages:
            logging.error(f"  - {package}: {description}")
        logging.error("请运行: pip install -r requirements.txt")
        return False

    logging.info("依赖包检查通过")
    return True


def check_environment():
    """检查运行环境"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 5):
            logging.error(f"Python版本过低: {sys.version}")
            logging.error("需要Python 3.7或更高版本")
            return False
        
        # 检查工作目录
        if not os.access(".", os.W_OK):
            logging.error("当前目录没有写权限")
            return False
        
        # 检查配置文件
        config_file = Path("config/config.json")
        if not config_file.exists():
            logging.warning("配置文件不存在，将创建默认配置")
        
        # 检查资源目录
        resource_dir = Path("resource")
        if not resource_dir.exists():
            logging.warning("资源目录不存在，将创建")
            resource_dir.mkdir(exist_ok=True)
        
        logging.info(f"Python版本: {sys.version}")
        logging.info(f"工作目录: {os.getcwd()}")
        logging.info("运行环境检查通过")
        return True
        
    except Exception as e:
        logging.error(f"环境检查失败: {e}")
        return False


def main():
    """主函数"""
    try:
        # 设置日志
        setup_logging()
        
        # 检查环境
        if not check_environment():
            logging.error("环境检查失败，程序退出")
            sys.exit(1)
        
        # 检查依赖
        if not check_dependencies():
            logging.error("依赖检查失败，程序退出")
            sys.exit(1)
        
        # 显示系统信息
        logging.info(f"系统平台: {sys.platform}")
        logging.info(f"项目路径: {project_root}")
        
        # 创建并运行图形界面
        logging.info("启动图形界面...")

        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtGui import QIcon
        from src.gui.motion_control_ui import MotionControlMainWindow

        app = QApplication(sys.argv)
        app.setStyle('Fusion')

        # {{ AURA: Add - 设置应用程序图标 }}
        # 设置应用程序图标
        icon_path = project_root / 'assets' / 'icons' / 'app_icon.png'
        if icon_path.exists():
            app.setWindowIcon(QIcon(str(icon_path)))
            logging.info(f"✅ 应用程序图标设置成功: {icon_path}")
        else:
            logging.warning(f"⚠️ 图标文件不存在: {icon_path}")

        window = MotionControlMainWindow()
        window.show()

        logging.info("界面已启动")
        sys.exit(app.exec_())
        
    except KeyboardInterrupt:
        logging.info("用户中断程序")
    except Exception as e:
        logging.error(f"程序运行出错: {e}")
        logging.error(f"错误详情:\n{traceback.format_exc()}")
        sys.exit(1)
    finally:
        logging.info("程序结束")


if __name__ == "__main__":
    main()
