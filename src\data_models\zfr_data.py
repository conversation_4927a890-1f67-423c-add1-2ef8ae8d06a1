#!/usr/bin/env python3
"""
25AutoAssembly ZFR数据模型
定义ZFR数据结构和相关操作
"""

from dataclasses import dataclass
from typing import Optional, List, Dict, Any
import time


@dataclass
class ZFRData:
    """
    ZFR数据结构
    包含偏心倾斜分析的关键参数
    """
    power: float = 0.0      # 功率
    coma_x: float = 0.0     # X方向彗差
    coma_y: float = 0.0     # Y方向彗差
    pv: float = 0.0         # Peak-to-Valley值
    rms: float = 0.0        # RMS值
    timestamp: float = 0.0  # 时间戳
    
    def __post_init__(self):
        """初始化后处理"""
        if self.timestamp == 0.0:
            self.timestamp = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'power': self.power,
            'coma_x': self.coma_x,
            'coma_y': self.coma_y,
            'pv': self.pv,
            'rms': self.rms,
            'timestamp': self.timestamp
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ZFRData':
        """从字典创建ZFRData对象"""
        return cls(
            power=data.get('power', 0.0),
            coma_x=data.get('coma_x', 0.0),
            coma_y=data.get('coma_y', 0.0),
            pv=data.get('pv', 0.0),
            rms=data.get('rms', 0.0),
            timestamp=data.get('timestamp', time.time())
        )
    
    def is_valid(self) -> bool:
        """检查数据是否有效"""
        return all(isinstance(getattr(self, field), (int, float)) 
                  for field in ['power', 'coma_x', 'coma_y', 'pv', 'rms'])
    
    def get_magnitude(self) -> float:
        """获取总体偏差量级"""
        return (self.power**2 + self.coma_x**2 + self.coma_y**2)**0.5
    
    def __str__(self) -> str:
        """字符串表示"""
        return (f"ZFRData(power={self.power:.6f}, "
                f"coma_x={self.coma_x:.6f}, coma_y={self.coma_y:.6f}, "
                f"pv={self.pv:.6f}, rms={self.rms:.6f})")


@dataclass
class QueryDeviceData:
    """查询设备数据模型"""
    device_id: str = ""           # 设备ID (device1, device2, device3)
    raw_m1_value: float = 0.0     # 原始M1值
    processed_value: float = 0.0  # 处理后的值 (base_value - raw_m1_value) / division_factor
    timestamp: float = 0.0        # 时间戳

    def __post_init__(self):
        """初始化后处理"""
        if self.timestamp == 0.0:
            self.timestamp = time.time()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'device_id': self.device_id,
            'raw_m1_value': self.raw_m1_value,
            'processed_value': self.processed_value,
            'timestamp': self.timestamp,
            'formatted_time': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.timestamp))
        }


@dataclass
class QueryResultData:
    """查询结果数据模型"""
    devices: List[QueryDeviceData]  # 设备数据列表
    query_timestamp: float = 0.0    # 查询时间戳
    base_value: float = 72902701    # 基准值
    division_factor: float = 10000  # 除数因子

    def __post_init__(self):
        """初始化后处理"""
        if self.query_timestamp == 0.0:
            self.query_timestamp = time.time()

    def get_device_data(self, device_id: str) -> Optional[QueryDeviceData]:
        """根据设备ID获取设备数据"""
        for device in self.devices:
            if device.device_id == device_id:
                return device
        return None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'devices': [device.to_dict() for device in self.devices],
            'query_timestamp': self.query_timestamp,
            'formatted_query_time': time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.query_timestamp)),
            'base_value': self.base_value,
            'division_factor': self.division_factor,
            'device_count': len(self.devices)
        }
