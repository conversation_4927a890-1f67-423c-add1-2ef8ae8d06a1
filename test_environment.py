#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
25AutoAssembly 环境测试脚本

该脚本用于验证25AutoAssembly项目的运行环境是否正确配置，
包括Python版本、核心依赖、Zemax集成等关键组件的检查。

使用方法:
    python test_environment.py

作者: Motion Control Expert
版本: 1.0.0
"""

import sys
import os
import platform
from pathlib import Path


def print_header():
    """打印测试头部信息"""
    print("=" * 60)
    print("🔍 25AutoAssembly 环境配置测试")
    print("=" * 60)
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"Python版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    print("=" * 60)
    print()


def test_python_version():
    """测试Python版本"""
    print("📋 测试Python版本...")
    
    if sys.version_info[:2] == (3, 6):
        print(f"✅ Python版本正确: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        return True
    else:
        print(f"❌ Python版本错误: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        print("   需要Python 3.6.x版本以支持Zemax ZOS-API")
        return False


def test_core_dependencies():
    """测试核心依赖"""
    print("\n📋 测试核心依赖...")
    
    dependencies = [
        ('PyQt5', 'GUI界面框架'),
        ('numpy', '数值计算库'),
        ('matplotlib', '数据可视化库'),
        ('pandas', '数据处理库'),
        ('scipy', '科学计算库'),
        ('watchdog', '文件监控库'),
        ('netifaces', '网络接口库'),
    ]
    
    success_count = 0
    for package, description in dependencies:
        try:
            __import__(package)
            print(f"✅ {package}: {description}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {package}: {description} - 导入失败: {e}")
    
    print(f"\n📊 核心依赖测试结果: {success_count}/{len(dependencies)} 成功")
    return success_count == len(dependencies)


def test_zemax_integration():
    """测试Zemax集成"""
    print("\n📋 测试Zemax集成...")
    
    # 测试pythonnet
    try:
        import clr
        print("✅ pythonnet (.NET集成) 导入成功")
        pythonnet_ok = True
    except ImportError as e:
        print(f"❌ pythonnet导入失败: {e}")
        print("   无法进行Zemax ZOS-API集成")
        return False
    
    # 检查Zemax注册表
    try:
        import winreg
        aKey = winreg.OpenKey(
            winreg.ConnectRegistry(None, winreg.HKEY_CURRENT_USER), 
            r"Software\Zemax", 0, winreg.KEY_READ
        )
        zemaxData = winreg.QueryValueEx(aKey, 'ZemaxRoot')
        zemax_root = zemaxData[0]
        winreg.CloseKey(aKey)
        print(f"✅ Zemax安装路径: {zemax_root}")
        
        # 检查关键DLL文件
        dll_files = [
            r'ZOS-API\Libraries\ZOSAPI_NetHelper.dll',
            r'ZOS-API\Libraries\ZOSAPI.dll',
            r'ZOS-API\Libraries\ZOSAPI_Interfaces.dll'
        ]
        
        dll_ok = True
        for dll_file in dll_files:
            dll_path = os.path.join(zemax_root, dll_file)
            if os.path.exists(dll_path):
                print(f"✅ {os.path.basename(dll_file)} 存在")
            else:
                print(f"⚠️  {os.path.basename(dll_file)} 未找到")
                dll_ok = False
        
        if dll_ok:
            print("✅ Zemax ZOS-API库文件完整")
        else:
            print("⚠️  部分Zemax ZOS-API库文件缺失")
            
        return True
        
    except Exception as e:
        print(f"⚠️  Zemax注册表检查失败: {e}")
        print("   请确保Zemax OpticStudio已正确安装")
        return False


def test_project_structure():
    """测试项目结构"""
    print("\n📋 测试项目结构...")
    
    required_files = [
        'main.py',
        'requirements.txt',
        'config/config.json',
        'gui/motion_control_ui.py',
        'device_communication/tcp_motion_controller.py',
        'data_models/zfr_data.py',
        'file_handler/result_parser.py',
        'utils/logger.py'
    ]
    
    optional_files = [
        'src/optical_analysis/__init__.py',
        'src/optical_analysis/zemax_connector.py',
        'ref/zpy_connection.py',
        'ref/zpy_fftmtf.py',
        'ref/zpy_psf.py'
    ]
    
    # 检查必需文件
    missing_required = []
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (必需)")
            missing_required.append(file_path)
    
    # 检查可选文件
    for file_path in optional_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} (光学分析)")
        else:
            print(f"⚠️  {file_path} (光学分析，可选)")
    
    if missing_required:
        print(f"\n❌ 缺少必需文件: {len(missing_required)} 个")
        return False
    else:
        print(f"\n✅ 项目结构完整")
        return True


def test_network_configuration():
    """测试网络配置"""
    print("\n📋 测试网络配置...")
    
    try:
        import socket
        
        # 测试本地网络接口
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        print(f"✅ 本机IP地址: {local_ip}")
        
        # 测试TCP端口可用性
        test_ports = [8080, 5050, 8899]
        for port in test_ports:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            
            if result == 0:
                print(f"⚠️  端口 {port} 已被占用")
            else:
                print(f"✅ 端口 {port} 可用")
        
        return True
        
    except Exception as e:
        print(f"❌ 网络配置测试失败: {e}")
        return False


def generate_report(results):
    """生成测试报告"""
    print("\n" + "=" * 60)
    print("📊 环境测试报告")
    print("=" * 60)
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20}: {status}")
    
    print("-" * 60)
    print(f"总计: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("\n🎉 环境配置完美！可以正常运行25AutoAssembly项目")
        return True
    elif passed_tests >= total_tests - 1:
        print("\n✅ 环境配置良好，可以运行基本功能")
        print("⚠️  部分高级功能可能受限")
        return True
    else:
        print("\n❌ 环境配置存在问题，建议重新配置")
        print("💡 请运行 setup_environment.bat 重新配置环境")
        return False


def main():
    """主函数"""
    print_header()
    
    # 执行各项测试
    results = {
        "Python版本": test_python_version(),
        "核心依赖": test_core_dependencies(),
        "Zemax集成": test_zemax_integration(),
        "项目结构": test_project_structure(),
        "网络配置": test_network_configuration()
    }
    
    # 生成报告
    success = generate_report(results)
    
    # 提供使用建议
    if success:
        print("\n💡 下一步操作:")
        print("   1. 运行完整程序: python main.py")
        print("   2. 快速启动: python run.py")
        print("   3. 查看项目文档: README.md")
    else:
        print("\n💡 问题解决建议:")
        print("   1. 重新运行环境配置: setup_environment.bat")
        print("   2. 检查Anaconda安装: conda --version")
        print("   3. 检查Zemax安装: 确保有有效许可证")
    
    return success


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
