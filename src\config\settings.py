"""
配置管理模块
负责加载和管理系统配置参数
"""

import json
import os
import logging
from typing import Dict, Any, Optional


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config/config.json"):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self._config: Dict[str, Any] = {}
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self._config = json.load(f)
                logging.info(f"配置文件加载成功: {self.config_file}")
            else:
                logging.warning(f"配置文件不存在: {self.config_file}")
                self._create_default_config()
        except Exception as e:
            logging.error(f"配置文件加载失败: {e}")
            self._create_default_config()
    
    def save_config(self) -> None:
        """保存配置文件"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=4, ensure_ascii=False)
            logging.info(f"配置文件保存成功: {self.config_file}")
        except Exception as e:
            logging.error(f"配置文件保存失败: {e}")
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点分隔的嵌套键如 'device.ip'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键，支持点分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        config = self._config
        
        # 创建嵌套字典结构
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def _create_default_config(self) -> None:
        """创建默认配置"""
        self._config = {
            "device": {
                "ip": "************",
                "port": 8080,
                "timeout": 5.0,
                "retry_count": 3
            },
            "file_settings": {
                "result_file_path": "resource/result.txt",
                "auto_reload": True,
                "file_check_interval": 1.0
            },
            "motion_control": {
                "translation_rotation_ratio": 4,
                "motion_level": 0.001,
                "enable_safety_limits": True
            }
        }
        self.save_config()


# 全局配置实例
config = ConfigManager()
