@echo off
chcp 936 > nul 2>&1
setlocal EnableDelayedExpansion

echo ======================================================
echo OpticalRealSim 优化打包脚本 (基于最佳实践)
echo ======================================================
echo.

REM 第1步: 设置环境
echo [1/7] 设置环境...

REM 获取项目根目录
pushd "%~dp0"
set PROJECT_ROOT=%CD%
popd
echo 项目根目录: %PROJECT_ROOT%

REM 激活conda环境
echo 激活conda环境: pyzemax
call conda activate pyzemax
if !ERRORLEVEL! NEQ 0 (
    echo 错误: 无法激活conda环境 'pyzemax'
    echo 请确保环境存在并正确配置
    goto :error
)

echo 环境激活成功
echo.

REM 第2步: 验证Python环境
echo [2/7] 验证Python环境...

REM 检查Python版本
for /f "tokens=*" %%i in ('python --version') do set PYTHON_VERSION=%%i
echo Python版本: %PYTHON_VERSION%

REM 检查关键包
python -c "import PyQt5; print('PyQt5版本:', PyQt5.Qt.PYQT_VERSION_STR)" 2>nul
if !ERRORLEVEL! NEQ 0 (
    echo 错误: PyQt5未正确安装
    goto :error
)

python -c "import pythonnet; print('PythonNET安装正常')" 2>nul
if !ERRORLEVEL! NEQ 0 (
    echo 错误: pythonnet未正确安装
    goto :error
)

echo 环境验证完成
echo.

REM 第3步: 清理旧的构建
echo [3/7] 清理旧的构建文件...
if exist "%PROJECT_ROOT%\build" (
    echo 删除build目录...
    rmdir /s /q "%PROJECT_ROOT%\build" 2>nul
)

if exist "%PROJECT_ROOT%\dist" (
    echo 删除dist目录...
    rmdir /s /q "%PROJECT_ROOT%\dist" 2>nul
)

echo 清理完成
echo.

REM 第4步: 验证必需文件
echo [4/7] 验证必需文件...

set MAIN_FILE=%PROJECT_ROOT%\src\main.py
set SPEC_FILE=%PROJECT_ROOT%\OpticalRealSim.spec
set SRC_DIR=%PROJECT_ROOT%\src
set ZOS_DIR=%PROJECT_ROOT%\ZOS

if not exist "%MAIN_FILE%" (
    echo 错误: 主文件未找到: %MAIN_FILE%
    goto :error
)

if not exist "%SPEC_FILE%" (
    echo 错误: Spec文件未找到: %SPEC_FILE%
    goto :error
)

if not exist "%SRC_DIR%" (
    echo 错误: src目录未找到: %SRC_DIR%
    goto :error
)

if not exist "%ZOS_DIR%" (
    echo 警告: ZOS目录未找到: %ZOS_DIR%
    echo 程序可能无法正常工作
) else (
    echo ZOS目录找到: %ZOS_DIR%
)

echo 文件验证完成
echo.

REM 第5步: 使用PyInstaller打包
echo [5/7] 使用PyInstaller打包...
echo 使用spec文件: %SPEC_FILE%

cd /d "%PROJECT_ROOT%"
pyinstaller "%SPEC_FILE%" --log-level=INFO

if !ERRORLEVEL! NEQ 0 (
    echo 错误: PyInstaller打包失败
    goto :error
)

echo PyInstaller打包成功
echo.

REM 第6步: 修复关键DLL依赖
echo [6/7] 修复关键DLL依赖...

REM 动态获取conda环境路径
for /f "tokens=*" %%i in ('python -c "import sys; print(sys.prefix)"') do set CONDA_ENV_PATH=%%i
echo Conda环境路径: %CONDA_ENV_PATH%

set SOURCE_DLL=%CONDA_ENV_PATH%\Lib\site-packages\Python.Runtime.dll
set TARGET_DIR=%PROJECT_ROOT%\dist\OpticalRealSim
echo 源DLL路径: %SOURCE_DLL%
echo 目标目录: %TARGET_DIR%

REM 检查源DLL是否存在
if not exist "%SOURCE_DLL%" (
    echo 错误: 源Python.Runtime.dll未找到: %SOURCE_DLL%
    echo 请验证conda环境中的pythonnet安装
    goto :error
)

REM 检查目标目录是否存在
if not exist "%TARGET_DIR%" (
    echo 错误: 目标目录未找到: %TARGET_DIR%
    echo PyInstaller构建可能失败
    goto :error
)

REM 复制Python.Runtime.dll到dist目录
echo 复制Python.Runtime.dll...
copy "%SOURCE_DLL%" "%TARGET_DIR%\" > nul
if !ERRORLEVEL! NEQ 0 (
    echo 错误: 复制Python.Runtime.dll失败
    goto :error
)

echo Python.Runtime.dll复制成功
echo.

REM 第7步: 最终验证和测试
echo [7/7] 最终验证和测试...

set FINAL_EXE=%TARGET_DIR%\OpticalRealSim.exe
if not exist "%FINAL_EXE%" (
    echo 错误: 最终可执行文件未找到: %FINAL_EXE%
    goto :error
)

set FINAL_DLL=%TARGET_DIR%\Python.Runtime.dll
if not exist "%FINAL_DLL%" (
    echo 错误: Python.Runtime.dll在最终构建中未找到: %FINAL_DLL%
    goto :error
)

REM 检查Zemax DLL文件
set ZEMAX_DLL1=%TARGET_DIR%\ZOSAPI.dll
set ZEMAX_DLL2=%TARGET_DIR%\ZOSAPI_Interfaces.dll
set ZEMAX_DLL3=%TARGET_DIR%\ZOSAPI_NetHelper.dll

if exist "%ZEMAX_DLL1%" (
    echo ✓ ZOSAPI.dll 已包含
) else (
    echo 警告: ZOSAPI.dll 未找到
)

if exist "%ZEMAX_DLL2%" (
    echo ✓ ZOSAPI_Interfaces.dll 已包含
) else (
    echo 警告: ZOSAPI_Interfaces.dll 未找到
)

if exist "%ZEMAX_DLL3%" (
    echo ✓ ZOSAPI_NetHelper.dll 已包含
) else (
    echo 警告: ZOSAPI_NetHelper.dll 未找到
)

REM 快速启动测试
echo 执行快速启动测试...
start /wait "" "%FINAL_EXE%" & timeout /t 3 /nobreak > nul
echo 启动测试完成

echo 最终验证完成
echo.

echo ======================================================
echo 打包成功完成!
echo ======================================================
echo.
echo 输出位置: %TARGET_DIR%
echo 可执行文件: %FINAL_EXE%
echo.
echo 修复内容:
echo - 移除了有问题的win32comext.shell.demos.explorer_browser导入
echo - 优化了隐藏导入配置
echo - 确保Python.Runtime.dll正确包含
echo - 包含所有必要的Zemax DLL文件
echo.

goto :end

:error
echo.
echo ======================================================
echo 打包失败!
echo ======================================================
echo.
echo 请检查上面的错误信息并修复问题。
echo 常见解决方案:
echo 1. 确保conda环境 'pyzemax' 存在并正确配置
echo 2. 验证所有必需包已安装 (PyQt5, pythonnet, pyinstaller)
echo 3. 检查所有源文件在正确位置存在
echo 4. 确保Zemax DLL文件在ZOS目录中可用
echo.
pause
exit /b 1

:end
echo 构建过程完成。按任意键继续...
pause
exit /b 0
