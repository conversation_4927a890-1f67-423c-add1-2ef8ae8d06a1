#!/usr/bin/env python3
"""
VRML 2.0 查看器 - 使用VTK原生支持
基于命令行输入文件名，使用VTK渲染VRML 2.0模型
"""

import sys
import os
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# VTK导入和检查
try:
    import vtk
    print(f"VTK版本: {vtk.vtkVersion.GetVTKVersion()}")
    VTK_AVAILABLE = True
except ImportError as e:
    print(f"VTK导入失败: {e}")
    print("请安装VTK: pip install vtk")
    VTK_AVAILABLE = False
    sys.exit(1)

# Qt导入和检查
try:
    from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
    from PyQt5.QtCore import Qt
    QT_AVAILABLE = True
except ImportError:
    try:
        from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
        from PyQt6.QtCore import Qt
        QT_AVAILABLE = True
    except ImportError:
        print("PyQt5/PyQt6导入失败，将使用VTK原生窗口")
        QT_AVAILABLE = False

# VTK-Qt集成
QVTK_AVAILABLE = False
if QT_AVAILABLE:
    try:
        from vtk.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
        QVTK_AVAILABLE = True
        print("VTK-Qt集成可用")
    except ImportError:
        try:
            from vtkmodules.qt.QVTKRenderWindowInteractor import QVTKRenderWindowInteractor
            QVTK_AVAILABLE = True
            print("VTK-Qt集成可用 (vtkmodules)")
        except ImportError:
            print("VTK-Qt集成不可用，将使用VTK原生窗口")


class VRML2Viewer:
    """VRML 2.0 查看器类"""
    
    def __init__(self):
        """初始化查看器"""
        self.renderer = None
        self.render_window = None
        self.interactor = None
        self.importer = None
        
    def load_vrml2_file(self, filename: str) -> bool:
        """
        使用VTK原生支持加载VRML 2.0文件
        
        Args:
            filename: VRML 2.0文件路径
            
        Returns:
            bool: 加载成功返回True
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(filename):
                logger.error(f"文件不存在: {filename}")
                return False
            
            # 检查文件扩展名
            if not filename.lower().endswith(('.wrl', '.vrml')):
                logger.warning(f"文件扩展名不是VRML格式: {filename}")
            
            logger.info(f"开始加载VRML 2.0文件: {filename}")
            
            # 创建VRML导入器
            self.importer = vtk.vtkVRMLImporter()
            self.importer.SetFileName(filename)
            
            # 创建渲染器
            self.renderer = vtk.vtkRenderer()
            self.renderer.SetBackground(0.1, 0.1, 0.2)  # 深蓝色背景
            
            # 创建渲染窗口
            self.render_window = vtk.vtkRenderWindow()
            self.render_window.AddRenderer(self.renderer)
            self.render_window.SetSize(800, 600)
            self.render_window.SetWindowName(f"VRML 2.0 Viewer - {os.path.basename(filename)}")
            
            # 设置导入器的渲染窗口
            self.importer.SetRenderWindow(self.render_window)
            
            # 执行导入
            logger.info("执行VRML导入...")
            self.importer.Read()
            
            # 获取导入的Actor数量
            actors = self.renderer.GetActors()
            actor_count = actors.GetNumberOfItems()
            logger.info(f"成功导入 {actor_count} 个Actor")
            
            if actor_count == 0:
                logger.warning("未导入任何3D对象，可能文件格式不正确或为空")
                return False
            
            # 设置相机以查看所有对象
            self.renderer.ResetCamera()
            
            # 创建交互器
            self.interactor = vtk.vtkRenderWindowInteractor()
            self.interactor.SetRenderWindow(self.render_window)
            
            # 设置交互样式
            style = vtk.vtkInteractorStyleTrackballCamera()
            self.interactor.SetInteractorStyle(style)
            
            logger.info("VRML 2.0文件加载成功")
            return True
            
        except Exception as e:
            logger.error(f"加载VRML 2.0文件失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def show(self):
        """显示3D模型"""
        if not self.render_window or not self.interactor:
            logger.error("渲染窗口或交互器未初始化")
            return
        
        try:
            # 渲染
            self.render_window.Render()
            
            # 开始交互
            logger.info("开始3D交互显示...")
            logger.info("操作说明:")
            logger.info("  - 鼠标左键拖拽: 旋转")
            logger.info("  - 鼠标右键拖拽: 缩放")
            logger.info("  - 鼠标中键拖拽: 平移")
            logger.info("  - 按 'q' 或关闭窗口: 退出")
            
            self.interactor.Start()
            
        except Exception as e:
            logger.error(f"显示失败: {e}")
            import traceback
            traceback.print_exc()
    
    def cleanup(self):
        """清理资源"""
        if self.interactor:
            self.interactor.TerminateApp()
        if self.render_window:
            self.render_window.Finalize()


class VRML2ViewerQt(QMainWindow):
    """基于Qt的VRML 2.0查看器"""
    
    def __init__(self):
        super().__init__()
        self.viewer = VRML2Viewer()
        self.vtk_widget = None
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("VRML 2.0 Viewer (Qt)")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout()
        central_widget.setLayout(layout)
        
        # 创建VTK widget
        if QVTK_AVAILABLE:
            self.vtk_widget = QVTKRenderWindowInteractor(central_widget)
            layout.addWidget(self.vtk_widget)
            
            # 设置VTK渲染窗口
            self.viewer.render_window = self.vtk_widget.GetRenderWindow()
        else:
            logger.error("VTK-Qt集成不可用")
    
    def load_and_show(self, filename: str):
        """加载并显示VRML文件"""
        if not QVTK_AVAILABLE:
            logger.error("无法使用Qt界面，VTK-Qt集成不可用")
            return False
        
        # 创建渲染器
        self.viewer.renderer = vtk.vtkRenderer()
        self.viewer.renderer.SetBackground(0.1, 0.1, 0.2)
        self.viewer.render_window.AddRenderer(self.viewer.renderer)
        
        # 加载VRML文件
        success = self.viewer.load_vrml2_file(filename)
        if success:
            # 设置交互器
            self.viewer.interactor = self.vtk_widget.GetRenderWindow().GetInteractor()
            style = vtk.vtkInteractorStyleTrackballCamera()
            self.viewer.interactor.SetInteractorStyle(style)
            
            # 渲染
            self.viewer.render_window.Render()
            self.show()
            return True
        
        return False


def get_input_filename() -> str:
    """获取用户输入的文件名"""
    print("\n" + "="*60)
    print("VRML 2.0 查看器")
    print("="*60)
    print("支持的文件格式: .wrl, .vrml (VRML 2.0)")
    print("操作说明:")
    print("  - 输入文件路径（支持相对路径和绝对路径）")
    print("  - 输入 'quit' 或 'exit' 退出程序")
    print("="*60)
    
    while True:
        try:
            filename = input("\n请输入VRML 2.0文件路径: ").strip()
            
            # 检查退出命令
            if filename.lower() in ['quit', 'exit', 'q']:
                print("退出程序")
                return None
            
            # 检查空输入
            if not filename:
                print("请输入有效的文件路径")
                continue
            
            # 移除引号（如果有）
            filename = filename.strip('"\'')
            
            # 检查文件是否存在
            if not os.path.exists(filename):
                print(f"文件不存在: {filename}")
                print("请检查文件路径是否正确")
                continue
            
            return filename
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            return None
        except Exception as e:
            print(f"输入错误: {e}")
            continue


def main():
    """主函数"""
    if not VTK_AVAILABLE:
        return 1
    
    try:
        # 获取文件名
        filename = get_input_filename()
        if not filename:
            return 0
        
        print(f"\n开始加载文件: {filename}")
        
        # 选择查看器类型
        if QT_AVAILABLE and QVTK_AVAILABLE:
            print("使用Qt界面...")
            app = QApplication(sys.argv)
            
            viewer = VRML2ViewerQt()
            success = viewer.load_and_show(filename)
            
            if success:
                sys.exit(app.exec_())
            else:
                print("加载失败，尝试使用VTK原生窗口...")
        
        # 使用VTK原生窗口
        print("使用VTK原生窗口...")
        viewer = VRML2Viewer()
        success = viewer.load_vrml2_file(filename)
        
        if success:
            viewer.show()
        else:
            print("文件加载失败")
            return 1
        
        # 清理
        viewer.cleanup()
        return 0
        
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
